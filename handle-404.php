<?php
/**
 * 404 Handler - Alternative approach for handling 404 errors
 * This file can be included at the top of any page that might not exist
 * or used as a fallback when .htaccess ErrorDocument doesn't work
 */

// Check if the requested file actually exists
$requestedFile = $_SERVER['SCRIPT_FILENAME'] ?? '';
$requestUri = $_SERVER['REQUEST_URI'] ?? '';

// If this file is being accessed directly or the requested file doesn't exist
if (basename($_SERVER['SCRIPT_NAME']) === 'handle-404.php' || 
    (!empty($requestedFile) && !file_exists($requestedFile))) {
    
    // Set 404 status code
    http_response_code(404);
    
    // Include the 404 page
    include_once '404.php';
    exit;
}
?>
