<?php
/**
 * Fix Message Categories
 * This script will fix the message categorization issues
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'classes/models.php';

// Include security middleware for web access
require_once 'includes/security_middleware.php';
requireRole('admin');

$results = [];
$messageModel = new Message();

try {
    $db = DatabaseConfig::getConnection();
    
    // Step 1: Check if message_category column exists
    $results[] = "=== CHECKING DATABASE STRUCTURE ===";
    
    $stmt = $db->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
    $stmt->execute();
    $categoryColumnExists = $stmt->fetch();
    
    if (!$categoryColumnExists) {
        $results[] = "❌ message_category column does not exist";
        $results[] = "Adding message_category column...";
        
        try {
            $db->exec("ALTER TABLE messages ADD COLUMN message_category ENUM('contact', 'quote', 'general') DEFAULT 'general' AFTER message_type");
            $results[] = "✅ message_category column added successfully";
        } catch (Exception $e) {
            $results[] = "❌ Failed to add message_category column: " . $e->getMessage();
            throw $e;
        }
    } else {
        $results[] = "✅ message_category column exists";
    }
    
    // Step 2: Check current message categories
    $results[] = "\n=== ANALYZING CURRENT MESSAGES ===";
    
    $stmt = $db->prepare("SELECT message_category, COUNT(*) as count FROM messages GROUP BY message_category");
    $stmt->execute();
    $categoryCounts = $stmt->fetchAll();
    
    $results[] = "Current message distribution:";
    foreach ($categoryCounts as $count) {
        $category = $count['message_category'] ?: 'NULL';
        $results[] = "  - {$category}: {$count['count']} messages";
    }
    
    // Step 3: Update message categories based on subject
    $results[] = "\n=== UPDATING MESSAGE CATEGORIES ===";
    
    // Update contact messages
    $stmt = $db->prepare("UPDATE messages SET message_category = 'contact' WHERE (subject LIKE '%Contact Form%' OR subject LIKE '%contact%' OR subject LIKE '%Contact%' OR subject LIKE '%inquiry%' OR subject LIKE '%Inquiry%') AND (message_category = 'general' OR message_category IS NULL)");
    $stmt->execute();
    $contactUpdated = $stmt->rowCount();
    $results[] = "✅ Updated {$contactUpdated} messages to 'contact' category";
    
    // Update quote messages
    $stmt = $db->prepare("UPDATE messages SET message_category = 'quote' WHERE (subject LIKE '%Quote Request%' OR subject LIKE '%quote%' OR subject LIKE '%Quote%' OR subject LIKE '%booking%' OR subject LIKE '%Booking%' OR subject LIKE '%travel%' OR subject LIKE '%Travel%' OR subject LIKE '%tour%' OR subject LIKE '%Tour%') AND (message_category = 'general' OR message_category IS NULL)");
    $stmt->execute();
    $quoteUpdated = $stmt->rowCount();
    $results[] = "✅ Updated {$quoteUpdated} messages to 'quote' category";
    
    // Step 4: Show updated distribution
    $results[] = "\n=== UPDATED MESSAGE DISTRIBUTION ===";
    
    $stmt = $db->prepare("SELECT message_category, COUNT(*) as count FROM messages GROUP BY message_category");
    $stmt->execute();
    $newCategoryCounts = $stmt->fetchAll();
    
    foreach ($newCategoryCounts as $count) {
        $category = $count['message_category'] ?: 'NULL';
        $results[] = "  - {$category}: {$count['count']} messages";
    }
    
    // Step 5: Show sample messages for verification
    $results[] = "\n=== SAMPLE MESSAGES BY CATEGORY ===";
    
    $categories = ['contact', 'quote', 'general'];
    foreach ($categories as $cat) {
        $stmt = $db->prepare("SELECT sender_name, subject FROM messages WHERE message_category = :category ORDER BY received_at DESC LIMIT 3");
        $stmt->bindParam(':category', $cat);
        $stmt->execute();
        $samples = $stmt->fetchAll();
        
        $results[] = "\n{$cat} messages:";
        if ($samples) {
            foreach ($samples as $sample) {
                $results[] = "  - From: {$sample['sender_name']} | Subject: {$sample['subject']}";
            }
        } else {
            $results[] = "  - No messages in this category";
        }
    }
    
    // Step 6: Fix contact form to use categories
    $results[] = "\n=== CHECKING CONTACT FORM INTEGRATION ===";
    
    $contactFile = '../contact.php';
    if (file_exists($contactFile)) {
        $contactContent = file_get_contents($contactFile);
        if (strpos($contactContent, "'message_category' => 'contact'") !== false) {
            $results[] = "✅ Contact form already uses message categories";
        } else {
            $results[] = "⚠️ Contact form needs to be updated to use message categories";
        }
    }
    
    $results[] = "\n=== SUMMARY ===";
    $results[] = "✅ Database structure updated";
    $results[] = "✅ Message categories fixed";
    $results[] = "✅ {$contactUpdated} contact messages categorized";
    $results[] = "✅ {$quoteUpdated} quote messages categorized";
    $results[] = "\nThe message categorization should now work correctly in the admin panel!";
    
} catch (Exception $e) {
    $results[] = "❌ Error: " . $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Message Categories - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-4xl mx-auto px-4">
            
            <!-- Header -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h1 class="text-xl font-bold text-gray-900">Fix Message Categories</h1>
                    <p class="text-sm text-gray-600">Fixing message categorization issues in the admin panel</p>
                </div>
            </div>

            <!-- Results -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Fix Results</h2>
                </div>
                
                <div class="p-6">
                    <div class="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
                        <?php foreach ($results as $result): ?>
                            <?php echo htmlspecialchars($result) . "\n"; ?>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="font-semibold text-blue-900 mb-3">
                    <i class="fas fa-rocket mr-2"></i>Next Steps
                </h3>
                
                <div class="space-y-2 text-blue-800">
                    <p>1. <strong>Check Messages Page:</strong> Go to the messages admin page to see if categories are now displayed correctly</p>
                    <p>2. <strong>Test Filtering:</strong> Try filtering by "Contact" and "Quote" to see if it works</p>
                    <p>3. <strong>Submit Test Messages:</strong> Submit a test contact form and quote request to verify new messages get proper categories</p>
                    <p>4. <strong>Update Forms:</strong> Make sure contact and quote forms are creating messages with proper categories</p>
                </div>
                
                <div class="mt-4 flex space-x-4">
                    <a href="messages.php" 
                       class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-300">
                        <i class="fas fa-envelope mr-2"></i>View Messages
                    </a>
                    <a href="setup-message-categories.php" 
                       class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition duration-300">
                        <i class="fas fa-cog mr-2"></i>Full Setup
                    </a>
                    <button onclick="location.reload()" 
                            class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition duration-300">
                        <i class="fas fa-redo mr-2"></i>Run Again
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
