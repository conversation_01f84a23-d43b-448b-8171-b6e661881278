# Meleva Tours and Travel - Root .htaccess

# Enable URL rewriting
RewriteEngine On

# Custom Error Pages
ErrorDocument 404 /meleva/404.php

# Comprehensive 404 handling for missing files
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/meleva/404\.php$
RewriteCond %{REQUEST_URI} !^/meleva/admin-dashboard/
RewriteCond %{REQUEST_URI} !^/meleva/uploads/
RewriteCond %{REQUEST_URI} !^/meleva/images/
RewriteCond %{REQUEST_URI} !^/meleva/js/
RewriteCond %{REQUEST_URI} !\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|pdf)$
RewriteRule ^(.*)$ 404.php [L]

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# Protect sensitive files
<Files "*.sql">
    Order allow,deny
    Deny from all
</Files>

<Files "*.log">
    Order allow,deny
    Deny from all
</Files>

<Files "*.bak">
    Order allow,deny
    Deny from all
</Files>

<Files "*~">
    Order allow,deny
    Deny from all
</Files>

# Allow access to documentation files
<Files "WEBSITE_DOCUMENTATION.md">
    Order allow,deny
    Allow from all
</Files>

<Files "ADMIN_DASHBOARD_DOCUMENTATION.md">
    Order allow,deny
    Allow from all
</Files>

<Files "PAYMENT_SYSTEM_DOCUMENTATION.md">
    Order allow,deny
    Allow from all
</Files>

# Protect other .md files
<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

# Disable directory browsing
Options -Indexes

# Prevent access to PHP files in uploads directory
<Files "uploads/*.php">
    Order allow,deny
    Deny from all
</Files>

# Enable compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Set cache headers for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>
