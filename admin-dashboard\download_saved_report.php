<?php
require_once 'config/config.php';
require_once 'classes/models.php';

// Require authentication
Auth::requireLogin();

$reportId = $_POST['report_id'] ?? null;

if (!$reportId) {
    die('Report ID is required');
}

// Get report details
$reportModel = new Report();
$report = $reportModel->findById($reportId);

if (!$report) {
    die('Report not found');
}

$reportData = json_decode($report['report_data'], true);

// Generate filename
$filename = str_replace(' ', '_', $report['report_name']) . '_' . date('Y-m-d', strtotime($report['generated_at']));

// Create text report content
$content = "MELEVA TOURS AND TRAVEL - SAVED REPORT\n";
$content .= "=" . str_repeat("=", 50) . "\n\n";
$content .= "Report Name: " . $report['report_name'] . "\n";
$content .= "Report Type: " . ucfirst(str_replace('_', ' ', $reportData['type'] ?? 'Unknown')) . "\n";
$content .= "Generated: " . date('F j, Y g:i A', strtotime($report['generated_at'])) . "\n";
$content .= "Date Range: " . ($reportData['date_range'] ?? 'N/A') . " days\n\n";

$content .= "REPORT DATA\n";
$content .= "-" . str_repeat("-", 20) . "\n";

if (isset($reportData['data']) && is_array($reportData['data'])) {
    foreach ($reportData['data'] as $key => $value) {
        if (is_numeric($value)) {
            $displayValue = is_float($value) ? number_format($value, 2) : $value;
            if (strpos($key, 'amount') !== false || strpos($key, 'revenue') !== false) {
                $displayValue = '$' . $displayValue;
            }
        } else {
            $displayValue = $value;
        }
        $content .= ucfirst(str_replace('_', ' ', $key)) . ": " . $displayValue . "\n";
    }
} else {
    $content .= "No data available\n";
}

$content .= "\n" . str_repeat("=", 60) . "\n";
$content .= "Report downloaded from Meleva Tours and Travel Admin Dashboard\n";

// Set headers for text file download
header('Content-Type: text/plain; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '.txt"');
header('Content-Length: ' . strlen($content));

echo $content;
exit;
?>
