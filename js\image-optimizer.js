/**
 * Meleva Tours - Image Optimization
 * Lightweight image optimization for frontend performance
 */

// Image optimization functionality
class MelevaImageOptimizer {
    constructor() {
        this.lazyImages = [];
        this.imageObserver = null;
        this.webpSupported = false;
        
        this.init();
    }

    /**
     * Initialize image optimization
     */
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.checkWebPSupport();
            this.initializeImageOptimization();
        });
    }

    /**
     * Check WebP support
     */
    checkWebPSupport() {
        const webP = new Image();
        webP.onload = webP.onerror = () => {
            this.webpSupported = (webP.height === 2);
        };
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    }

    /**
     * Initialize image optimization features
     */
    initializeImageOptimization() {
        this.setupLazyLoading();
        this.optimizeExistingImages();
        this.preloadCriticalImages();
    }

    /**
     * Setup lazy loading for images
     */
    setupLazyLoading() {
        // Find all images with data-src attribute (lazy loading)
        this.lazyImages = document.querySelectorAll('img[data-src]');
        
        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.lazyImages.forEach(img => {
                this.imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            this.lazyImages.forEach(img => {
                this.loadImage(img);
            });
        }
    }

    /**
     * Load individual image with optimization
     */
    loadImage(img) {
        const src = img.dataset.src;
        const srcset = img.dataset.srcset;
        
        if (!src) return;

        // Create a new image to test loading
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // Image loaded successfully, update the actual img element
            img.src = src;
            if (srcset) {
                img.srcset = srcset;
            }
            
            // Add loaded class for fade-in effect
            img.classList.add('image-loaded');
            
            // Remove data attributes to prevent reprocessing
            delete img.dataset.src;
            delete img.dataset.srcset;
        };
        
        imageLoader.onerror = () => {
            // Handle image load error
            img.classList.add('image-error');
            console.warn('Failed to load image:', src);
        };
        
        // Start loading
        imageLoader.src = src;
    }

    /**
     * Optimize existing images that are already loaded
     */
    optimizeExistingImages() {
        const images = document.querySelectorAll('img:not([data-src])');
        
        images.forEach(img => {
            // Add loading optimization
            if (!img.loading) {
                img.loading = 'lazy';
            }
            
            // Add decode hint for better performance
            img.decoding = 'async';
            
            // Add error handling
            img.onerror = () => {
                img.classList.add('image-error');
                console.warn('Failed to load image:', img.src);
            };
        });
    }

    /**
     * Preload critical images (above the fold)
     */
    preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('.critical-image, .hero-image, .logo');
        
        criticalImages.forEach(img => {
            if (img.dataset.src) {
                // For lazy loaded critical images, load them immediately
                this.loadImage(img);
            } else if (img.src) {
                // For already loaded images, ensure they're prioritized
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = img.src;
                document.head.appendChild(link);
            }
        });
    }

    /**
     * Create optimized image element
     */
    createOptimizedImage(src, alt, className = '', lazy = true) {
        const img = document.createElement('img');
        
        if (lazy) {
            // Set up lazy loading
            img.dataset.src = src;
            img.alt = alt;
            img.className = className + ' lazy-image';
            img.loading = 'lazy';
            img.decoding = 'async';
            
            // Add placeholder
            img.src = 'data:image/svg+xml;base64,' + btoa(`
                <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                    <rect width="100%" height="100%" fill="#f3f4f6"/>
                    <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af">Loading...</text>
                </svg>
            `);
        } else {
            // Load immediately for critical images
            img.src = src;
            img.alt = alt;
            img.className = className + ' critical-image';
            img.loading = 'eager';
            img.decoding = 'sync';
        }
        
        return img;
    }
}

// Image compression utilities
class ImageCompressor {
    /**
     * Compress image file on client side
     */
    static compressImage(file, maxWidth = 1200, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = img;
                
                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }
                
                canvas.width = width;
                canvas.height = height;
                
                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);
                
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }

    /**
     * Generate image thumbnail
     */
    static generateThumbnail(file, size = 150) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                canvas.width = size;
                canvas.height = size;
                
                // Calculate crop dimensions for square thumbnail
                const minDimension = Math.min(img.width, img.height);
                const x = (img.width - minDimension) / 2;
                const y = (img.height - minDimension) / 2;
                
                ctx.drawImage(img, x, y, minDimension, minDimension, 0, 0, size, size);
                
                canvas.toBlob(resolve, 'image/jpeg', 0.8);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
}

// Initialize image optimization
const melevaImageOptimizer = new MelevaImageOptimizer();
