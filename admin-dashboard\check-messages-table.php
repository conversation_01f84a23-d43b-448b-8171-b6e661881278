<?php
// Quick diagnostic script to check messages table structure
require_once 'config/config.php';

try {
    $db = DatabaseConfig::getConnection();
    
    echo "<h2>Messages Table Structure</h2>";
    
    // Check table structure
    $stmt = $db->prepare("DESCRIBE messages");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if we have any messages
    $stmt = $db->prepare("SELECT COUNT(*) as count FROM messages");
    $stmt->execute();
    $count = $stmt->fetch();
    
    echo "<p><strong>Total messages:</strong> " . $count['count'] . "</p>";
    
    // Show sample messages
    if ($count['count'] > 0) {
        echo "<h3>Sample Messages</h3>";
        $stmt = $db->prepare("SELECT * FROM messages ORDER BY received_at DESC LIMIT 3");
        $stmt->execute();
        $messages = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
        echo "<tr>";
        foreach (array_keys($messages[0]) as $key) {
            if (!is_numeric($key)) {
                echo "<th>" . htmlspecialchars($key) . "</th>";
            }
        }
        echo "</tr>";
        
        foreach ($messages as $message) {
            echo "<tr>";
            foreach ($message as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<td>" . htmlspecialchars(substr($value, 0, 50)) . (strlen($value) > 50 ? '...' : '') . "</td>";
                }
            }
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
