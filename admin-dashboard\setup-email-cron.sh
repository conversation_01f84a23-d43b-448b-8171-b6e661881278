#!/bin/bash

# Email Fetcher Cron Job Setup Script
# This script helps set up automatic email fetching

echo "=== Meleva Tours Email Fetcher Cron Setup ==="
echo ""

# Check if we're running as root or with sudo
if [ "$EUID" -eq 0 ]; then
    echo "Warning: Running as root. Consider running as the web server user instead."
fi

# Get the current directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EMAIL_FETCHER="$SCRIPT_DIR/email-fetcher.php"

echo "Script directory: $SCRIPT_DIR"
echo "Email fetcher script: $EMAIL_FETCHER"
echo ""

# Check if email-fetcher.php exists
if [ ! -f "$EMAIL_FETCHER" ]; then
    echo "Error: email-fetcher.php not found in $SCRIPT_DIR"
    exit 1
fi

# Check if PHP is available
if ! command -v php &> /dev/null; then
    echo "Error: PHP is not installed or not in PATH"
    exit 1
fi

PHP_VERSION=$(php -v | head -n 1)
echo "PHP version: $PHP_VERSION"
echo ""

# Check if IMAP extension is available
if php -m | grep -q "imap"; then
    echo "✓ IMAP extension is available"
else
    echo "✗ IMAP extension is not available"
    echo "Please install IMAP extension first:"
    echo "  Ubuntu/Debian: sudo apt-get install php-imap"
    echo "  CentOS/RHEL: sudo yum install php-imap"
    echo "  XAMPP: Uncomment ;extension=imap in php.ini"
    exit 1
fi

echo ""

# Test the email fetcher script
echo "Testing email fetcher script..."
php "$EMAIL_FETCHER" --test 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ Email fetcher script is working"
else
    echo "✗ Email fetcher script has issues"
    echo "Please check the script configuration and try again"
    exit 1
fi

echo ""

# Ask user for cron frequency
echo "How often would you like to fetch emails?"
echo "1) Every 5 minutes (recommended for active businesses)"
echo "2) Every 15 minutes (good balance)"
echo "3) Every 30 minutes (less frequent)"
echo "4) Every hour (minimal checking)"
echo "5) Custom interval"
echo ""

read -p "Choose an option (1-5): " choice

case $choice in
    1)
        CRON_SCHEDULE="*/5 * * * *"
        DESCRIPTION="every 5 minutes"
        ;;
    2)
        CRON_SCHEDULE="*/15 * * * *"
        DESCRIPTION="every 15 minutes"
        ;;
    3)
        CRON_SCHEDULE="*/30 * * * *"
        DESCRIPTION="every 30 minutes"
        ;;
    4)
        CRON_SCHEDULE="0 * * * *"
        DESCRIPTION="every hour"
        ;;
    5)
        echo ""
        echo "Enter custom cron schedule (e.g., '*/10 * * * *' for every 10 minutes):"
        read -p "Cron schedule: " CRON_SCHEDULE
        DESCRIPTION="custom schedule: $CRON_SCHEDULE"
        ;;
    *)
        echo "Invalid choice. Using default: every 15 minutes"
        CRON_SCHEDULE="*/15 * * * *"
        DESCRIPTION="every 15 minutes"
        ;;
esac

echo ""
echo "Selected schedule: $DESCRIPTION"
echo "Cron expression: $CRON_SCHEDULE"
echo ""

# Create the cron job entry
CRON_ENTRY="$CRON_SCHEDULE cd $SCRIPT_DIR && php email-fetcher.php >> /var/log/meleva-email-fetcher.log 2>&1"

echo "Cron job entry:"
echo "$CRON_ENTRY"
echo ""

# Ask for confirmation
read -p "Do you want to add this cron job? (y/N): " confirm

if [[ $confirm =~ ^[Yy]$ ]]; then
    # Add to crontab
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
    
    if [ $? -eq 0 ]; then
        echo "✓ Cron job added successfully!"
        echo ""
        echo "The email fetcher will now run $DESCRIPTION"
        echo "Logs will be written to: /var/log/meleva-email-fetcher.log"
        echo ""
        echo "To view current cron jobs: crontab -l"
        echo "To remove this cron job: crontab -e (then delete the line)"
        echo "To view logs: tail -f /var/log/meleva-email-fetcher.log"
        
        # Create log file with proper permissions
        sudo touch /var/log/meleva-email-fetcher.log
        sudo chmod 666 /var/log/meleva-email-fetcher.log
        
        echo ""
        echo "Setup complete! Email fetching is now automated."
        
    else
        echo "✗ Failed to add cron job"
        echo "You may need to run this script with appropriate permissions"
        exit 1
    fi
else
    echo "Cron job not added. You can add it manually later:"
    echo ""
    echo "1. Run: crontab -e"
    echo "2. Add this line:"
    echo "   $CRON_ENTRY"
    echo "3. Save and exit"
fi

echo ""
echo "=== Setup Complete ==="

# Show next steps
echo ""
echo "Next steps:"
echo "1. Test the setup by running: php $EMAIL_FETCHER"
echo "2. Check the admin messages page to see if emails are being fetched"
echo "3. Monitor the log file: tail -f /var/log/meleva-email-fetcher.log"
echo "4. Adjust the cron schedule if needed"
echo ""
echo "For troubleshooting, check:"
echo "- Email server credentials in email-fetcher.php"
echo "- IMAP extension is properly installed"
echo "- File permissions are correct"
echo "- Log file for error messages"
