<?php
/**
 * Debug Reply HTTP 500 Error
 * Simulate the exact reply process to find the error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Debug Reply HTTP 500 Error</h2>";
echo "<p>Simulating the exact reply process to identify the issue...</p>";

try {
    // Define admin access constant
    define('ADMIN_ACCESS', true);
    
    // Include required files (same as messages.php)
    require_once 'config/config.php';
    require_once 'classes/models.php';
    require_once 'classes/EmailService.php';
    
    echo "<p>✅ Files included successfully</p>";
    
    // Test authentication (same as messages.php)
    Auth::startSession();
    if (!Auth::isLoggedIn()) {
        echo "<p style='color: red;'>❌ Not logged in - this could be the issue!</p>";
        echo "<p><a href='login.php'>Please log in first</a></p>";
        exit;
    } else {
        echo "<p>✅ User is logged in</p>";
    }
    
    // Get current user (same as messages.php)
    $currentUser = Auth::getCurrentUser();
    echo "<p>✅ Current user: " . htmlspecialchars($currentUser['username'] ?? 'Unknown') . "</p>";
    
    // Initialize models (same as messages.php)
    $messageModel = new Message();
    $emailService = new EmailService();
    echo "<p>✅ Models initialized successfully</p>";
    
    echo "<hr>";
    
    // Create a test message to reply to
    echo "<h3>Creating Test Message</h3>";
    
    $testMessageData = [
        'sender_name' => 'Test Customer',
        'sender_email' => '<EMAIL>',
        'subject' => 'Contact Form - Test Message',
        'message_content' => 'This is a test message for reply debugging.',
        'message_category' => 'contact'
    ];
    
    $testMessageId = $messageModel->create($testMessageData);
    
    if ($testMessageId) {
        echo "<p style='color: green;'>✅ Test message created with ID: {$testMessageId}</p>";
        
        // Simulate the exact reply process from messages.php
        echo "<h3>Simulating Reply Process</h3>";
        
        // Simulate POST data (same as messages.php)
        $messageId = $testMessageId;
        $replyContent = "Thank you for your message. This is a test reply from the debug script.";
        $senderEmail = $testMessageData['sender_email'];
        $senderName = $testMessageData['sender_name'];
        $originalSubject = $testMessageData['subject'];
        
        echo "<p><strong>Reply data:</strong></p>";
        echo "<ul>";
        echo "<li>Message ID: {$messageId}</li>";
        echo "<li>Reply content: " . htmlspecialchars($replyContent) . "</li>";
        echo "<li>Sender email: " . htmlspecialchars($senderEmail) . "</li>";
        echo "<li>Sender name: " . htmlspecialchars($senderName) . "</li>";
        echo "<li>Original subject: " . htmlspecialchars($originalSubject) . "</li>";
        echo "</ul>";
        
        // Step 1: Save reply to database (same as messages.php)
        echo "<p>Step 1: Saving reply to database...</p>";
        $replyResult = $messageModel->reply($messageId, $replyContent);
        
        if ($replyResult) {
            echo "<p style='color: green;'>✅ Reply saved to database successfully</p>";
            
            // Step 2: Mark as read (same as messages.php)
            echo "<p>Step 2: Marking message as read...</p>";
            $markReadResult = $messageModel->markAsRead($messageId);
            
            if ($markReadResult) {
                echo "<p style='color: green;'>✅ Message marked as read successfully</p>";
                
                // Step 3: Send email reply (same as messages.php)
                echo "<p>Step 3: Preparing email reply...</p>";
                
                $originalMessage = [
                    'sender_name' => $senderName,
                    'sender_email' => $senderEmail,
                    'subject' => $originalSubject
                ];
                
                echo "<p>Original message data:</p>";
                echo "<pre>" . print_r($originalMessage, true) . "</pre>";
                
                echo "<p>Current user data:</p>";
                echo "<pre>" . print_r($currentUser, true) . "</pre>";
                
                // This is where the error might occur
                echo "<p>Attempting to send email reply...</p>";
                
                try {
                    $emailSent = $emailService->sendReplyEmail($originalMessage, $replyContent, $currentUser);
                    
                    if ($emailSent) {
                        echo "<p style='color: green;'>✅ Email reply sent successfully</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ Email reply failed to send (but no exception thrown)</p>";
                    }
                    
                } catch (Exception $emailError) {
                    echo "<p style='color: red;'>❌ Email reply error: " . htmlspecialchars($emailError->getMessage()) . "</p>";
                    echo "<p><strong>Stack trace:</strong></p>";
                    echo "<pre>" . htmlspecialchars($emailError->getTraceAsString()) . "</pre>";
                }
                
            } else {
                echo "<p style='color: red;'>❌ Failed to mark message as read</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Failed to save reply to database</p>";
        }
        
        // Clean up test message
        $deleteStmt = $messageModel->getDb()->prepare("DELETE FROM messages WHERE message_id = :id");
        $deleteStmt->bindParam(':id', $testMessageId);
        $deleteStmt->execute();
        echo "<p>✅ Test message cleaned up</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test message</p>";
    }
    
    echo "<hr>";
    
    // Test the API endpoint that the reply form uses
    echo "<h3>Testing API Endpoint</h3>";
    
    if (file_exists('api/get_message.php')) {
        echo "<p>✅ API file exists: api/get_message.php</p>";
        
        // Test if we can access the API (simulate the AJAX call)
        $testUrl = "http://localhost/meleva/admin-dashboard/api/get_message.php?id=1";
        echo "<p>API URL would be: " . htmlspecialchars($testUrl) . "</p>";
        echo "<p>⚠️ Note: The reply form uses AJAX to fetch message details before showing the reply modal</p>";
        
    } else {
        echo "<p style='color: red;'>❌ API file missing: api/get_message.php</p>";
    }
    
    echo "<hr>";
    
    // Summary and recommendations
    echo "<h3>🎯 Diagnosis Summary</h3>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Potential Causes of HTTP 500 Error:</h4>";
    echo "<ol>";
    echo "<li><strong>Authentication Issue:</strong> User session might expire during reply process</li>";
    echo "<li><strong>API Endpoint Error:</strong> The get_message.php API might have authentication issues</li>";
    echo "<li><strong>Email Service Error:</strong> SMTP configuration or email sending might fail</li>";
    echo "<li><strong>Database Error:</strong> Reply or markAsRead operations might fail</li>";
    echo "<li><strong>Missing CSRF Token:</strong> Form might be missing security tokens</li>";
    echo "</ol>";
    
    echo "<h4>Next Steps:</h4>";
    echo "<ul>";
    echo "<li>✅ <NAME_EMAIL> email</li>";
    echo "<li>✅ Removed all time references from templates</li>";
    echo "<li>🔧 Test the reply functionality in messages.php again</li>";
    echo "<li>🔧 Check browser developer tools (F12) for exact error details</li>";
    echo "<li>🔧 Verify admin session is active when replying</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Fatal Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← Back to Messages</a> | <a href='login.php'>Login Page</a></p>";
?>
