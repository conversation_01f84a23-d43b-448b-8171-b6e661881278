<?php
/**
 * Admin Email Handler
 * Handles admin replies from external email clients
 */

require_once __DIR__ . '/../config/config.php';
require_once 'models.php';
require_once 'EmailService.php';

class AdminEmailHandler {
    private $db;
    private $messageModel;
    private $emailService;
    
    // Admin email addresses that can reply to messages
    private $adminEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
    ];
    
    public function __construct() {
        $this->db = Database::getInstance()->getConnection();
        $this->messageModel = new Message();
        $this->emailService = new EmailService();
    }
    
    /**
     * Process incoming email from admin
     */
    public function processAdminReply($emailData) {
        try {
            // Verify this is from an admin email
            if (!$this->isAdminEmail($emailData['sender_email'])) {
                error_log("Non-admin email attempted reply: " . $emailData['sender_email']);
                return false;
            }
            
            // Extract conversation information
            $conversationInfo = $this->extractConversationInfo($emailData);
            
            if (!$conversationInfo) {
                error_log("Could not extract conversation info from admin reply");
                return false;
            }
            
            // Get admin user info
            $adminUser = $this->getAdminUserByEmail($emailData['sender_email']);
            
            // Process the reply
            $result = $this->saveAdminReply($conversationInfo, $emailData, $adminUser);
            
            if ($result) {
                // Send confirmation to customer
                $this->forwardReplyToCustomer($conversationInfo, $emailData, $adminUser);
                
                // Update conversation status
                $this->updateConversationStatus($conversationInfo['conversation_id'], 'pending');
                
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            error_log("Admin email processing error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Check if email is from authorized admin
     */
    private function isAdminEmail($email) {
        return in_array(strtolower($email), array_map('strtolower', $this->adminEmails));
    }
    
    /**
     * Extract conversation information from email
     */
    private function extractConversationInfo($emailData) {
        // Method 1: Check for conversation ID in subject
        if (preg_match('/\[CONV_([A-Z0-9]+)\]/', $emailData['subject'], $matches)) {
            $conversationId = 'CONV_' . $matches[1];
            return $this->getConversationDetails($conversationId);
        }
        
        // Method 2: Check In-Reply-To header
        if (!empty($emailData['in_reply_to'])) {
            $sql = "SELECT m.*, ec.* FROM messages m 
                    JOIN email_conversations ec ON m.conversation_id = ec.conversation_id 
                    WHERE m.email_message_id = :message_id LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':message_id' => $emailData['in_reply_to']]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        // Method 3: Check for conversation ID in custom headers
        if (!empty($emailData['headers']['X-Conversation-ID'])) {
            return $this->getConversationDetails($emailData['headers']['X-Conversation-ID']);
        }
        
        // Method 4: Try to match by customer email in recent conversations
        if (!empty($emailData['original_to'])) {
            $sql = "SELECT m.*, ec.* FROM messages m 
                    JOIN email_conversations ec ON m.conversation_id = ec.conversation_id 
                    WHERE ec.sender_email = :email 
                    AND ec.status IN ('open', 'pending')
                    ORDER BY ec.last_activity DESC LIMIT 1";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([':email' => $emailData['original_to']]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return null;
    }
    
    /**
     * Get conversation details by ID
     */
    private function getConversationDetails($conversationId) {
        $sql = "SELECT m.*, ec.* FROM messages m 
                JOIN email_conversations ec ON m.conversation_id = ec.conversation_id 
                WHERE ec.conversation_id = :conversation_id 
                AND m.message_id = ec.initial_message_id LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':conversation_id' => $conversationId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get admin user by email
     */
    private function getAdminUserByEmail($email) {
        $sql = "SELECT * FROM users WHERE email = :email LIMIT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute([':email' => $email]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            // Create a default admin user entry for tracking
            return [
                'user_id' => null,
                'username' => 'Admin (' . $email . ')',
                'email' => $email,
                'role' => 'admin'
            ];
        }
        
        return $user;
    }
    
    /**
     * Save admin reply to database
     */
    private function saveAdminReply($conversationInfo, $emailData, $adminUser) {
        try {
            // Clean the reply content (remove quoted text)
            $cleanContent = $this->cleanReplyContent($emailData['content']);
            
            $messageData = [
                'conversation_id' => $conversationInfo['conversation_id'],
                'email_message_id' => $emailData['message_id'],
                'in_reply_to' => $emailData['in_reply_to'] ?? null,
                'email_thread_id' => $emailData['thread_id'] ?? null,
                'message_type' => 'outgoing',
                'sender_name' => $adminUser['username'],
                'sender_email' => $emailData['sender_email'],
                'subject' => $emailData['subject'],
                'message_content' => $cleanContent,
                'email_status' => 'sent',
                'original_message_id' => $conversationInfo['message_id']
            ];
            
            return $this->messageModel->create($messageData);
            
        } catch (Exception $e) {
            error_log("Error saving admin reply: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Forward admin reply to customer
     */
    private function forwardReplyToCustomer($conversationInfo, $emailData, $adminUser) {
        try {
            // Prepare customer email data
            $customerEmailData = [
                'sender_name' => $conversationInfo['sender_name'],
                'sender_email' => $conversationInfo['sender_email'],
                'subject' => $conversationInfo['subject'],
                'message_id' => $conversationInfo['message_id'],
                'conversation_id' => $conversationInfo['conversation_id']
            ];
            
            // Clean reply content
            $cleanContent = $this->cleanReplyContent($emailData['content']);
            
            // Send email to customer using existing email service
            return $this->emailService->sendReplyEmail($customerEmailData, $cleanContent, $adminUser);
            
        } catch (Exception $e) {
            error_log("Error forwarding reply to customer: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Clean reply content by removing quoted text
     */
    private function cleanReplyContent($content) {
        // Remove common email signatures and quoted text
        $patterns = [
            '/^On .* wrote:.*$/ms',  // "On [date] [person] wrote:"
            '/^From:.*$/ms',         // Email headers
            '/^Sent:.*$/ms',
            '/^To:.*$/ms',
            '/^Subject:.*$/ms',
            '/^>.*$/m',              // Quoted lines starting with >
            '/^-----Original Message-----.*$/ms',
            '/^________________________________.*$/ms',
            '/^\s*--\s*$/m.*$/ms',   // Email signatures starting with --
        ];
        
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }
        
        return trim($content);
    }
    
    /**
     * Update conversation status
     */
    private function updateConversationStatus($conversationId, $status) {
        $sql = "UPDATE email_conversations SET 
                status = :status, 
                last_activity = NOW() 
                WHERE conversation_id = :conversation_id";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([
            ':status' => $status,
            ':conversation_id' => $conversationId
        ]);
    }
    
    /**
     * Generate admin notification email for new messages
     */
    public function sendAdminNotification($messageData) {
        try {
            $conversationId = $messageData['conversation_id'];
            $subject = "[{$conversationId}] New Message: " . $messageData['subject'];
            
            $emailContent = $this->generateAdminNotificationContent($messageData);
            
            // Send to all admin emails
            foreach ($this->adminEmails as $adminEmail) {
                $this->sendNotificationEmail($adminEmail, $subject, $emailContent, $messageData);
            }
            
            return true;
            
        } catch (Exception $e) {
            error_log("Error sending admin notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Generate admin notification email content
     */
    private function generateAdminNotificationContent($messageData) {
        return "
New message received:

From: {$messageData['sender_name']} <{$messageData['sender_email']}>
Subject: {$messageData['subject']}
Conversation ID: {$messageData['conversation_id']}

Message:
{$messageData['message_content']}

---
To reply to this message, simply reply to this email from any device.
The conversation will be automatically tracked.

View in admin dashboard: " . $_SERVER['HTTP_HOST'] . "/admin-dashboard/messages.php
        ";
    }
    
    /**
     * Send notification email to admin
     */
    private function sendNotificationEmail($adminEmail, $subject, $content, $messageData) {
        $mail = new PHPMailer\PHPMailer\PHPMailer(true);
        
        try {
            // Configure SMTP (reuse existing configuration)
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>';
            $mail->Password = 'lhgd sztv vrxe odws';
            $mail->SMTPSecure = PHPMailer\PHPMailer\PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;
            
            // Set headers for conversation tracking
            $mail->setFrom('<EMAIL>', 'Meleva Tours Notifications');
            $mail->addReplyTo($messageData['sender_email'], $messageData['sender_name']);
            $mail->addAddress($adminEmail);
            
            // Add conversation tracking headers
            $mail->addCustomHeader('X-Conversation-ID', $messageData['conversation_id']);
            $mail->addCustomHeader('X-Original-To', $messageData['sender_email']);
            
            $mail->Subject = $subject;
            $mail->Body = $content;
            
            return $mail->send();
            
        } catch (Exception $e) {
            error_log("Admin notification email error: " . $e->getMessage());
            return false;
        }
    }
}
?>
