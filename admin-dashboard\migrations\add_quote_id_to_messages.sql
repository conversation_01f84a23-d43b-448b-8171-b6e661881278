-- Add quote_id column to messages table for linking messages to quotes
-- This allows messages to be associated with specific quote requests

ALTER TABLE messages 
ADD COLUMN quote_id INT NULL AFTER message_id,
ADD INDEX idx_quote_id (quote_id);

-- Add foreign key constraint if quotes table exists
-- Note: This will only work if the quotes table exists
-- ALTER TABLE messages 
-- ADD FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE SET NULL;
