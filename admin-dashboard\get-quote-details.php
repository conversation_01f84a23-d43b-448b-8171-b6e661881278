<?php
// Get detailed quote information for admin modal
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';

// Check if user is logged in
session_start();
if (!isset($_SESSION['user_id'])) {
    http_response_code(403);
    echo '<div class="text-red-600">Access denied</div>';
    exit;
}

$quoteId = $_GET['id'] ?? null;

if (!$quoteId || !is_numeric($quoteId)) {
    echo '<div class="text-red-600">Invalid quote ID</div>';
    exit;
}

try {
    $quoteModel = new Quote();
    $paymentModel = new Payment();

    // Get quote with payment summary
    $quote = $quoteModel->getQuoteWithPaymentSummary($quoteId);

    if (!$quote) {
        echo '<div class="text-red-600">Quote not found</div>';
        exit;
    }

    // Get payment history
    $payments = $paymentModel->getByQuoteId($quoteId);

    // Get quote history
    $quoteHistory = $quoteModel->getQuoteHistory($quoteId);
    
    // Get associated packages
    $db = Database::getInstance()->getConnection();
    $packagesQuery = $db->prepare("
        SELECT tp.name, tp.description, tp.price, tp.duration, pt.type_name, d.name as destination_name
        FROM quote_packages qp
        JOIN tour_packages tp ON qp.tour_package_id = tp.tour_package_id
        LEFT JOIN tour_package_types pt ON tp.package_type_id = pt.package_type_id
        LEFT JOIN destinations d ON tp.destination_id = d.destination_id
        WHERE qp.quote_id = ?
    ");
    $packagesQuery->execute([$quoteId]);
    $packages = $packagesQuery->fetchAll();
    
    ?>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Customer Information -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-user mr-2 text-blue-600"></i>
                Customer Information
            </h4>
            <div class="space-y-2 text-sm">
                <div><strong>Name:</strong> <?php echo Utils::displayText($quote['customer_name']); ?></div>
                <div><strong>Email:</strong>
                    <a href="mailto:<?php echo htmlspecialchars($quote['customer_email']); ?>"
                       class="text-blue-600 hover:text-blue-800">
                        <?php echo Utils::displayText($quote['customer_email']); ?>
                    </a>
                </div>
                <div><strong>Phone:</strong> 
                    <a href="tel:<?php echo htmlspecialchars($quote['customer_phone']); ?>" 
                       class="text-blue-600 hover:text-blue-800">
                        <?php echo htmlspecialchars($quote['customer_phone']); ?>
                    </a>
                </div>
                <?php if (!empty($quote['customer_country'])): ?>
                    <div><strong>Country:</strong> <?php echo htmlspecialchars($quote['customer_country']); ?></div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quote Information -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-file-invoice mr-2 text-green-600"></i>
                Quote Information
            </h4>
            <div class="space-y-2 text-sm">
                <div><strong>Reference:</strong> <?php echo htmlspecialchars($quote['quote_reference']); ?></div>
                <div><strong>Status:</strong> 
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                        <?php
                        switch($quote['quote_status']) {
                            case 'quoted': echo 'bg-blue-100 text-blue-800'; break;
                            case 'accepted': echo 'bg-blue-100 text-blue-800'; break;
                            case 'rejected': echo 'bg-red-100 text-red-800'; break;
                            case 'expired': echo 'bg-gray-100 text-gray-800'; break;
                            case 'paid': echo 'bg-green-100 text-green-800'; break;
                            case 'partially_paid': echo 'bg-yellow-100 text-yellow-800'; break;
                            default: echo 'bg-purple-100 text-purple-800';
                        }
                        ?>">
                        <?php
                        switch($quote['quote_status']) {
                            case 'partially_paid': echo 'Partially Paid'; break;
                            case 'paid': echo 'Fully Paid'; break;
                            case 'quoted': echo 'Quoted'; break;
                            case 'accepted': echo 'Accepted'; break;
                            case 'rejected': echo 'Rejected'; break;
                            case 'expired': echo 'Expired'; break;
                            default: echo ucfirst($quote['quote_status']);
                        }
                        ?>
                    </span>
                </div>
                <div><strong>Created:</strong> <?php echo date('F j, Y \a\t g:i A', strtotime($quote['created_at'])); ?></div>
                <?php if ($quote['quoted_amount']): ?>
                    <div><strong>Quoted Amount:</strong> $<?php echo number_format($quote['quoted_amount'], 2); ?></div>
                <?php endif; ?>
                <?php if (isset($quote['total_paid']) && $quote['total_paid'] > 0): ?>
                    <div><strong>Amount Paid:</strong>
                        <span class="text-green-600 font-semibold">$<?php echo number_format($quote['total_paid'], 2); ?></span>
                    </div>
                <?php endif; ?>
                <?php if (isset($quote['balance_remaining']) && $quote['balance_remaining'] > 0): ?>
                    <div><strong>Balance Remaining:</strong>
                        <span class="text-orange-600 font-semibold">$<?php echo number_format($quote['balance_remaining'], 2); ?></span>
                    </div>
                <?php endif; ?>
                <?php if ($quote['quoted_at']): ?>
                    <?php
                    $quotedDate = new DateTime($quote['quoted_at'], new DateTimeZone('UTC'));
                    $quotedDate->setTimezone(new DateTimeZone(DatabaseConfig::TIMEZONE));
                    ?>
                    <div><strong>Quoted On:</strong> <?php echo $quotedDate->format('F j, Y \a\t g:i A'); ?></div>
                <?php endif; ?>
                <?php if ($quote['expires_at']): ?>
                    <?php
                    $expiresDate = new DateTime($quote['expires_at'], new DateTimeZone('UTC'));
                    $expiresDate->setTimezone(new DateTimeZone(DatabaseConfig::TIMEZONE));
                    ?>
                    <div><strong>Expires:</strong> <?php echo $expiresDate->format('F j, Y \a\t g:i A'); ?></div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Travel Details -->
    <div class="mt-6 bg-gray-50 p-4 rounded-lg">
        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fas fa-plane mr-2 text-orange-600"></i>
            Travel Details
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <strong>Travel Date:</strong><br>
                <?php
                if ($quote['travel_date'] && $quote['travel_date'] !== '0000-00-00' && $quote['travel_date'] !== '1970-01-01') {
                    $travelDate = new DateTime($quote['travel_date'], new DateTimeZone(DatabaseConfig::TIMEZONE));
                    echo $travelDate->format('F j, Y');
                } else {
                    echo '<span class="text-gray-500">Flexible dates</span>';
                }
                ?>
            </div>
            <div>
                <strong>Travelers:</strong><br>
                <?php echo $quote['number_of_adults']; ?> Adult(s)
                <?php if ($quote['number_of_children'] > 0): ?>
                    <br><?php echo $quote['number_of_children']; ?> Child(ren)
                <?php endif; ?>
            </div>
            <div>
                <strong>Total Travelers:</strong><br>
                <?php echo ($quote['number_of_adults'] + $quote['number_of_children']); ?> People
            </div>
        </div>
    </div>

    <!-- Selected Packages -->
    <?php if (!empty($packages)): ?>
        <div class="mt-6 bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-suitcase mr-2 text-orange-500"></i>
                Selected Tour Packages
            </h4>
            <div class="space-y-2">
                <?php foreach ($packages as $package): ?>
                    <div class="bg-white p-3 rounded border">
                        <div class="flex items-center">
                            <i class="fas fa-suitcase text-orange-500 mr-3 text-sm"></i>
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($package['name']); ?></div>
                                <?php if ($package['type_name'] || $package['destination_name'] || $package['duration']): ?>
                                    <div class="text-xs text-gray-500 mt-1">
                                        <?php
                                        $details = [];
                                        if ($package['type_name']) $details[] = $package['type_name'];
                                        if ($package['destination_name']) $details[] = $package['destination_name'];
                                        if ($package['duration']) $details[] = $package['duration'];
                                        echo htmlspecialchars(implode(' • ', $details));
                                        ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <?php if ($package['price']): ?>
                                <div class="text-orange-600 font-semibold text-sm">
                                    $<?php echo number_format($package['price'], 0); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Selected Activities -->
    <?php if (!empty($quote['selected_activities'])): ?>
        <div class="mt-6 bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-heart mr-2 text-pink-600"></i>
                Activities of Interest
            </h4>
            <div class="bg-white p-3 rounded border">
                <div class="flex flex-wrap gap-2">
                    <?php
                    $activities = explode(', ', $quote['selected_activities']);
                    foreach ($activities as $activity):
                        $activity = trim($activity);
                        if (!empty($activity)):
                    ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-orange-100 text-orange-800">
                            <?php
                            // Add icons for different activities
                            $activityIcons = [
                                'Safari' => 'fas fa-binoculars',
                                'Beach Holiday' => 'fas fa-umbrella-beach',
                                'Mountain Climbing' => 'fas fa-mountain',
                                'Cultural Tours' => 'fas fa-landmark',
                                'Wildlife Photography' => 'fas fa-camera',
                                'Adventure Sports' => 'fas fa-parachute-box'
                            ];
                            $icon = $activityIcons[$activity] ?? 'fas fa-star';
                            ?>
                            <i class="<?php echo $icon; ?> mr-2"></i>
                            <?php echo htmlspecialchars($activity); ?>
                        </span>
                    <?php
                        endif;
                    endforeach;
                    ?>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <!-- Special Requirements -->
    <?php if (!empty($quote['special_requirements'])): ?>
        <div class="mt-6 bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
                <i class="fas fa-clipboard-list mr-2 text-red-600"></i>
                Special Requirements
            </h4>
            <div class="bg-white p-3 rounded border text-sm">
                <?php echo nl2br(htmlspecialchars($quote['special_requirements'])); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Payment Information -->
    <?php if ($quote['quoted_amount']): ?>
    <div class="bg-gray-50 p-4 rounded-lg mt-6">
        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fas fa-money-bill-wave mr-2 text-green-600"></i>
            Payment Information
        </h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
                <strong>Quote Amount:</strong><br>
                <span class="text-lg font-semibold text-green-600">$<?php echo number_format($quote['quoted_amount'], 2); ?></span>
            </div>
            <div>
                <strong>Total Paid:</strong><br>
                <span class="text-lg font-semibold text-blue-600">$<?php echo number_format($quote['total_paid'] ?? 0, 2); ?></span>
            </div>
            <div>
                <strong>Balance Remaining:</strong><br>
                <span class="text-lg font-semibold <?php echo ($quote['balance_remaining'] ?? 0) > 0 ? 'text-orange-600' : 'text-green-600'; ?>">
                    $<?php echo number_format($quote['balance_remaining'] ?? 0, 2); ?>
                </span>
            </div>
        </div>

        <div class="mt-3">
            <strong>Payment Status:</strong>
            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ml-2
                <?php
                switch($quote['payment_status'] ?? 'unpaid') {
                    case 'paid': echo 'bg-green-100 text-green-800'; break;
                    case 'partial': echo 'bg-yellow-100 text-yellow-800'; break;
                    default: echo 'bg-gray-100 text-gray-800';
                }
                ?>">
                <?php
                switch($quote['payment_status'] ?? 'unpaid') {
                    case 'paid': echo 'Fully Paid'; break;
                    case 'partial': echo 'Partially Paid'; break;
                    default: echo 'Unpaid';
                }
                ?>
            </span>
        </div>
    </div>
    <?php endif; ?>

    <!-- Payment History -->
    <?php if (!empty($payments)): ?>
    <div class="bg-gray-50 p-4 rounded-lg mt-6">
        <h4 class="font-semibold text-gray-900 mb-3 flex items-center">
            <i class="fas fa-history mr-2 text-purple-600"></i>
            Payment History
        </h4>
        <div class="space-y-3">
            <?php foreach ($payments as $payment): ?>
            <div class="bg-white p-3 rounded border">
                <div class="flex justify-between items-start">
                    <div>
                        <div class="font-medium"><?php echo htmlspecialchars($payment['payment_reference']); ?></div>
                        <div class="text-sm text-gray-600">
                            <?php echo ucfirst($payment['payment_type']); ?> Payment -
                            $<?php echo number_format($payment['amount'], 2); ?>
                        </div>
                        <?php if ($payment['payment_date']): ?>
                        <div class="text-xs text-gray-500">
                            <?php echo date('M j, Y g:i A', strtotime($payment['payment_date'])); ?>
                        </div>
                        <?php endif; ?>
                    </div>
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        <?php
                        switch($payment['payment_status']) {
                            case 'completed': echo 'bg-green-100 text-green-800'; break;
                            case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                            case 'failed': echo 'bg-red-100 text-red-800'; break;
                            default: echo 'bg-gray-100 text-gray-800';
                        }
                        ?>">
                        <?php echo ucfirst($payment['payment_status']); ?>
                    </span>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <div class="mt-6 flex justify-end space-x-3">
        <?php if ($quote['quote_status'] === 'pending'): ?>
            <button onclick="closeDetailsModal(); openQuoteModal(<?php echo $quote['quote_id']; ?>, '<?php echo htmlspecialchars($quote['quote_reference']); ?>')" 
                    class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center">
                <i class="fas fa-file-invoice-dollar mr-2"></i>
                Send Quote
            </button>
        <?php endif; ?>
        
        <?php if ($quote['quote_status'] === 'quoted'): ?>
            <a href="../payment.php?quote_ref=<?php echo urlencode($quote['quote_reference']); ?>" 
               target="_blank"
               class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 flex items-center">
                <i class="fas fa-credit-card mr-2"></i>
                View Payment Page
            </a>
        <?php endif; ?>
        
        <button onclick="closeDetailsModal()" 
                class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
            Close
        </button>
    </div>

    <?php
    
} catch (Exception $e) {
    echo '<div class="text-red-600">Error loading quote details: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
