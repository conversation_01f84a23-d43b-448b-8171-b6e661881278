<?php
// Start output buffering to prevent header issues
ob_start();

// Define admin access constant
define('ADMIN_ACCESS', true);

require_once '../config/config.php';
require_once '../classes/models.php';

// Include security middleware for authentication
require_once '../includes/security_middleware.php';
requireRole('admin');

header('Content-Type: application/json');

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    echo json_encode(['success' => false, 'error' => 'Invalid message ID']);
    exit;
}

$messageId = intval($_GET['id']);
$messageModel = new Message();

try {
    $message = $messageModel->findById($messageId);
    
    if ($message) {
        // Mark as read when viewed
        $messageModel->markAsRead($messageId);
        
        echo json_encode([
            'success' => true,
            'message' => $message
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Message not found'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
