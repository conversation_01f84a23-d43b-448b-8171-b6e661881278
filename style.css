/* ===================================
   MELEVA TOURS - GLOBAL STYLES
   Common styles used across all pages
   =================================== */

/* Reset + Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body, html {
  font-family: 'Segoe UI', sans-serif;
  scroll-behavior: smooth;
}

/* ===================================
   NAVIGATION STYLES
   =================================== */

#main-nav {
  transition: all 0.3s ease-in-out;
}

/* Ensure hero sections start from top when nav is transparent */
body.has-hero-section {
  padding-top: 0;
}

/* Add padding for pages without hero sections */
body:not(.has-hero-section) {
  padding-top: 80px;
}

/* ===================================
   COMMON BACKGROUND PATTERNS
   =================================== */

.safari-pattern {
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
}

.hero-overlay {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 100%);
}

.parallax-bg {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

@media (max-width: 768px) {
    .parallax-bg {
        background-attachment: scroll;
    }
}

/* ===================================
   COMMON TEXT STYLES
   =================================== */

.text-shadow {
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.gradient-text {
    background: linear-gradient(135deg, #f97316, #ea580c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* ===================================
   COMMON INTERACTIVE ELEMENTS
   =================================== */

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.safari-border {
    border-image: linear-gradient(45deg, #f97316, #ea580c, #dc2626) 1;
}

/* ===================================
   SWIPER COMMON STYLES
   =================================== */

.swiper-button-next,
.swiper-button-prev {
    color: #f97316 !important;
}

.swiper-button-next:after,
.swiper-button-prev:after {
    font-size: 20px !important;
}

/* Custom Swiper Pagination Styles */
.swiper-pagination {
    position: relative !important;
    margin-top: 2rem;
}

.swiper-pagination-bullet {
    width: 12px !important;
    height: 12px !important;
    background: rgb(58, 58, 58) !important;
    opacity: 1 !important;
    margin: 0 6px !important;
    transition: all 0.3s ease !important;
}

.swiper-pagination-bullet-active {
    background: linear-gradient(135deg, #f97316, #ea580c) !important;
    transform: scale(1.2) !important;
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.4) !important;
}

.swiper-pagination-bullet:hover {
    background: rgb(58, 58, 58) !important;
    transform: scale(1.1) !important;
}

.swiper-pagination-bullet-active:hover {
    background: linear-gradient(135deg, #f97316, #ea580c) !important;
    transform: scale(1.2) !important;
}

/* ===================================
   IMAGE OPTIMIZATION STYLES
   =================================== */

/* Base image styles */
img {
  max-width: 100%;
  height: auto;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Lazy loading states */
img[data-src], img.lazy-image {
  opacity: 0;
  transform: scale(0.95);
}

img.image-loaded {
  opacity: 1;
  transform: scale(1);
}

img.image-error {
  opacity: 0.5;
  background-color: #f3f4f6;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='%23999' viewBox='0 0 24 24'%3e%3cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 48px 48px;
}

/* Critical images (above the fold) */
.critical-image, .hero-image, .logo {
  opacity: 1 !important;
  transform: scale(1) !important;
}

/* Image containers with loading states */
.image-container {
  position: relative;
  overflow: hidden;
  background-color: #f3f4f6;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Image hover effects */
.image-hover-zoom {
  overflow: hidden;
}

.image-hover-zoom img {
  transition: transform 0.3s ease;
}

.image-hover-zoom:hover img {
  transform: scale(1.05);
}

/* Gallery specific optimizations */
.gallery-item img {
  cursor: pointer;
  transition: all 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Progressive loading shimmer effect */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.image-loading {
  background: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

/* ===================================
   IMAGE OPTIMIZATION STYLES
   =================================== */

/* Base image styles */
img {
  max-width: 100%;
  height: auto;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Lazy loading states */
img[data-src] {
  opacity: 0;
  transform: scale(0.95);
}

img.image-loaded {
  opacity: 1;
  transform: scale(1);
}

img.image-error {
  opacity: 0.5;
  background-color: #f3f4f6;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' fill='%23999' viewBox='0 0 24 24'%3e%3cpath d='M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 48px 48px;
}

/* Responsive image containers */
.image-container {
  position: relative;
  overflow: hidden;
  background-color: #f3f4f6;
}

.image-container::before {
  content: '';
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, #f3f4f6 25%, transparent 25%),
              linear-gradient(-45deg, #f3f4f6 25%, transparent 25%),
              linear-gradient(45deg, transparent 75%, #f3f4f6 75%),
              linear-gradient(-45deg, transparent 75%, #f3f4f6 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  opacity: 0.1;
  z-index: 1;
}

.image-container img {
  position: relative;
  z-index: 2;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Image hover effects */
.image-hover-zoom {
  overflow: hidden;
}

.image-hover-zoom img {
  transition: transform 0.3s ease;
}

.image-hover-zoom:hover img {
  transform: scale(1.05);
}

/* Progressive image loading */
.progressive-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.progressive-image-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #f3f4f6;
  background-image: linear-gradient(90deg, #f3f4f6 0%, #e5e7eb 50%, #f3f4f6 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Critical images (above the fold) */
.critical-image {
  opacity: 1 !important;
  transform: scale(1) !important;
}

/* Gallery specific optimizations */
.gallery-item img {
  cursor: pointer;
  transition: all 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}