<?php
/**
 * Test Both Email Accounts
 * Quick test for both info@ and booking@ accounts
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Both Email Accounts</h2>";
echo "<p>Testing IMAP connection for both email accounts with correct passwords...</p>";

// Configuration
$host = 'mail.melevatours.co.ke';
$port = 993;
$encryption = 'ssl';

$accounts = [
    [
        'email' => '<EMAIL>',
        'password' => 'hi$Ch9=lYcap{7cA',
        'category' => 'contact'
    ],
    [
        'email' => '<EMAIL>',
        'password' => '1V^bAvDR!%)6,C&A',
        'category' => 'quote'
    ]
];

echo "<h3>Server Configuration:</h3>";
echo "<ul>";
echo "<li><strong>Host:</strong> $host</li>";
echo "<li><strong>Port:</strong> $port</li>";
echo "<li><strong>Encryption:</strong> $encryption</li>";
echo "</ul>";

// Check IMAP extension
if (!extension_loaded('imap')) {
    echo "<p style='color: red;'>❌ IMAP extension is not loaded!</p>";
    exit;
}

echo "<p style='color: green;'>✅ IMAP extension is loaded</p>";

echo "<h3>Testing Email Accounts:</h3>";

$successCount = 0;
$totalAccounts = count($accounts);

foreach ($accounts as $index => $account) {
    echo "<div style='border: 1px solid #ccc; margin: 10px 0; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Testing: {$account['email']} ({$account['category']})</h4>";
    
    // Clear previous errors
    if (function_exists('imap_errors')) {
        imap_errors();
        imap_alerts();
    }
    
    $hostname = "{{$host}:{$port}/imap/{$encryption}}INBOX";
    echo "<p><strong>Connecting to:</strong> $hostname</p>";
    
    $inbox = @imap_open($hostname, $account['email'], $account['password']);
    
    if ($inbox) {
        echo "<p style='color: green; font-weight: bold;'>✅ SUCCESS! Connected to {$account['email']}</p>";
        
        // Get mailbox info
        $mailboxInfo = imap_mailboxmsginfo($inbox);
        echo "<ul>";
        echo "<li><strong>Total messages:</strong> {$mailboxInfo->Nmsgs}</li>";
        echo "<li><strong>Unread messages:</strong> {$mailboxInfo->Unread}</li>";
        echo "<li><strong>Recent messages:</strong> {$mailboxInfo->Recent}</li>";
        echo "<li><strong>Mailbox size:</strong> " . number_format($mailboxInfo->Size) . " bytes</li>";
        echo "</ul>";
        
        // Try to get the latest email
        if ($mailboxInfo->Nmsgs > 0) {
            echo "<p><strong>Latest Email Preview:</strong></p>";
            $header = imap_headerinfo($inbox, $mailboxInfo->Nmsgs);
            if ($header) {
                $from = $header->from[0]->mailbox . "@" . $header->from[0]->host;
                $subject = $header->subject ?? 'No subject';
                $date = $header->date ?? 'No date';
                
                echo "<ul>";
                echo "<li><strong>From:</strong> " . htmlspecialchars($from) . "</li>";
                echo "<li><strong>Subject:</strong> " . htmlspecialchars($subject) . "</li>";
                echo "<li><strong>Date:</strong> " . htmlspecialchars($date) . "</li>";
                echo "</ul>";
            }
        } else {
            echo "<p><em>No emails found in this mailbox.</em></p>";
        }
        
        imap_close($inbox);
        $successCount++;
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>❌ FAILED to connect to {$account['email']}</p>";
        
        // Get IMAP errors
        $errors = imap_errors();
        $alerts = imap_alerts();
        
        if ($errors) {
            echo "<p><strong>Errors:</strong></p>";
            echo "<ul>";
            foreach ($errors as $error) {
                echo "<li style='color: red;'>" . htmlspecialchars($error) . "</li>";
            }
            echo "</ul>";
        }
        
        if ($alerts) {
            echo "<p><strong>Alerts:</strong></p>";
            echo "<ul>";
            foreach ($alerts as $alert) {
                echo "<li style='color: orange;'>" . htmlspecialchars($alert) . "</li>";
            }
            echo "</ul>";
        }
    }
    
    echo "</div>";
}

// Summary
echo "<hr>";
echo "<h3>Summary</h3>";

if ($successCount === $totalAccounts) {
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>🎉 ALL TESTS PASSED! ({$successCount}/{$totalAccounts})</h4>";
    echo "<p><strong>Excellent!</strong> Both email accounts are working perfectly.</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Your email fetcher is ready to use</li>";
    echo "<li>✅ You can now automatically capture customer email replies</li>";
    echo "<li>✅ Set up automatic email fetching with cron jobs</li>";
    echo "</ul>";
    echo "<p><a href='email-fetcher.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🚀 Start Fetching Emails</a>";
    echo "<a href='setup-email-cron.sh' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>⏰ Setup Automatic Fetching</a></p>";
    echo "</div>";
    
} elseif ($successCount > 0) {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>⚠️ PARTIAL SUCCESS ({$successCount}/{$totalAccounts})</h4>";
    echo "<p>Some accounts are working, but others need attention.</p>";
    echo "<p><strong>Working accounts can be used immediately.</strong></p>";
    echo "<p>Fix the failing accounts and test again.</p>";
    echo "</div>";
    
} else {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>❌ ALL TESTS FAILED (0/{$totalAccounts})</h4>";
    echo "<p>None of the email accounts could connect successfully.</p>";
    echo "<p><strong>Common Solutions:</strong></p>";
    echo "<ul>";
    echo "<li>Check if IMAP is enabled in your hosting control panel</li>";
    echo "<li>Verify the passwords are correct</li>";
    echo "<li>Contact your hosting provider for IMAP settings</li>";
    echo "<li>Try different server settings (hostname, port, encryption)</li>";
    echo "</ul>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Tools:</strong></p>";
echo "<p>";
echo "<a href='test-email-login.php'>🔧 Test Individual Accounts</a> | ";
echo "<a href='email-debug.php'>🔍 Full Diagnostic</a> | ";
echo "<a href='email-setup-guide.php'>📖 Setup Guide</a>";
echo "</p>";
?>
