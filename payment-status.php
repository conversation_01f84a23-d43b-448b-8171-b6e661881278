<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/booking_models.php';
require_once 'admin-dashboard/classes/PesapalService.php';

// Set content type to JSON
header('Content-Type: application/json');

// Initialize models and services
$paymentModel = new Payment();
$bookingModel = new Booking();
$pesapalService = new PesapalService();

// Get payment ID from request
$paymentId = $_GET['payment_id'] ?? null;

if (!$paymentId) {
    http_response_code(400);
    echo json_encode(['error' => 'Payment ID is required']);
    exit;
}

try {
    // Get payment details
    $payment = $paymentModel->findById($paymentId);
    
    if (!$payment) {
        http_response_code(404);
        echo json_encode(['error' => 'Payment not found']);
        exit;
    }
    
    // If payment is already completed or failed, return current status
    if (in_array($payment['payment_status'], ['completed', 'failed', 'cancelled'])) {
        echo json_encode([
            'status' => $payment['payment_status'],
            'payment_id' => $paymentId,
            'amount' => $payment['amount'],
            'currency' => $payment['currency'],
            'payment_date' => $payment['payment_date'],
            'pesapal_status' => $payment['pesapal_status']
        ]);
        exit;
    }
    
    // Check with Pesapal if we have a tracking ID
    if ($payment['pesapal_tracking_id']) {
        // Get auth token
        $authToken = $pesapalService->getAuthToken();
        
        if ($authToken) {
            // Get transaction status from Pesapal
            $pesapalStatus = $pesapalService->getTransactionStatus($authToken, $payment['pesapal_tracking_id']);
            
            if ($pesapalStatus) {
                $newStatus = 'pending'; // Default
                $pesapalStatusCode = $pesapalStatus['payment_status_description'] ?? '';
                
                // Map Pesapal status to our status
                switch (strtolower($pesapalStatusCode)) {
                    case 'completed':
                    case 'success':
                        $newStatus = 'completed';
                        break;
                    case 'failed':
                    case 'invalid':
                        $newStatus = 'failed';
                        break;
                    case 'cancelled':
                        $newStatus = 'cancelled';
                        break;
                    default:
                        $newStatus = 'pending';
                }
                
                // Update payment status if it has changed
                if ($newStatus !== $payment['payment_status']) {
                    $paymentModel->updatePaymentStatus(
                        $paymentId, 
                        $newStatus, 
                        $pesapalStatusCode,
                        $pesapalStatus['payment_method'] ?? null
                    );
                    
                    // Update booking status if payment is completed
                    if ($newStatus === 'completed') {
                        $booking = $bookingModel->findById($payment['booking_id']);
                        if ($booking) {
                            // Calculate total paid amount
                            $totalPaid = $paymentModel->getTotalPaidForBooking($payment['booking_id']);
                            
                            // Update booking payment status
                            if ($totalPaid >= $booking['total_amount']) {
                                $bookingModel->updateStatus($payment['booking_id'], 'confirmed', 'paid');
                            } else {
                                $bookingModel->updateStatus($payment['booking_id'], 'confirmed', 'partial');
                            }
                        }
                    }
                }
                
                // Return updated status
                echo json_encode([
                    'status' => $newStatus,
                    'payment_id' => $paymentId,
                    'amount' => $payment['amount'],
                    'currency' => $payment['currency'],
                    'payment_date' => $newStatus === 'completed' ? (new DateTime('now', new DateTimeZone(DatabaseConfig::TIMEZONE)))->format('Y-m-d H:i:s') : null,
                    'pesapal_status' => $pesapalStatusCode,
                    'payment_method' => $pesapalStatus['payment_method'] ?? null
                ]);
                exit;
            }
        }
    }
    
    // Return current status if we couldn't check with Pesapal
    echo json_encode([
        'status' => $payment['payment_status'],
        'payment_id' => $paymentId,
        'amount' => $payment['amount'],
        'currency' => $payment['currency'],
        'payment_date' => $payment['payment_date'],
        'pesapal_status' => $payment['pesapal_status']
    ]);
    
} catch (Exception $e) {
    error_log("Payment status check error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error']);
}
?>
