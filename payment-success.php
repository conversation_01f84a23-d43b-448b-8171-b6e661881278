<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/booking_models.php';
require_once 'admin-dashboard/classes/EmailService.php';

// Initialize models
$paymentModel = new Payment();
$bookingModel = new Booking();
$quoteModel = new Quote();
$emailService = new EmailService();

// Get payment ID from URL or quote reference
$paymentId = $_GET['payment_id'] ?? null;
$quoteRef = $_GET['quote_ref'] ?? null;
$payment = null;
$booking = null;
$quote = null;
$message = '';

$successMessage = '';
$isPartialPayment = false;
$paymentAmount = 0;
$balanceRemaining = 0;

if ($paymentId) {
    // Handle traditional payment ID approach
    $payment = $paymentModel->findWithBookingDetails($paymentId);

    if (!$payment) {
        $message = 'Payment not found.';
    } elseif ($payment['payment_status'] === 'failed') {
        $message = 'Payment failed. Please try again.';
    } elseif ($payment['payment_status'] === 'pending') {
        $successMessage = 'Payment is being processed. You will receive confirmation once completed.';
    } elseif ($payment['payment_status'] === 'completed') {
        $paymentAmount = $payment['amount'];

        if ($payment['booking_id']) {
            // Get full booking details for booking-based payment
            $booking = $bookingModel->findWithDetails($payment['booking_id']);
            $successMessage = 'Payment successful! Your booking has been confirmed.';
        } elseif ($payment['quote_id']) {
            // Get quote details for quote-based payment with payment summary
            $quote = $quoteModel->getQuoteWithPaymentSummary($payment['quote_id']);

            if ($quote) {
                $balanceRemaining = $quote['balance_remaining'];
                $isPartialPayment = $balanceRemaining > 0;

                if ($isPartialPayment) {
                    $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Balance remaining: $" . number_format($balanceRemaining, 2);
                } else {
                    $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Your quote is now fully paid.";
                }

                // Send payment confirmation email
                try {
                    $emailData = [
                        'customer_name' => $quote['customer_name'],
                        'customer_email' => $quote['customer_email'],
                        'quote_reference' => $quote['quote_reference'],
                        'payment_reference' => $payment['payment_reference'],
                        'amount_paid' => $paymentAmount,
                        'quoted_amount' => $quote['quoted_amount'],
                        'total_paid' => $quote['total_paid'],
                        'balance_remaining' => $balanceRemaining,
                        'travel_date' => $quote['travel_date'],
                        'number_of_adults' => $quote['number_of_adults'],
                        'number_of_children' => $quote['number_of_children'],
                        'special_requirements' => $quote['special_requirements']
                    ];

                    if ($isPartialPayment) {
                        // Generate payment link for remaining balance
                        $paymentLink = $paymentModel->generatePaymentLink($quote['quote_id'], $balanceRemaining, 'balance');
                        $emailData['payment_link'] = $paymentLink;
                        $emailService->sendPartialPaymentNotification($emailData);
                    } else {
                        $emailService->sendPaymentReceipt($emailData);
                    }
                } catch (Exception $e) {
                    error_log("Failed to send payment confirmation email: " . $e->getMessage());
                }
            }
        }
    } else {
        $message = 'Payment status unknown. Please contact support.';
    }
} elseif ($quoteRef) {
    // Handle simplified quote reference approach
    $quote = $quoteModel->getQuoteWithPaymentSummary($quoteModel->findByReference($quoteRef)['quote_id'] ?? null);

    if (!$quote) {
        $message = 'Quote not found.';
    } else {
        // Check if there are any completed payments for this quote
        $recentPayments = $paymentModel->getByQuoteId($quote['quote_id']);
        $hasCompletedPayments = false;
        $latestPayment = null;

        foreach ($recentPayments as $recentPayment) {
            if ($recentPayment['payment_status'] === 'completed') {
                $hasCompletedPayments = true;
                if (!$latestPayment || $recentPayment['payment_date'] > $latestPayment['payment_date']) {
                    $latestPayment = $recentPayment;
                }
            }
        }

        if ($hasCompletedPayments && $latestPayment) {
            $paymentAmount = $latestPayment['amount'];
            $balanceRemaining = $quote['balance_remaining'];
            $isPartialPayment = $balanceRemaining > 0;

            if ($isPartialPayment) {
                $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Balance remaining: $" . number_format($balanceRemaining, 2);
            } else {
                $successMessage = "Payment received successfully! You paid $" . number_format($paymentAmount, 2) . ". Your quote is now fully paid.";
            }

            // Send payment confirmation email
            try {
                $emailData = [
                    'customer_name' => $quote['customer_name'],
                    'customer_email' => $quote['customer_email'],
                    'quote_reference' => $quote['quote_reference'],
                    'payment_reference' => $latestPayment['payment_reference'],
                    'amount_paid' => $paymentAmount,
                    'quoted_amount' => $quote['quoted_amount'],
                    'total_paid' => $quote['total_paid'],
                    'balance_remaining' => $balanceRemaining,
                    'travel_date' => $quote['travel_date'],
                    'number_of_adults' => $quote['number_of_adults'],
                    'number_of_children' => $quote['number_of_children'],
                    'special_requirements' => $quote['special_requirements']
                ];

                if ($isPartialPayment) {
                    // Generate payment link for remaining balance
                    $paymentLink = $paymentModel->generatePaymentLink($quote['quote_id'], $balanceRemaining, 'balance');
                    $emailData['payment_link'] = $paymentLink;
                    $emailService->sendPartialPaymentNotification($emailData);
                } else {
                    $emailService->sendPaymentReceipt($emailData);
                }
            } catch (Exception $e) {
                error_log("Failed to send payment confirmation email: " . $e->getMessage());
            }
        } else {
            $message = 'Payment has not been completed yet. Please try again or contact support.';
        }
    }
} else {
    $message = 'Payment ID or quote reference is required.';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Payment Successful - Meleva Tours & Travel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-50">
    <?php include 'header.php'; ?>

    <!-- Success Section -->
    <section class="py-16">
        <div class="max-w-4xl mx-auto px-6">
            
            <?php if ($message): ?>
                <div class="text-center">
                    <div class="bg-red-100 text-red-800 border border-red-200 p-6 rounded-lg">
                        <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                        <h2 class="text-2xl font-bold mb-2">Error</h2>
                        <p><?php echo htmlspecialchars($message); ?></p>
                        <div class="mt-6">
                            <a href="index.php" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                                <i class="fas fa-home mr-2"></i>Return Home
                            </a>
                        </div>
                    </div>
                </div>
            <?php elseif ($successMessage): ?>
                <!-- Success Message with Custom Text -->
                <div class="text-center mb-8">
                    <div class="bg-green-100 text-green-800 border border-green-200 p-8 rounded-lg">
                        <i class="fas fa-check-circle text-6xl mb-6 text-green-600"></i>
                        <h1 class="text-4xl font-bold mb-4">
                            <?php echo $isPartialPayment ? 'Deposit Received!' : 'Payment Successful!'; ?>
                        </h1>
                        <p class="text-xl mb-6"><?php echo htmlspecialchars($successMessage); ?></p>

                        <?php if ($isPartialPayment): ?>
                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                                <p class="text-orange-800">
                                    <i class="fas fa-info-circle mr-2"></i>
                                    You can make additional payments anytime to complete your booking.
                                </p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php else: ?>
                <!-- Payment Details -->
                <div class="bg-white p-6 rounded-lg shadow-sm mb-8">
                            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Payment Details</h2>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                                <div>
                                    <?php if ($payment): ?>
                                        <p><strong>Payment Reference:</strong> <?php echo htmlspecialchars($payment['payment_reference']); ?></p>
                                        <p><strong>Reference:</strong> <?php echo htmlspecialchars($payment['booking_reference'] ?? $quote['quote_reference']); ?></p>
                                        <p><strong>This Payment:</strong> $<?php echo number_format($payment['amount'], 2); ?></p>
                                        <?php if ($quote): ?>
                                            <p><strong>Quote Total:</strong> $<?php echo number_format($quote['quoted_amount'], 2); ?></p>
                                            <p><strong>Total Paid:</strong> $<?php echo number_format($quote['total_paid'] ?? $payment['amount'], 2); ?></p>
                                            <?php if (isset($quote['balance_remaining']) && $quote['balance_remaining'] > 0): ?>
                                                <p class="text-orange-600"><strong>Balance Remaining:</strong> $<?php echo number_format($quote['balance_remaining'], 2); ?></p>
                                            <?php else: ?>
                                                <p class="text-green-600"><strong>Status:</strong> Fully Paid</p>
                                            <?php endif; ?>
                                        <?php endif; ?>
                                    <?php elseif ($quote): ?>
                                        <p><strong>Quote Reference:</strong> <?php echo htmlspecialchars($quote['quote_reference']); ?></p>
                                        <p><strong>Amount Paid:</strong> $<?php echo number_format($quote['quoted_amount'], 2); ?></p>
                                    <?php endif; ?>
                                </div>
                                <div>
                                    <?php if ($payment): ?>
                                        <p><strong>Payment Type:</strong> <?php echo ucfirst($payment['payment_type']); ?> Payment</p>
                                        <p><strong>Payment Date:</strong> <?php echo date('F j, Y g:i A', strtotime($payment['payment_date'])); ?></p>
                                    <?php endif; ?>
                                    <p><strong>Status:</strong> <span class="text-green-600 font-semibold">Completed</span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($booking): ?>
                    <!-- Booking Summary -->
                    <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-6">Booking Summary</h2>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-3">Customer Information</h3>
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($booking['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($booking['customer_email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($booking['customer_phone']); ?></p>
                                <?php if ($booking['customer_country']): ?>
                                    <p><strong>Country:</strong> <?php echo htmlspecialchars($booking['customer_country']); ?></p>
                                <?php endif; ?>
                            </div>
                            
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-3">Travel Details</h3>
                                <p><strong>Travel Date:</strong> <?php echo date('F j, Y', strtotime($booking['travel_date'])); ?></p>
                                <p><strong>Adults:</strong> <?php echo $booking['number_of_adults']; ?></p>
                                <p><strong>Children:</strong> <?php echo $booking['number_of_children']; ?></p>
                                <p><strong>Booking Status:</strong> <span class="text-green-600 font-semibold"><?php echo ucfirst($booking['booking_status']); ?></span></p>
                            </div>
                        </div>
                        
                        <?php if (!empty($booking['package_names'])): ?>
                            <div class="border-t pt-6">
                                <h3 class="font-semibold text-gray-700 mb-3">Selected Packages</h3>
                                <p class="text-gray-600"><?php echo htmlspecialchars($booking['package_names']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($booking['special_requirements']): ?>
                            <div class="border-t pt-6">
                                <h3 class="font-semibold text-gray-700 mb-3">Special Requirements</h3>
                                <p class="text-gray-600"><?php echo htmlspecialchars($booking['special_requirements']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="border-t pt-6">
                            <div class="flex justify-between items-center">
                                <div>
                                    <p class="text-lg"><strong>Total Booking Amount:</strong> $<?php echo number_format($booking['total_amount'], 2); ?></p>
                                    <p class="text-lg"><strong>Amount Paid:</strong> $<?php echo number_format($booking['paid_amount'] ?? 0, 2); ?></p>
                                    <?php 
                                    $remainingBalance = $booking['total_amount'] - ($booking['paid_amount'] ?? 0);
                                    if ($remainingBalance > 0): 
                                    ?>
                                        <p class="text-lg text-orange-600"><strong>Remaining Balance:</strong> $<?php echo number_format($remainingBalance, 2); ?></p>
                                    <?php endif; ?>
                                </div>
                                <div class="text-right">
                                    <span class="text-sm text-gray-600">Payment Status:</span><br>
                                    <span class="text-lg font-semibold <?php echo $booking['payment_status'] === 'paid' ? 'text-green-600' : 'text-orange-600'; ?>">
                                        <?php echo ucfirst($booking['payment_status']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($quote): ?>
                    <!-- Quote Summary -->
                    <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                        <h2 class="text-2xl font-bold text-gray-800 mb-6">Travel Quote Summary</h2>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h3 class="font-semibold text-gray-700 mb-3">Customer Information</h3>
                                <p><strong>Name:</strong> <?php echo htmlspecialchars($quote['customer_name']); ?></p>
                                <p><strong>Email:</strong> <?php echo htmlspecialchars($quote['customer_email']); ?></p>
                                <p><strong>Phone:</strong> <?php echo htmlspecialchars($quote['customer_phone']); ?></p>
                                <?php if ($quote['customer_country']): ?>
                                    <p><strong>Country:</strong> <?php echo htmlspecialchars($quote['customer_country']); ?></p>
                                <?php endif; ?>
                            </div>

                            <div>
                                <h3 class="font-semibold text-gray-700 mb-3">Travel Details</h3>
                                <p><strong>Travel Date:</strong>
                                    <?php echo $quote['travel_date'] ? date('F j, Y', strtotime($quote['travel_date'])) : 'To be determined'; ?>
                                </p>
                                <p><strong>Travelers:</strong>
                                    <?php echo $quote['number_of_adults']; ?> Adult(s)
                                    <?php if ($quote['number_of_children'] > 0): ?>
                                        , <?php echo $quote['number_of_children']; ?> Child(ren)
                                    <?php endif; ?>
                                </p>
                                <p><strong>Quote Status:</strong> <span class="text-green-600 font-semibold">Paid</span></p>
                            </div>
                        </div>

                        <?php if (!empty($quote['special_requirements'])): ?>
                            <div class="border-t pt-6">
                                <h3 class="font-semibold text-gray-700 mb-3">Special Requirements</h3>
                                <p class="text-gray-600"><?php echo nl2br(htmlspecialchars($quote['special_requirements'])); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <!-- Next Steps -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-8 mb-8">
                    <h2 class="text-2xl font-bold text-blue-800 mb-4">
                        <i class="fas fa-info-circle mr-2"></i>What's Next?
                    </h2>
                    <div class="space-y-3 text-blue-700">
                        <p><i class="fas fa-envelope mr-2"></i>You will receive a confirmation email with your booking details and receipt.</p>
                        <p><i class="fas fa-phone mr-2"></i>Our team will contact you within 24 hours to confirm your travel arrangements.</p>
                        <?php if (isset($remainingBalance) && $remainingBalance > 0): ?>
                            <p><i class="fas fa-credit-card mr-2"></i>The remaining balance of $<?php echo number_format($remainingBalance, 2); ?> is due before your travel date.</p>
                        <?php endif; ?>
                        <p><i class="fas fa-file-alt mr-2"></i>Please keep your reference <strong><?php echo htmlspecialchars($payment['booking_reference'] ?? $quote['quote_reference']); ?></strong> for future correspondence.</p>
                    </div>
                </div>

                <!-- Additional Payment Option for Partial Payments -->
                <?php if ($isPartialPayment && $balanceRemaining > 0): ?>
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-8">
                        <h3 class="text-xl font-bold text-orange-800 mb-4">
                            <i class="fas fa-credit-card mr-2"></i>Complete Your Payment
                        </h3>
                        <p class="text-orange-700 mb-4">
                            You have a remaining balance of <strong>$<?php echo number_format($balanceRemaining, 2); ?></strong>.
                            You can make an additional payment anytime to complete your booking.
                        </p>
                        <div class="text-center">
                            <a href="payment.php?quote_ref=<?php echo urlencode($quote['quote_reference'] ?? ''); ?>"
                               class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                                <i class="fas fa-plus mr-2"></i>Make Additional Payment
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Action Buttons -->
                <div class="text-center space-y-4">
                    <div class="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
                        <button onclick="window.print()" 
                                class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300">
                            <i class="fas fa-print mr-2"></i>Print Receipt
                        </button>
                        
                        <a href="contact.php" 
                           class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                            <i class="fas fa-headset mr-2"></i>Contact Support
                        </a>
                        
                        <a href="index.php" 
                           class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 inline-block">
                            <i class="fas fa-home mr-2"></i>Return Home
                        </a>
                    </div>
                    
                    <p class="text-sm text-gray-600 mt-6">
                        Need help? Contact us at 
                        <a href="mailto:<EMAIL>" class="text-orange-600 hover:text-orange-700 font-medium"><EMAIL></a> 
                        or call us at 
                        <a href="tel:+254123456789" class="text-orange-600 hover:text-orange-700 font-medium">+254 123 456 789</a>
                    </p>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <?php include 'footer.php'; ?>

    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                background: white !important;
            }
            
            .bg-green-100, .bg-blue-50 {
                background: white !important;
                border: 1px solid #ccc !important;
            }
        }
    </style>
</body>
</html>
