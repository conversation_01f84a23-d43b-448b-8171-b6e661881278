<?php
// Define admin access constant
define('ADMIN_ACCESS', true);

// Include security middleware
require_once 'includes/security_middleware.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';

// Initialize models
$paymentModel = new Payment();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle manual payment status updates
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $paymentId = $_POST['payment_id'] ?? null;
    $action = $_POST['action'];

    if ($paymentId && $action === 'mark_paid') {
        try {
            // Update payment status to completed
            $updated = $paymentModel->updatePaymentStatus($paymentId, 'completed', 'ADMIN_VERIFIED', 'Manual Verification');

            if ($updated) {
                // Get payment details to update quote status if needed
                $payment = $paymentModel->findById($paymentId);
                if ($payment && $payment['quote_id']) {
                    // Update quote payment status (this calculates total_paid and updates status)
                    $quoteModel = new Quote();
                    $quoteModel->updatePaymentStatus($payment['quote_id']);
                }

                $message = 'Payment status updated to paid successfully.';
                $messageType = 'success';
            } else {
                $message = 'Failed to update payment status.';
                $messageType = 'error';
            }
        } catch (Exception $e) {
            error_log("Manual payment update error: " . $e->getMessage());
            $message = 'An error occurred while updating payment status.';
            $messageType = 'error';
        }
    }
}

// Get filter parameters
$statusFilter = $_GET['status'] ?? '';
$searchFilter = $_GET['search'] ?? '';
$dateFromFilter = $_GET['date_from'] ?? '';
$dateToFilter = $_GET['date_to'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$limit = 20;
$offset = ($page - 1) * $limit;

// Build filters array
$filters = [];
if ($statusFilter) $filters['status'] = $statusFilter;
if ($searchFilter) $filters['search'] = $searchFilter;
if ($dateFromFilter) $filters['date_from'] = $dateFromFilter;
if ($dateToFilter) $filters['date_to'] = $dateToFilter;

// Fetch payments with filters
$payments = $paymentModel->findAllWithDetails($limit, $offset, $filters);
$totalPayments = $paymentModel->countWithFilters($filters);

// Calculate pagination
$totalPages = ceil($totalPayments / $limit);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote Payments - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>

<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>

    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>

    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="mobile-menu-btn" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Quote Payments</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6">
            <div class="max-w-7xl mx-auto">

                <!-- Message Display -->
                <?php if ($message): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
                        <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <!-- Payment Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <?php
                    // Calculate payment statistics
                    $allPayments = $paymentModel->findAll();
                    $totalAmount = 0;
                    $completedAmount = 0;
                    $pendingAmount = 0;
                    $failedCount = 0;

                    foreach ($allPayments as $p) {
                        $totalAmount += $p['amount'];
                        if ($p['payment_status'] === 'completed') {
                            $completedAmount += $p['amount'];
                        } elseif ($p['payment_status'] === 'pending') {
                            $pendingAmount += $p['amount'];
                        } elseif ($p['payment_status'] === 'failed') {
                            $failedCount++;
                        }
                    }
                    ?>

                    <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-green-500">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-money-bill-wave text-xl text-green-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Revenue</p>
                                <p class="text-2xl font-semibold text-gray-900">$<?php echo number_format($completedAmount, 2); ?></p>
                                <p class="text-xs text-green-600 mt-1">Completed payments</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-yellow-500">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-clock text-xl text-yellow-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Pending Payments</p>
                                <p class="text-2xl font-semibold text-gray-900">$<?php echo number_format($pendingAmount, 2); ?></p>
                                <p class="text-xs text-yellow-600 mt-1">Awaiting processing</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-blue-500">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-chart-line text-xl text-blue-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Total Transactions</p>
                                <p class="text-2xl font-semibold text-gray-900"><?php echo count($allPayments); ?></p>
                                <p class="text-xs text-blue-600 mt-1">All payment attempts</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg shadow-sm p-6 border-l-4 border-red-500">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-times-circle text-xl text-red-600"></i>
                                </div>
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-gray-500">Failed Payments</p>
                                <p class="text-2xl font-semibold text-gray-900"><?php echo $failedCount; ?></p>
                                <p class="text-xs text-red-600 mt-1">Unsuccessful attempts</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
                    <form method="GET" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <!-- Status Filter -->
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" id="status" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                    <option value="">All Statuses</option>
                                    <option value="pending" <?php echo $statusFilter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="completed" <?php echo $statusFilter === 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="failed" <?php echo $statusFilter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                                    <option value="cancelled" <?php echo $statusFilter === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>

                            <!-- Search Filter -->
                            <div>
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                <input type="text"
                                       name="search"
                                       id="search"
                                       value="<?php echo htmlspecialchars($searchFilter); ?>"
                                       placeholder="Customer name, quote reference, or payment reference"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>

                            <!-- Date From Filter -->
                            <div>
                                <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">From Date</label>
                                <input type="date"
                                       name="date_from"
                                       id="date_from"
                                       value="<?php echo htmlspecialchars($dateFromFilter); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>

                            <!-- Date To Filter -->
                            <div>
                                <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">To Date</label>
                                <input type="date"
                                       name="date_to"
                                       id="date_to"
                                       value="<?php echo htmlspecialchars($dateToFilter); ?>"
                                       class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                            </div>
                        </div>

                        <!-- Filter Actions -->
                        <div class="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
                            <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg font-medium transition duration-300 flex items-center">
                                <i class="fas fa-search mr-2"></i>Apply Filters
                            </button>

                            <a href="payments.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg font-medium transition duration-300 flex items-center">
                                <i class="fas fa-times mr-2"></i>Clear All
                            </a>

                            <!-- Quick Status Filters -->
                            <div class="flex flex-wrap items-center gap-2 ml-auto">
                                <span class="text-sm text-gray-600 mr-2">Quick filters:</span>
                                <a href="?status=pending" class="inline-flex items-center justify-center bg-yellow-100 text-yellow-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-yellow-200 transition duration-300 min-w-[80px]">
                                    Pending
                                </a>
                                <a href="?status=completed" class="inline-flex items-center justify-center bg-green-100 text-green-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-green-200 transition duration-300 min-w-[100px]">
                                    Completed
                                </a>
                                <a href="?status=failed" class="inline-flex items-center justify-center bg-red-100 text-red-800 px-4 py-2 rounded-full text-sm font-medium hover:bg-red-200 transition duration-300 min-w-[80px]">
                                    Failed
                                </a>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Filter Summary -->
                <?php if ($statusFilter || $searchFilter || $dateFromFilter || $dateToFilter): ?>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                    <div class="flex flex-wrap items-center gap-3">
                        <span class="text-sm font-medium text-blue-800">
                            <i class="fas fa-filter mr-1"></i>Active Filters:
                        </span>

                        <?php if ($statusFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Status: <?php echo ucfirst($statusFilter); ?>
                            <a href="<?php echo http_build_query(array_merge($_GET, ['status' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <?php if ($searchFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Search: "<?php echo htmlspecialchars($searchFilter); ?>"
                            <a href="<?php echo http_build_query(array_merge($_GET, ['search' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <?php if ($dateFromFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            From: <?php echo htmlspecialchars($dateFromFilter); ?>
                            <a href="<?php echo http_build_query(array_merge($_GET, ['date_from' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <?php if ($dateToFilter): ?>
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            To: <?php echo htmlspecialchars($dateToFilter); ?>
                            <a href="<?php echo http_build_query(array_merge($_GET, ['date_to' => ''])); ?>" class="ml-2 text-blue-600 hover:text-blue-800">
                                <i class="fas fa-times"></i>
                            </a>
                        </span>
                        <?php endif; ?>

                        <span class="text-sm text-blue-700 ml-auto">
                            Showing <?php echo count($payments); ?> of <?php echo $totalPayments; ?> payments
                        </span>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Payments Table -->
                <div class="bg-white rounded-lg shadow-sm overflow-hidden">
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment Details</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quote Reference</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($payments)): ?>
                                    <tr>
                                        <td colspan="6" class="px-6 py-12 text-center text-gray-500">
                                            <i class="fas fa-credit-card text-4xl mb-4"></i>
                                            <p class="text-lg">No payments found</p>
                                            <p class="text-sm">Quote payments will appear here once customers make payments</p>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($payments as $payment): ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($payment['payment_reference']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo ucfirst($payment['payment_type']); ?> Payment</div>
                                                    <?php if ($payment['pesapal_tracking_id']): ?>
                                                        <div class="text-xs text-gray-400">Pesapal: <?php echo htmlspecialchars($payment['pesapal_tracking_id']); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($payment['quote_reference'] ?? 'N/A'); ?>
                                                </div>
                                            </td>

                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm font-medium text-gray-900">
                                                        <?php echo htmlspecialchars($payment['customer_name'] ?? 'N/A'); ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        <?php echo htmlspecialchars($payment['customer_email'] ?? 'N/A'); ?>
                                                    </div>
                                                    <?php if (!empty($payment['customer_phone'])): ?>
                                                        <div class="text-xs text-gray-400">
                                                            <?php echo htmlspecialchars($payment['customer_phone']); ?>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    $<?php echo number_format($payment['amount'], 2); ?>
                                                </div>
                                                <?php if ($payment['payment_method']): ?>
                                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($payment['payment_method']); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    <?php 
                                                    switch($payment['payment_status']) {
                                                        case 'completed': echo 'bg-green-100 text-green-800'; break;
                                                        case 'failed': echo 'bg-red-100 text-red-800'; break;
                                                        case 'cancelled': echo 'bg-gray-100 text-gray-800'; break;
                                                        default: echo 'bg-yellow-100 text-yellow-800';
                                                    }
                                                    ?>">
                                                    <?php echo ucfirst($payment['payment_status']); ?>
                                                </span>
                                                <?php if ($payment['pesapal_status']): ?>
                                                    <div class="text-xs text-gray-500 mt-1"><?php echo htmlspecialchars($payment['pesapal_status']); ?></div>
                                                <?php endif; ?>
                                            </td>
                                            
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div>
                                                    <div class="text-sm text-gray-900">
                                                        <?php
                                                        if ($payment['payment_date']) {
                                                            $paymentDate = new DateTime($payment['payment_date'], new DateTimeZone('UTC'));
                                                            $paymentDate->setTimezone(new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                            echo $paymentDate->format('M j, Y');
                                                        } else {
                                                            echo 'N/A';
                                                        }
                                                        ?>
                                                    </div>
                                                    <div class="text-sm text-gray-500">
                                                        <?php
                                                        if ($payment['payment_date']) {
                                                            $paymentDate = new DateTime($payment['payment_date'], new DateTimeZone('UTC'));
                                                            $paymentDate->setTimezone(new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                            echo $paymentDate->format('g:i A');
                                                        } else {
                                                            $createdDate = new DateTime($payment['created_at'], new DateTimeZone('UTC'));
                                                            $createdDate->setTimezone(new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                            echo $createdDate->format('M j, Y');
                                                        }
                                                        ?>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Actions Column -->
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <?php if ($payment['payment_status'] === 'pending'): ?>
                                                    <form method="POST" style="display: inline;" onsubmit="return confirm('Are you sure you want to mark this payment as paid? This action cannot be undone.');">
                                                        <input type="hidden" name="payment_id" value="<?php echo $payment['payment_id']; ?>">
                                                        <input type="hidden" name="action" value="mark_paid">
                                                        <?php echo csrf_field(); ?>
                                                        <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded text-xs font-medium transition duration-300">
                                                            <i class="fas fa-check mr-1"></i>
                                                            Mark Paid
                                                        </button>
                                                    </form>
                                                <?php elseif ($payment['payment_status'] === 'completed'): ?>
                                                    <span class="text-green-600 text-xs font-medium">
                                                        <i class="fas fa-check-circle mr-1"></i>
                                                        Verified
                                                    </span>
                                                <?php else: ?>
                                                    <span class="text-gray-400 text-xs">
                                                        No action
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Enhanced Pagination -->
                    <?php if ($totalPayments > $limit): ?>
                        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow-sm">
                            <div class="flex-1 flex justify-between sm:hidden">
                                <?php if ($page > 1): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        Previous
                                    </a>
                                <?php endif; ?>
                                <?php if ($page < $totalPages): ?>
                                    <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        Next
                                    </a>
                                <?php endif; ?>
                            </div>
                            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p class="text-sm text-gray-700">
                                        Showing <span class="font-medium"><?php echo $offset + 1; ?></span> to
                                        <span class="font-medium"><?php echo min($offset + $limit, $totalPayments); ?></span> of
                                        <span class="font-medium"><?php echo $totalPayments; ?></span> payments
                                    </p>
                                </div>
                                <div>
                                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                        <?php if ($page > 1): ?>
                                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>"
                                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php
                                        $startPage = max(1, $page - 2);
                                        $endPage = min($totalPages, $page + 2);

                                        if ($startPage > 1): ?>
                                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => 1])); ?>"
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                                            <?php if ($startPage > 2): ?>
                                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                                            <?php if ($i == $page): ?>
                                                <span class="relative inline-flex items-center px-4 py-2 border border-orange-500 bg-orange-50 text-sm font-medium text-orange-600"><?php echo $i; ?></span>
                                            <?php else: ?>
                                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>"
                                                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $i; ?></a>
                                            <?php endif; ?>
                                        <?php endfor; ?>

                                        <?php if ($endPage < $totalPages): ?>
                                            <?php if ($endPage < $totalPages - 1): ?>
                                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                                            <?php endif; ?>
                                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $totalPages])); ?>"
                                               class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?php echo $totalPages; ?></a>
                                        <?php endif; ?>

                                        <?php if ($page < $totalPages): ?>
                                            <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>"
                                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        <?php endif; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>


            </div>
        </div>
    </div>

    <style>
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .sidebar-overlay { position: fixed; top: 0; left: 0; w-full; h-full; background: rgba(0, 0, 0, 0.5); z-index: 40; display: none; }
        .main-content { transition: margin-left 0.3s ease-in-out; }
        .sidebar-active { background-color: rgba(249, 115, 22, 0.1); border-right: 3px solid #f97316; }

        @media (max-width: 1024px) {
            #sidebar { transform: translateX(-100%); }
            #sidebar.show { transform: translateX(0); }
            .sidebar-overlay.show { display: block; }
        }
    </style>

    <script>
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            if (mobileMenuBtn) {
                mobileMenuBtn.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                    sidebarOverlay.classList.toggle('show');
                });
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', function() {
                    sidebar.classList.remove('show');
                    sidebarOverlay.classList.remove('show');
                });
            }
        });

        // Enhanced Filter Functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search');
            const statusSelect = document.getElementById('status');
            const dateFromInput = document.getElementById('date_from');
            const dateToInput = document.getElementById('date_to');

            // Auto-submit on status change
            if (statusSelect) {
                statusSelect.addEventListener('change', function() {
                    if (this.value) {
                        this.form.submit();
                    }
                });
            }

            // Search on Enter key
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.form.submit();
                    }
                });
            }

            // Auto-submit when date range is complete
            function checkDateRange() {
                if (dateFromInput && dateToInput && dateFromInput.value && dateToInput.value) {
                    dateFromInput.form.submit();
                }
            }

            if (dateFromInput) dateFromInput.addEventListener('change', checkDateRange);
            if (dateToInput) dateToInput.addEventListener('change', checkDateRange);

            // Highlight search terms in results
            const searchTerm = '<?php echo addslashes($searchFilter); ?>';
            if (searchTerm) {
                const regex = new RegExp(`(${searchTerm})`, 'gi');
                document.querySelectorAll('td').forEach(cell => {
                    if (cell.textContent.toLowerCase().includes(searchTerm.toLowerCase())) {
                        cell.innerHTML = cell.innerHTML.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
                    }
                });
            }
        });
    </script>
</body>
</html>
