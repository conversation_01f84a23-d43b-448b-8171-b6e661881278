<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';
require_once 'classes/ImageManager.php';

// Require authentication
Auth::requireLogin();

$imageModel = new Image();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'upload':
                try {
                    if (!isset($_FILES['gallery_images']) || empty($_FILES['gallery_images']['name'][0])) {
                        throw new Exception('Please select at least one image to upload');
                    }

                    $title = Utils::sanitizeInput($_POST['title']);
                    $altText = Utils::sanitizeInput($_POST['alt_text']);
                    $displayImageIndex = isset($_POST['display_image_index']) && $_POST['display_image_index'] !== ''
                        ? intval($_POST['display_image_index']) : null;

                    if (empty($title) || empty($altText)) {
                        throw new Exception('Title and Alt Text are required');
                    }

                    $imageManager = new ImageManager();
                    $result = $imageManager->processGalleryUploads(
                        $_FILES['gallery_images'],
                        $currentUser['user_id'],
                        $title,
                        $altText,
                        '', // No description
                        'main_gallery',
                        $displayImageIndex
                    );

                    if ($result['success']) {
                        $success = "Successfully uploaded " . count($result['uploaded_images']) . " image(s)!";
                        if (!empty($result['errors'])) {
                            $success .= " Some files had issues: " . implode(', ', $result['errors']);
                        }
                    } else {
                        $error = "Upload failed: " . implode(', ', $result['errors']);
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;

            case 'update':
                try {
                    $imageId = intval($_POST['image_id']);
                    $data = [
                        'alt_text' => Utils::sanitizeInput($_POST['title']) . ' - ' . Utils::sanitizeInput($_POST['alt_text'])
                    ];

                    if ($imageModel->update($imageId, $data)) {
                        $success = 'Image updated successfully!';
                    } else {
                        $error = 'Failed to update image.';
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;

            case 'delete':
                try {
                    $imageId = intval($_POST['image_id']);
                    $imageManager = new ImageManager();
                    $result = $imageManager->deleteImageById($imageId);

                    if ($result['success']) {
                        $success = 'Image deleted successfully!';
                    } else {
                        $error = $result['error'];
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;

            case 'toggle_display':
                try {
                    $imageId = intval($_POST['image_id']);
                    $currentStatus = intval($_POST['current_status']);
                    
                    // Check current display count
                    $displayCount = $imageModel->getDisplayImageCount('main_gallery');
                    
                    if ($currentStatus == 0) {
                        // Trying to set as display image
                        if ($displayCount >= 20) {
                            throw new Exception('Maximum of 20 images can be set for display. Please unselect another image first.');
                        }
                        $newStatus = 1;
                        $message = 'Image set for display in gallery!';
                    } else {
                        // Removing from display
                        $newStatus = 0;
                        $message = 'Image removed from gallery display!';
                    }

                    if ($imageModel->update($imageId, ['is_display_image' => $newStatus])) {
                        $success = $message;
                    } else {
                        $error = 'Failed to update display status.';
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;

            case 'bulk_delete':
                try {
                    if (!isset($_POST['selected_images']) || empty($_POST['selected_images'])) {
                        throw new Exception('No images selected for deletion.');
                    }

                    $selectedImages = $_POST['selected_images'];
                    $imageManager = new ImageManager();
                    $deletedCount = 0;
                    $errors = [];

                    foreach ($selectedImages as $imageId) {
                        $imageId = intval($imageId);
                        $result = $imageManager->deleteImageById($imageId);

                        if ($result['success']) {
                            $deletedCount++;
                        } else {
                            $errors[] = "Failed to delete image ID {$imageId}: " . $result['error'];
                        }
                    }

                    if ($deletedCount > 0) {
                        $success = "Successfully deleted {$deletedCount} image(s).";
                        if (!empty($errors)) {
                            $success .= " However, some deletions failed.";
                        }
                    }

                    if (!empty($errors)) {
                        $error = implode('<br>', $errors);
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;

            case 'bulk_toggle_display':
                try {
                    if (!isset($_POST['selected_images']) || empty($_POST['selected_images'])) {
                        throw new Exception('No images selected.');
                    }

                    $selectedImages = $_POST['selected_images'];
                    $action = $_POST['bulk_action'] ?? 'show'; // 'show' or 'hide'
                    $updatedCount = 0;
                    $errors = [];

                    // Check current display count if we're trying to show images
                    if ($action === 'show') {
                        $currentDisplayCount = $imageModel->getDisplayImageCount('main_gallery');
                        $newDisplayCount = $currentDisplayCount + count($selectedImages);

                        if ($newDisplayCount > 20) {
                            $maxAllowed = 20 - $currentDisplayCount;
                            throw new Exception("Cannot display all selected images. Maximum 20 images allowed for display. You can add {$maxAllowed} more image(s).");
                        }
                    }

                    $newStatus = ($action === 'show') ? 1 : 0;

                    foreach ($selectedImages as $imageId) {
                        $imageId = intval($imageId);

                        if ($imageModel->update($imageId, ['is_display_image' => $newStatus])) {
                            $updatedCount++;
                        } else {
                            $errors[] = "Failed to update image ID {$imageId}";
                        }
                    }

                    if ($updatedCount > 0) {
                        $actionText = ($action === 'show') ? 'set for display' : 'hidden from display';
                        $success = "Successfully {$actionText} {$updatedCount} image(s).";
                    }

                    if (!empty($errors)) {
                        $error = implode('<br>', $errors);
                    }
                } catch (Exception $e) {
                    $error = $e->getMessage();
                }
                break;


        }
    }
}

// Get search and filter parameters
$searchTerm = isset($_GET['search']) ? Utils::sanitizeInput($_GET['search']) : '';
$displayFilter = isset($_GET['display']) ? Utils::sanitizeInput($_GET['display']) : 'all';

// Get images with filters
$images = $imageModel->findGalleryImages($searchTerm, $displayFilter);

// Get statistics
$imageStats = $imageModel->getImageStats();
$displayCount = $imageModel->getDisplayImageCount('main_gallery');

// Get image for editing if ID is provided
$editImage = null;
if (isset($_GET['edit'])) {
    $editImage = $imageModel->findById(intval($_GET['edit']));
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gallery Images - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .sidebar-transition {
            transition: all 0.3s ease;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .sidebar-active {
            background-color: rgba(249, 115, 22, 0.1);
            border-right: 3px solid #f97316;
        }

        /* Image Upload Styles */
        .upload-zone {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        
        .upload-zone.dragover {
            border-color: #f97316;
            background-color: rgba(249, 115, 22, 0.05);
        }
        
        .image-preview {
            position: relative;
            overflow: hidden;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .image-preview:hover {
            transform: scale(1.02);
        }
        
        .image-preview img {
            width: 100%;
            height: 120px;
            object-fit: cover;
        }
        
        .image-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .image-preview:hover .image-overlay {
            opacity: 1;
        }
        
        .display-image-indicator {
            position: absolute;
            top: 8px;
            left: 8px;
            background: #10b981;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .display-limit-warning {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: #92400e;
        }

        .display-limit-reached {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
        }

        /* Form Styles */
        .form-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
        }

        .form-header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 12px 12px 0 0;
            margin: -1px -1px 0 -1px;
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.expanded {
            max-height: 2000px;
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Gallery Images</h1>
            </div>
        </div>
        <!-- Content Area -->
        <div class="p-6">
            <!-- Page Description -->
            <div class="mb-6">
                <p class="text-gray-600">Manage main gallery images</p>
            </div>

            <?php if (isset($success)): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-check-circle mr-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <?php if (isset($error)): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>

            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-images text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Total Images</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $imageStats['main_gallery_count']; ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-eye text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Display Images</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo $displayCount; ?><span class="text-sm text-gray-500">/20</span></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-upload text-orange-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Available Slots</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo max(0, 20 - $displayCount); ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-percentage text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-500">Display Rate</p>
                            <p class="text-2xl font-bold text-gray-900">
                                <?php echo $imageStats['main_gallery_count'] > 0 ? round(($displayCount / $imageStats['main_gallery_count']) * 100) : 0; ?>%
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Display Limit Warning -->
            <?php if ($displayCount >= 18): ?>
                <div class="mb-6 <?php echo $displayCount >= 20 ? 'display-limit-reached' : 'display-limit-warning'; ?> px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <?php if ($displayCount >= 20): ?>
                            <span class="font-medium">Display limit reached! You have selected the maximum of 20 images for gallery display.</span>
                        <?php else: ?>
                            <span class="font-medium">Approaching display limit! You can select <?php echo 20 - $displayCount; ?> more image(s) for gallery display.</span>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>



            <!-- Upload Form -->
            <div class="form-section mb-6">
                <div class="form-header cursor-pointer" id="toggleUploadHeader">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-semibold flex items-center">
                            <i class="fas fa-cloud-upload-alt mr-2"></i>Upload New Images
                        </h3>
                        <div class="text-white hover:text-orange-200 transition-colors">
                            <i class="fas fa-chevron-down transform transition-transform" id="uploadToggleIcon"></i>
                        </div>
                    </div>
                </div>
                
                <div id="uploadFormContent" class="collapsible-content">
                    <div class="p-6">
                        <form id="uploadForm" method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="upload">
                            <input type="hidden" name="display_image_index" id="displayImageIndex" value="">
                            
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <!-- Left Column - Form Fields -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Image Title *</label>
                                        <input type="text" name="title" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Enter a descriptive title">
                                    </div>
                                    
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Alt Text *</label>
                                        <input type="text" name="alt_text" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500" placeholder="Describe the image for accessibility">
                                    </div>
                                    

                                    
                                    <div class="bg-blue-50 p-4 rounded-lg">
                                        <h4 class="text-sm font-medium text-blue-900 mb-2">
                                            <i class="fas fa-info-circle mr-1"></i>Upload Guidelines
                                        </h4>
                                        <ul class="text-sm text-blue-800 space-y-1">
                                            <li>• Upload as many images as you want</li>
                                            <li>• Only 20 images can be displayed in the gallery</li>
                                            <li>• Images are auto-compressed to max 5MB</li>
                                            <li>• Supported formats: PNG, JPG, GIF, WebP</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <!-- Right Column - Image Upload -->
                                <div class="space-y-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Select Images *</label>
                                        
                                        <!-- Upload Zone -->
                                        <div class="upload-zone border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-orange-500 transition-colors">
                                            <div class="space-y-2">
                                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                                                <p class="text-sm text-gray-600">
                                                    <span class="font-medium text-orange-600">Click to upload</span> or drag and drop
                                                </p>
                                                <p class="text-xs text-gray-500">PNG, JPG, GIF, WebP (auto-compressed to max 5MB)</p>
                                            </div>
                                            <input type="file" id="imageInput" name="gallery_images[]" multiple accept="image/*" class="hidden">
                                        </div>
                                        
                                        <!-- Compression Progress -->
                                        <div id="compressionProgress" class="hidden mt-2">
                                            <div class="bg-gray-200 rounded-full h-2">
                                                <div id="progressBar" class="bg-orange-500 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                                            </div>
                                            <p class="text-xs text-gray-600 mt-1">Compressing images...</p>
                                        </div>
                                    </div>
                                    
                                    <!-- Image Previews -->
                                    <div id="imagePreviewContainer" class="space-y-4">
                                        <div id="imagePreviewGrid" class="grid grid-cols-2 gap-3"></div>
                                    </div>
                                    
                                    <!-- Upload Summary -->
                                    <div id="uploadSummary" class="hidden bg-green-50 p-3 rounded-lg">
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-green-700">Total files: <span id="totalFiles">0</span></span>
                                            <span class="text-green-700">Total size: <span id="totalSize">0 KB</span></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                                <button type="button" onclick="resetUploadForm()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                    Reset
                                </button>
                                <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                    <i class="fas fa-upload mr-2"></i>Upload Images
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Search and Filter -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6">
                <form method="GET" class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($searchTerm); ?>" placeholder="Search images..." class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                    </div>
                    <div>
                        <select name="display" class="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                            <option value="all" <?php echo $displayFilter === 'all' ? 'selected' : ''; ?>>All Images</option>
                            <option value="display" <?php echo $displayFilter === 'display' ? 'selected' : ''; ?>>Display Images Only</option>
                            <option value="hidden" <?php echo $displayFilter === 'hidden' ? 'selected' : ''; ?>>Hidden Images Only</option>
                        </select>
                    </div>
                    <button type="submit" class="px-4 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                </form>
            </div>
            
            <!-- Images Grid -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800">Gallery Images</h3>
                            <p class="text-sm text-gray-500 mt-1">
                                Showing <?php echo count($images); ?> images
                                (<?php echo $displayCount; ?>/20 selected for display)
                            </p>
                        </div>

                        <!-- Bulk Actions -->
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                                <label for="select-all" class="text-sm text-gray-600">Select All</label>
                            </div>

                            <div id="bulk-actions" class="hidden flex items-center space-x-2">
                                <span id="selected-count" class="text-sm text-gray-600">0 selected</span>
                                <button type="button" onclick="showBulkActions()" class="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors">
                                    Actions
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php if (empty($images)): ?>
                    <div class="p-12 text-center text-gray-500">
                        <i class="fas fa-images text-4xl mb-4"></i>
                        <p class="text-lg font-medium">No images found</p>
                        <p class="text-sm">Upload your first images to get started!</p>
                    </div>
                <?php else: ?>
                    <div class="p-6">
                        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
                            <?php foreach ($images as $image): ?>
                                <div class="relative group">
                                    <!-- Selection Checkbox -->
                                    <div class="absolute top-2 left-2 z-10">
                                        <input type="checkbox"
                                               class="image-checkbox rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                                               value="<?php echo $image['image_id']; ?>"
                                               onchange="updateBulkActions()">
                                    </div>

                                    <div class="aspect-square rounded-lg overflow-hidden bg-gray-100 border-2 <?php echo $image['is_display_image'] ? 'border-green-500' : 'border-gray-200'; ?> hover:border-orange-300 transition-all duration-300">
                                        <img src="<?php echo htmlspecialchars('../' . $image['url']); ?>"
                                             alt="<?php echo htmlspecialchars($image['alt_text']); ?>"
                                             class="w-full h-full object-cover">
                                        
                                        <!-- Overlay with actions -->
                                        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                                            <div class="flex space-x-2">
                                                <!-- Toggle Display Button -->
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="toggle_display">
                                                    <input type="hidden" name="image_id" value="<?php echo $image['image_id']; ?>">
                                                    <input type="hidden" name="current_status" value="<?php echo $image['is_display_image']; ?>">
                                                    <button type="submit" 
                                                            class="p-2 rounded-full transition-all duration-200 <?php echo $image['is_display_image'] ? 'bg-green-500 text-white' : 'bg-white text-green-500 hover:bg-green-500 hover:text-white'; ?>"
                                                            title="<?php echo $image['is_display_image'] ? 'Remove from gallery display' : 'Add to gallery display'; ?>">
                                                        <i class="fas fa-eye text-sm"></i>
                                                    </button>
                                                </form>
                                                
                                                <!-- Edit Button -->
                                                <button onclick="editImage(<?php echo $image['image_id']; ?>)" 
                                                        class="p-2 bg-white text-blue-500 rounded-full hover:bg-blue-500 hover:text-white transition-all duration-200"
                                                        title="Edit image">
                                                    <i class="fas fa-edit text-sm"></i>
                                                </button>
                                                
                                                <!-- Delete Button -->
                                                <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this image?')">
                                                    <input type="hidden" name="action" value="delete">
                                                    <input type="hidden" name="image_id" value="<?php echo $image['image_id']; ?>">
                                                    <button type="submit" 
                                                            class="p-2 bg-white text-red-500 rounded-full hover:bg-red-500 hover:text-white transition-all duration-200"
                                                            title="Delete image">
                                                        <i class="fas fa-trash text-sm"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>

                                        <!-- Display indicator -->
                                        <?php if ($image['is_display_image']): ?>
                                            <div class="display-image-indicator">
                                                <i class="fas fa-eye mr-1"></i>Display
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Image info -->
                                    <div class="mt-2 text-xs text-gray-600">
                                        <p class="font-medium truncate" title="<?php echo htmlspecialchars($image['alt_text']); ?>">
                                            <?php echo Utils::truncateText($image['alt_text'], 30); ?>
                                        </p>
                                        <p class="text-gray-500">
                                            <?php echo date('M j, Y', strtotime($image['created_at'])); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulkActionsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-tasks mr-2 text-orange-600"></i>Bulk Actions
                        </h3>
                        <button onclick="closeBulkActionsModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="mb-4">
                        <p class="text-sm text-gray-600">
                            <span id="bulk-selected-count">0</span> image(s) selected
                        </p>
                    </div>

                    <div class="space-y-3">
                        <!-- Show in Gallery -->
                        <form method="POST" id="bulk-show-form" onsubmit="return confirmBulkAction('show')">
                            <input type="hidden" name="action" value="bulk_toggle_display">
                            <input type="hidden" name="bulk_action" value="show">
                            <div id="bulk-show-images"></div>
                            <button type="submit" class="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-eye mr-2"></i>Show in Gallery
                            </button>
                        </form>

                        <!-- Hide from Gallery -->
                        <form method="POST" id="bulk-hide-form" onsubmit="return confirmBulkAction('hide')">
                            <input type="hidden" name="action" value="bulk_toggle_display">
                            <input type="hidden" name="bulk_action" value="hide">
                            <div id="bulk-hide-images"></div>
                            <button type="submit" class="w-full flex items-center justify-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                                <i class="fas fa-eye-slash mr-2"></i>Hide from Gallery
                            </button>
                        </form>

                        <!-- Delete Selected -->
                        <form method="POST" id="bulk-delete-form" onsubmit="return confirmBulkAction('delete')">
                            <input type="hidden" name="action" value="bulk_delete">
                            <div id="bulk-delete-images"></div>
                            <button type="submit" class="w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-2"></i>Delete Selected
                            </button>
                        </form>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <button onclick="closeBulkActionsModal()" class="w-full px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div id="editModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">
                            <i class="fas fa-edit mr-2 text-orange-600"></i>Edit Image
                        </h3>
                        <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form id="editForm" method="POST">
                        <input type="hidden" name="action" value="update">
                        <input type="hidden" name="image_id" id="editImageId">
                        
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                                <input type="text" name="title" id="editTitle" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Alt Text</label>
                                <input type="text" name="alt_text" id="editAltText" required class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                            </div>
                        </div>
                        
                        <div class="flex justify-end space-x-3 mt-6">
                            <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                Cancel
                            </button>
                            <button type="submit" class="px-6 py-2 text-white gradient-bg rounded-lg hover:opacity-90 transition-opacity">
                                <i class="fas fa-save mr-2"></i>Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Global variables
        let selectedFiles = [];
        let displayImageIndex = -1;

        // Images data for JavaScript access
        const images = <?php
            echo json_encode($images, JSON_UNESCAPED_UNICODE | JSON_HEX_QUOT | JSON_HEX_APOS);
        ?>;
        
        // Mobile sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            function toggleSidebar() {
                sidebar.classList.toggle('sidebar-open');
                sidebarOverlay.classList.toggle('active');
                document.body.classList.toggle('overflow-hidden');
            }
            
            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }
            
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }
            
            const navLinks = sidebar.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
            
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });

            initializeUploadForm();
            initializeFormToggle();
        });

        // Form toggle functionality
        function initializeFormToggle() {
            const toggleHeader = document.getElementById('toggleUploadHeader');
            const formContent = document.getElementById('uploadFormContent');
            const toggleIcon = document.getElementById('uploadToggleIcon');

            toggleHeader.addEventListener('click', function() {
                formContent.classList.toggle('expanded');
                toggleIcon.classList.toggle('rotate-180');
            });
        }

        // Upload form initialization
        function initializeUploadForm() {
            const uploadZone = document.querySelector('.upload-zone');
            const imageInput = document.getElementById('imageInput');

            if (!uploadZone || !imageInput) {
                console.error('Required elements not found for upload form initialization');
                return;
            }

            // Upload zone click handler
            uploadZone.addEventListener('click', () => imageInput.click());

            // File input change handler
            imageInput.addEventListener('change', handleFileSelection);

            // Drag and drop handlers
            uploadZone.addEventListener('dragover', handleDragOver);
            uploadZone.addEventListener('dragleave', handleDragLeave);
            uploadZone.addEventListener('drop', handleFileDrop);
        }

        function handleFileSelection(event) {
            const files = Array.from(event.target.files);
            processFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.currentTarget.classList.remove('dragover');
        }

        function handleFileDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');
            const files = Array.from(event.dataTransfer.files);
            processFiles(files);
        }

        function processFiles(files) {
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length === 0) {
                alert('Please select valid image files.');
                return;
            }

            // Append new files to existing ones instead of replacing
            selectedFiles = [...selectedFiles, ...imageFiles];
            console.log('Total files after adding:', selectedFiles.length);
            compressAndPreviewFiles(selectedFiles);
        }

        async function compressAndPreviewFiles(files) {
            const progressContainer = document.getElementById('compressionProgress');
            const progressBar = document.getElementById('progressBar');
            const previewGrid = document.getElementById('imagePreviewGrid');
            
            progressContainer.classList.remove('hidden');
            previewGrid.innerHTML = '';
            
            const compressedFiles = [];
            const maxSizeBytes = 5 * 1024 * 1024; // 5MB
            
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                progressBar.style.width = `${((i + 1) / files.length) * 100}%`;
                
                try {
                    let compressedFile = await compressImageToSize(file, maxSizeBytes);

                    if (compressedFile) {
                        compressedFiles.push({
                            original: file,
                            compressed: compressedFile,
                            index: i
                        });

                        createImagePreview(compressedFile, file, i);
                    } else {
                        throw new Error('Compression returned null');
                    }
                } catch (error) {
                    console.error('Error compressing image:', error);

                    // If compression fails, still try to use original if under 5MB
                    if (file.size <= maxSizeBytes) {
                        compressedFiles.push({
                            original: file,
                            compressed: file,
                            index: i
                        });
                        createImagePreview(file, file, i);
                        console.log(`Using original file for ${file.name} (compression failed but file is under size limit)`);
                    } else {
                        const errorMsg = error.message.includes('timeout')
                            ? `Image compression timed out for ${file.name}. Please try a smaller image or different format.`
                            : `Failed to compress ${file.name} to required size (${formatFileSize(maxSizeBytes)}). Current size: ${formatFileSize(file.size)}. Please choose a smaller image.`;

                        alert(errorMsg);
                        console.error(`Compression failed for ${file.name}:`, error);
                    }
                }
            }
            
            progressContainer.classList.add('hidden');
            updateUploadSummary(compressedFiles);
            updateFormData(compressedFiles);
        }

        async function compressImageToSize(file, maxSizeBytes) {
            try {
                // Start with reasonable dimensions based on file size
                let maxDimension = file.size > 10 * 1024 * 1024 ? 800 : 1200; // 10MB threshold
                let quality = 0.8; // Start with lower quality for faster processing
                let compressedFile;
                let attempts = 0;
                const maxAttempts = 4; // Limit attempts to prevent infinite loops

                // Quick size check - if already small enough, use higher quality
                if (file.size <= maxSizeBytes) {
                    return await compressImage(file, maxDimension, maxDimension, 0.9);
                }

                // Progressive compression with limited attempts
                do {
                    try {
                        compressedFile = await compressImage(file, maxDimension, maxDimension, quality);
                        attempts++;

                        if (compressedFile && compressedFile.size <= maxSizeBytes) {
                            break;
                        }

                        // Adjust parameters more aggressively for faster convergence
                        if (attempts === 1) {
                            quality = 0.6;
                        } else if (attempts === 2) {
                            quality = 0.4;
                            maxDimension = Math.floor(maxDimension * 0.8);
                        } else {
                            maxDimension = Math.floor(maxDimension * 0.7);
                            quality = 0.3;
                        }
                    } catch (error) {
                        console.error(`Compression attempt ${attempts + 1} failed:`, error);
                        // If compression fails, try with more aggressive settings
                        quality = Math.max(0.1, quality - 0.2);
                        maxDimension = Math.floor(maxDimension * 0.8);
                        attempts++;
                    }

                } while ((!compressedFile || compressedFile.size > maxSizeBytes) && attempts < maxAttempts);

                if (!compressedFile) {
                    throw new Error('Failed to compress image after all attempts');
                }

                return compressedFile;
            } catch (error) {
                console.error('Image compression failed:', error);
                throw error;
            }
        }

        function compressImage(file, maxWidth, maxHeight, quality) {
            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                let blobUrl = null;

                // Set timeout to prevent hanging
                const timeout = setTimeout(() => {
                    if (blobUrl) URL.revokeObjectURL(blobUrl);
                    reject(new Error('Image compression timeout'));
                }, 30000); // 30 second timeout

                img.onload = () => {
                    try {
                        let { width, height } = calculateDimensions(
                            img.width,
                            img.height,
                            maxWidth,
                            maxHeight
                        );

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);
                        canvas.toBlob((blob) => {
                            clearTimeout(timeout);
                            if (blobUrl) URL.revokeObjectURL(blobUrl);

                            if (blob) {
                                resolve(blob);
                            } else {
                                reject(new Error('Failed to create compressed blob'));
                            }
                        }, 'image/jpeg', quality);
                    } catch (error) {
                        clearTimeout(timeout);
                        if (blobUrl) URL.revokeObjectURL(blobUrl);
                        reject(error);
                    }
                };

                img.onerror = () => {
                    clearTimeout(timeout);
                    if (blobUrl) URL.revokeObjectURL(blobUrl);
                    reject(new Error('Failed to load image for compression'));
                };

                try {
                    blobUrl = URL.createObjectURL(file);
                    img.src = blobUrl;
                } catch (error) {
                    clearTimeout(timeout);
                    reject(error);
                }
            });
        }

        function calculateDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
            const ratio = Math.min(maxWidth / originalWidth, maxHeight / originalHeight);
            
            if (ratio >= 1) {
                return { width: originalWidth, height: originalHeight };
            }
            
            return {
                width: Math.round(originalWidth * ratio),
                height: Math.round(originalHeight * ratio)
            };
        }

        function createImagePreview(compressedFile, originalFile, index) {
            const previewGrid = document.getElementById('imagePreviewGrid');
            const previewDiv = document.createElement('div');
            previewDiv.className = 'image-preview relative';
            previewDiv.dataset.index = index;
            
            const img = document.createElement('img');
            img.src = URL.createObjectURL(compressedFile);
            img.alt = `Preview ${index + 1}`;
            
            const overlay = document.createElement('div');
            overlay.className = 'image-overlay';
            
            const actions = document.createElement('div');
            actions.className = 'flex space-x-2';
            
            const displayBtn = document.createElement('button');
            displayBtn.type = 'button';
            displayBtn.className = 'bg-green-500 text-white px-2 py-1 rounded text-xs hover:bg-green-600 transition-colors';
            displayBtn.innerHTML = '<i class="fas fa-eye"></i>';
            displayBtn.title = 'Set as display image';
            displayBtn.onclick = () => setDisplayImage(index);
            
            const deleteBtn = document.createElement('button');
            deleteBtn.type = 'button';
            deleteBtn.className = 'bg-red-500 text-white px-2 py-1 rounded text-xs hover:bg-red-600 transition-colors';
            deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
            deleteBtn.title = 'Remove image';
            deleteBtn.onclick = () => removeImage(index);
            
            actions.appendChild(displayBtn);
            actions.appendChild(deleteBtn);
            overlay.appendChild(actions);
            
            const info = document.createElement('div');
            info.className = 'mt-1 text-center text-xs text-gray-600';
            const originalSize = formatFileSize(originalFile.size);
            const compressedSize = formatFileSize(compressedFile.size);
            const savings = Math.round((1 - compressedFile.size / originalFile.size) * 100);
            info.innerHTML = `${originalSize} → ${compressedSize} (${savings}% saved)`;
            
            previewDiv.appendChild(img);
            previewDiv.appendChild(overlay);
            previewDiv.appendChild(info);
            
            if (index === displayImageIndex) {
                const indicator = document.createElement('div');
                indicator.className = 'display-image-indicator';
                indicator.innerHTML = '<i class="fas fa-eye mr-1"></i>Display';
                previewDiv.appendChild(indicator);
            }
            
            previewGrid.appendChild(previewDiv);
        }

        function setDisplayImage(index) {
            displayImageIndex = index;
            document.getElementById('displayImageIndex').value = index;
            
            const previewGrid = document.getElementById('imagePreviewGrid');
            previewGrid.querySelectorAll('.display-image-indicator').forEach(el => el.remove());
            previewGrid.querySelectorAll('.image-preview').forEach((el, i) => {
                if (i === index) {
                    const indicator = document.createElement('div');
                    indicator.className = 'display-image-indicator';
                    indicator.innerHTML = '<i class="fas fa-eye mr-1"></i>Display';
                    el.appendChild(indicator);
                }
            });
        }

        function removeImage(index) {
            selectedFiles.splice(index, 1);
            
            if (displayImageIndex === index) {
                displayImageIndex = -1;
                document.getElementById('displayImageIndex').value = '';
            } else if (displayImageIndex > index) {
                displayImageIndex--;
                document.getElementById('displayImageIndex').value = displayImageIndex;
            }
            
            if (selectedFiles.length > 0) {
                compressAndPreviewFiles(selectedFiles);
            } else {
                document.getElementById('imagePreviewGrid').innerHTML = '';
                document.getElementById('uploadSummary').classList.add('hidden');
            }
        }

        function updateUploadSummary(compressedFiles) {
            const summaryContainer = document.getElementById('uploadSummary');
            const totalFiles = document.getElementById('totalFiles');
            const totalSize = document.getElementById('totalSize');
            
            const totalSizeBytes = compressedFiles.reduce((sum, file) => sum + file.compressed.size, 0);
            
            totalFiles.textContent = compressedFiles.length;
            totalSize.textContent = formatFileSize(totalSizeBytes);
            
            summaryContainer.classList.remove('hidden');
        }

        function updateFormData(compressedFiles) {
            const dataTransfer = new DataTransfer();
            compressedFiles.forEach(fileData => {
                dataTransfer.items.add(fileData.compressed);
            });
            
            document.getElementById('imageInput').files = dataTransfer.files;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function resetUploadForm() {
            document.getElementById('uploadForm').reset();
            selectedFiles = [];
            displayImageIndex = -1;
            document.getElementById('imagePreviewGrid').innerHTML = '';
            document.getElementById('uploadSummary').classList.add('hidden');
            document.getElementById('displayImageIndex').value = '';
        }

        function editImage(imageId) {
            // Find image data from the loaded images array
            const image = images.find(img => img.image_id == imageId);

            if (image) {
                // Populate form fields
                document.getElementById('editImageId').value = image.image_id;

                // Parse the alt_text to extract title and alt text
                // The alt_text is stored as "title - alt_text" format
                const altTextParts = image.alt_text.split(' - ');
                const title = altTextParts[0] || '';
                const altText = altTextParts.length > 1 ? altTextParts.slice(1).join(' - ') : image.alt_text;

                document.getElementById('editTitle').value = title;
                document.getElementById('editAltText').value = altText;

                // Show the modal
                document.getElementById('editModal').classList.remove('hidden');
            } else {
                alert('Error: Image not found');
            }
        }

        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
        }

        // Bulk Selection Functions
        function updateBulkActions() {
            const checkboxes = document.querySelectorAll('.image-checkbox:checked');
            const count = checkboxes.length;
            const bulkActions = document.getElementById('bulk-actions');
            const selectedCount = document.getElementById('selected-count');
            const selectAll = document.getElementById('select-all');

            selectedCount.textContent = `${count} selected`;

            if (count > 0) {
                bulkActions.classList.remove('hidden');
            } else {
                bulkActions.classList.add('hidden');
            }

            // Update select all checkbox
            const allCheckboxes = document.querySelectorAll('.image-checkbox');
            selectAll.checked = allCheckboxes.length > 0 && count === allCheckboxes.length;
            selectAll.indeterminate = count > 0 && count < allCheckboxes.length;
        }

        function showBulkActions() {
            const checkboxes = document.querySelectorAll('.image-checkbox:checked');
            const selectedIds = Array.from(checkboxes).map(cb => cb.value);

            // Update count in modal
            document.getElementById('bulk-selected-count').textContent = selectedIds.length;

            // Add hidden inputs for each form
            updateBulkFormInputs('bulk-show-images', selectedIds);
            updateBulkFormInputs('bulk-hide-images', selectedIds);
            updateBulkFormInputs('bulk-delete-images', selectedIds);

            document.getElementById('bulkActionsModal').classList.remove('hidden');
        }

        function updateBulkFormInputs(containerId, selectedIds) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_images[]';
                input.value = id;
                container.appendChild(input);
            });
        }

        function closeBulkActionsModal() {
            document.getElementById('bulkActionsModal').classList.add('hidden');
        }

        function confirmBulkAction(action) {
            const count = document.querySelectorAll('.image-checkbox:checked').length;
            let message = '';

            switch(action) {
                case 'show':
                    message = `Are you sure you want to show ${count} image(s) in the gallery?`;
                    break;
                case 'hide':
                    message = `Are you sure you want to hide ${count} image(s) from the gallery?`;
                    break;
                case 'delete':
                    message = `Are you sure you want to delete ${count} image(s)? This action cannot be undone.`;
                    break;
            }

            return confirm(message);
        }

        // Select All functionality
        document.addEventListener('DOMContentLoaded', function() {
            const selectAll = document.getElementById('select-all');

            selectAll.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.image-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAll.checked;
                });
                updateBulkActions();
            });

            // Initialize bulk actions state
            updateBulkActions();
        });
    </script>
</body>
</html>