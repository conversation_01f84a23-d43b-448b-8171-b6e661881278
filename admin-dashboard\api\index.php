<?php
// Start output buffering to prevent header issues
ob_start();

header('Content-Type: application/json');
require_once '../config/config.php';
require_once '../classes/models.php';
require_once '../classes/additional_models.php';

// Enable CORS
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Check authentication
Auth::startSession();
if (!Auth::isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($path, '/'));

// Get the resource and ID from URL
$resource = isset($pathParts[2]) ? $pathParts[2] : '';
$id = isset($pathParts[3]) ? $pathParts[3] : null;

try {
    switch ($resource) {
        case 'users':
            handleUsers($method, $id);
            break;
        case 'destinations':
            handleDestinations($method, $id);
            break;
        case 'packages':
            handlePackages($method, $id);
            break;
        case 'package-types':
            handlePackageTypes($method, $id);
            break;
        case 'images':
            handleImages($method, $id);
            break;
        case 'gallery':
            handleGallery($method, $id);
            break;
        case 'contact-info':
            handleContactInfo($method, $id);
            break;
        case 'messages':
            handleMessages($method, $id);
            break;
        case 'reports':
            handleReports($method, $id);
            break;
        case 'dashboard-stats':
            handleDashboardStats();
            break;
        default:
            http_response_code(404);
            echo json_encode(['error' => 'Resource not found']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

function handleUsers($method, $id) {
    $userModel = new User();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $user = $userModel->findById($id);
                if ($user) {
                    unset($user['password_hash']);
                    echo json_encode($user);
                } else {
                    http_response_code(404);
                    echo json_encode(['error' => 'User not found']);
                }
            } else {
                $users = $userModel->findAll();
                foreach ($users as &$user) {
                    unset($user['password_hash']);
                }
                echo json_encode($users);
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            if ($userModel->create($data)) {
                echo json_encode(['success' => true, 'message' => 'User created successfully']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create user']);
            }
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                if ($userModel->update($id, $data)) {
                    echo json_encode(['success' => true, 'message' => 'User updated successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to update user']);
                }
            }
            break;
        case 'DELETE':
            if ($id) {
                if ($userModel->delete($id)) {
                    echo json_encode(['success' => true, 'message' => 'User deleted successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to delete user']);
                }
            }
            break;
    }
}

function handleDestinations($method, $id) {
    $destinationModel = new Destination();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $destination = $destinationModel->findById($id);
                echo json_encode($destination);
            } else {
                $destinations = $destinationModel->findAllWithImages();
                echo json_encode($destinations);
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            $data['created_by_user_id'] = Auth::getCurrentUser()['user_id'];
            $destinationId = $destinationModel->create($data);
            if ($destinationId) {
                echo json_encode(['success' => true, 'id' => $destinationId, 'message' => 'Destination created successfully']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create destination']);
            }
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                if ($destinationModel->update($id, $data)) {
                    echo json_encode(['success' => true, 'message' => 'Destination updated successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to update destination']);
                }
            }
            break;
        case 'DELETE':
            if ($id) {
                if ($destinationModel->delete($id)) {
                    echo json_encode(['success' => true, 'message' => 'Destination deleted successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to delete destination']);
                }
            }
            break;
    }
}

function handlePackages($method, $id) {
    $packageModel = new TourPackage();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $package = $packageModel->findById($id);
                echo json_encode($package);
            } else {
                $packages = $packageModel->findAllWithDetails();
                echo json_encode($packages);
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            $data['created_by_user_id'] = Auth::getCurrentUser()['user_id'];
            $packageId = $packageModel->create($data);
            if ($packageId) {
                echo json_encode(['success' => true, 'id' => $packageId, 'message' => 'Package created successfully']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create package']);
            }
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                if ($packageModel->update($id, $data)) {
                    echo json_encode(['success' => true, 'message' => 'Package updated successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to update package']);
                }
            }
            break;
        case 'DELETE':
            if ($id) {
                if ($packageModel->delete($id)) {
                    echo json_encode(['success' => true, 'message' => 'Package deleted successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to delete package']);
                }
            }
            break;
    }
}

function handlePackageTypes($method, $id) {
    $typeModel = new TourPackageType();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $type = $typeModel->findById($id);
                echo json_encode($type);
            } else {
                $types = $typeModel->findAll();
                echo json_encode($types);
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            if ($typeModel->create($data)) {
                echo json_encode(['success' => true, 'message' => 'Package type created successfully']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create package type']);
            }
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                if ($typeModel->update($id, $data)) {
                    echo json_encode(['success' => true, 'message' => 'Package type updated successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to update package type']);
                }
            }
            break;
        case 'DELETE':
            if ($id) {
                if ($typeModel->delete($id)) {
                    echo json_encode(['success' => true, 'message' => 'Package type deleted successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to delete package type']);
                }
            }
            break;
    }
}

function handleDashboardStats() {
    $reportModel = new Report();
    $stats = $reportModel->generateDashboardStats();
    echo json_encode($stats);
}

function handleMessages($method, $id) {
    $messageModel = new Message();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $message = $messageModel->findById($id);
                echo json_encode($message);
            } else {
                $messages = $messageModel->findAll();
                echo json_encode($messages);
            }
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                if (isset($data['mark_read']) && $data['mark_read']) {
                    if ($messageModel->markAsRead($id)) {
                        echo json_encode(['success' => true, 'message' => 'Message marked as read']);
                    } else {
                        http_response_code(400);
                        echo json_encode(['error' => 'Failed to mark message as read']);
                    }
                } elseif (isset($data['reply_content'])) {
                    if ($messageModel->reply($id, $data['reply_content'])) {
                        echo json_encode(['success' => true, 'message' => 'Reply sent successfully']);
                    } else {
                        http_response_code(400);
                        echo json_encode(['error' => 'Failed to send reply']);
                    }
                }
            }
            break;
        case 'DELETE':
            if ($id) {
                if ($messageModel->delete($id)) {
                    echo json_encode(['success' => true, 'message' => 'Message deleted successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to delete message']);
                }
            }
            break;
    }
}

function handleImages($method, $id) {
    $imageModel = new Image();

    // Parse the URL to get the resource type and ID
    // Expected format: /images/destinations/123/images or /images/packages/123/images
    $pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
    $resourceType = null;
    $resourceId = null;

    // Find the images index and extract resource info
    $imagesIndex = array_search('images', $pathParts);
    if ($imagesIndex !== false && isset($pathParts[$imagesIndex + 1]) && isset($pathParts[$imagesIndex + 2])) {
        $resourceType = $pathParts[$imagesIndex + 1]; // 'destinations' or 'packages'
        $resourceId = intval($pathParts[$imagesIndex + 2]);
    }

    switch ($method) {
        case 'GET':
            if ($resourceType && $resourceId) {
                try {
                    $images = [];

                    if ($resourceType === 'destinations') {
                        $images = $imageModel->findByType('destination', $resourceId);
                    } elseif ($resourceType === 'packages') {
                        $images = $imageModel->findByType('tour_package', $resourceId);
                    }

                    // Add display image status and decode HTML entities
                    foreach ($images as &$image) {
                        // The URLs are stored correctly for admin dashboard context

                        // Decode HTML entities in alt_text for proper display
                        if (isset($image['alt_text'])) {
                            $image['alt_text'] = html_entity_decode($image['alt_text'], ENT_QUOTES, 'UTF-8');
                        }

                        $image['is_display_image'] = (bool)$image['is_display_image'];
                    }

                    echo json_encode([
                        'success' => true,
                        'data' => $images
                    ]);
                } catch (Exception $e) {
                    http_response_code(500);
                    echo json_encode([
                        'success' => false,
                        'error' => 'Failed to load images: ' . $e->getMessage()
                    ]);
                }
            } else {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid request format. Expected: /images/destinations/{id}/images or /images/packages/{id}/images'
                ]);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
    }
}

function handleGallery($method, $id) {
    $galleryModel = new Gallery();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $gallery = $galleryModel->findById($id);
                echo json_encode($gallery);
            } else {
                $galleries = $galleryModel->findAllWithImageCount();
                echo json_encode($galleries);
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            if ($galleryModel->create($data)) {
                echo json_encode(['success' => true, 'message' => 'Gallery created successfully']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to create gallery']);
            }
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                if ($galleryModel->update($id, $data)) {
                    echo json_encode(['success' => true, 'message' => 'Gallery updated successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to update gallery']);
                }
            }
            break;
        case 'DELETE':
            if ($id) {
                if ($galleryModel->delete($id)) {
                    echo json_encode(['success' => true, 'message' => 'Gallery deleted successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to delete gallery']);
                }
            }
            break;
    }
}

function handleContactInfo($method, $id) {
    $contactModel = new ContactInfo();
    
    switch ($method) {
        case 'GET':
            $contact = $contactModel->getCurrent();
            echo json_encode($contact);
            break;
        case 'PUT':
            if ($id) {
                $data = json_decode(file_get_contents('php://input'), true);
                $data['last_edited_by_user_id'] = Auth::getCurrentUser()['user_id'];
                if ($contactModel->update($id, $data)) {
                    echo json_encode(['success' => true, 'message' => 'Contact info updated successfully']);
                } else {
                    http_response_code(400);
                    echo json_encode(['error' => 'Failed to update contact info']);
                }
            }
            break;
    }
}

function handleReports($method, $id) {
    $reportModel = new Report();
    
    switch ($method) {
        case 'GET':
            if ($id) {
                $report = $reportModel->findById($id);
                echo json_encode($report);
            } else {
                $reports = $reportModel->findAll();
                echo json_encode($reports);
            }
            break;
        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            $data['generated_by_user_id'] = Auth::getCurrentUser()['user_id'];
            if ($reportModel->create($data)) {
                echo json_encode(['success' => true, 'message' => 'Report generated successfully']);
            } else {
                http_response_code(400);
                echo json_encode(['error' => 'Failed to generate report']);
            }
            break;
    }
}
