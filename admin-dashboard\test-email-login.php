<?php
/**
 * Email Login Tester
 * Test different email credentials and settings
 */

// Include security middleware for web access
require_once 'includes/security_middleware.php';
requireRole('admin');

$testResult = '';
$testSuccess = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $host = trim($_POST['host']);
    $port = intval($_POST['port']);
    $encryption = $_POST['encryption'];
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    
    // Test the connection
    $hostname = "{{$host}:{$port}/imap/{$encryption}}INBOX";
    
    // Clear previous errors
    if (function_exists('imap_errors')) {
        imap_errors();
        imap_alerts();
    }
    
    $inbox = @imap_open($hostname, $email, $password);
    
    if ($inbox) {
        $mailboxInfo = imap_mailboxmsginfo($inbox);
        $testSuccess = true;
        $testResult = "✅ SUCCESS! Connected successfully!\n";
        $testResult .= "Total messages: {$mailboxInfo->Nmsgs}\n";
        $testResult .= "Unread messages: {$mailboxInfo->Unread}\n";
        imap_close($inbox);
    } else {
        $errors = imap_errors();
        $alerts = imap_alerts();
        
        $testResult = "❌ FAILED to connect\n\n";
        if ($errors) {
            $testResult .= "Errors:\n" . implode("\n", $errors) . "\n\n";
        }
        if ($alerts) {
            $testResult .= "Alerts:\n" . implode("\n", $alerts) . "\n\n";
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Login Tester - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen py-8">
        <div class="max-w-2xl mx-auto px-4">
            
            <!-- Header -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h1 class="text-xl font-bold text-gray-900">Email Login Tester</h1>
                    <p class="text-sm text-gray-600">Test different email credentials to find the correct settings</p>
                </div>
            </div>

            <!-- Test Result -->
            <?php if ($testResult): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $testSuccess ? 'bg-green-100 border border-green-200' : 'bg-red-100 border border-red-200'; ?>">
                    <h3 class="font-semibold <?php echo $testSuccess ? 'text-green-800' : 'text-red-800'; ?> mb-2">
                        Test Result
                    </h3>
                    <pre class="<?php echo $testSuccess ? 'text-green-700' : 'text-red-700'; ?> text-sm whitespace-pre-wrap"><?php echo htmlspecialchars($testResult); ?></pre>
                </div>
            <?php endif; ?>

            <!-- Test Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Test Email Credentials</h2>
                </div>
                
                <form method="POST" class="p-6 space-y-4">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Server Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="host" class="block text-sm font-medium text-gray-700 mb-1">Host</label>
                            <input type="text" id="host" name="host" value="<?php echo $_POST['host'] ?? 'mail.melevatours.co.ke'; ?>" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                        </div>
                        
                        <div>
                            <label for="port" class="block text-sm font-medium text-gray-700 mb-1">Port</label>
                            <input type="number" id="port" name="port" value="<?php echo $_POST['port'] ?? '993'; ?>" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                        </div>
                        
                        <div>
                            <label for="encryption" class="block text-sm font-medium text-gray-700 mb-1">Encryption</label>
                            <select id="encryption" name="encryption" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                                <option value="ssl" <?php echo ($_POST['encryption'] ?? 'ssl') === 'ssl' ? 'selected' : ''; ?>>SSL</option>
                                <option value="tls" <?php echo ($_POST['encryption'] ?? '') === 'tls' ? 'selected' : ''; ?>>TLS</option>
                                <option value="none" <?php echo ($_POST['encryption'] ?? '') === 'none' ? 'selected' : ''; ?>>None</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Email Credentials -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                            <input type="email" id="email" name="email" value="<?php echo $_POST['email'] ?? '<EMAIL>'; ?>" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                        </div>
                        
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-1">Password</label>
                            <input type="password" id="password" name="password" value="<?php echo $_POST['password'] ?? ''; ?>" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500">
                        </div>
                    </div>
                    
                    <!-- Test Button -->
                    <div class="flex justify-center">
                        <button type="submit" 
                                class="bg-orange-500 text-white px-8 py-3 rounded-lg hover:bg-orange-600 transition duration-300">
                            <i class="fas fa-plug mr-2"></i>Test Connection
                        </button>
                    </div>
                </form>
            </div>

            <!-- Common Issues -->
            <div class="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 class="font-semibold text-yellow-900 mb-3">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Common Authentication Issues
                </h3>
                
                <div class="space-y-3 text-yellow-800">
                    <div>
                        <strong>1. Wrong Password:</strong>
                        <p class="text-sm">Make sure you're using the correct email password. Try logging into webmail first.</p>
                    </div>
                    
                    <div>
                        <strong>2. IMAP Disabled:</strong>
                        <p class="text-sm">Your hosting provider might have IMAP disabled. Contact them to enable it.</p>
                    </div>
                    
                    <div>
                        <strong>3. App-Specific Password Required:</strong>
                        <p class="text-sm">Some hosts require special passwords for IMAP access (different from webmail password).</p>
                    </div>
                    
                    <div>
                        <strong>4. Account Doesn't Exist:</strong>
                        <p class="text-sm"><NAME_EMAIL> and <EMAIL> actually exist.</p>
                    </div>
                </div>
            </div>

            <!-- Quick Test Presets -->
            <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="font-semibold text-blue-900 mb-3">
                    <i class="fas fa-rocket mr-2"></i>Quick Test Presets
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button onclick="setPreset('mail.melevatours.co.ke', 993, 'ssl')" 
                            class="p-3 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 text-left">
                        <div class="font-medium text-blue-900">Standard SSL</div>
                        <div class="text-sm text-blue-700">mail.melevatours.co.ke:993 (SSL)</div>
                    </button>
                    
                    <button onclick="setPreset('melevatours.co.ke', 993, 'ssl')" 
                            class="p-3 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 text-left">
                        <div class="font-medium text-blue-900">Direct Domain</div>
                        <div class="text-sm text-blue-700">melevatours.co.ke:993 (SSL)</div>
                    </button>
                    
                    <button onclick="setPreset('mail.melevatours.co.ke', 143, 'tls')" 
                            class="p-3 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 text-left">
                        <div class="font-medium text-blue-900">TLS Connection</div>
                        <div class="text-sm text-blue-700">mail.melevatours.co.ke:143 (TLS)</div>
                    </button>
                    
                    <button onclick="setPreset('mail.melevatours.co.ke', 143, 'none')" 
                            class="p-3 bg-white border border-blue-300 rounded-lg hover:bg-blue-50 text-left">
                        <div class="font-medium text-blue-900">No Encryption</div>
                        <div class="text-sm text-blue-700">mail.melevatours.co.ke:143 (None)</div>
                    </button>
                </div>
            </div>

            <!-- Navigation -->
            <div class="mt-6 flex justify-center space-x-4">
                <a href="email-setup-guide.php" 
                   class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition duration-300">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Setup Guide
                </a>
            </div>
        </div>
    </div>

    <script>
        function setPreset(host, port, encryption) {
            document.getElementById('host').value = host;
            document.getElementById('port').value = port;
            document.getElementById('encryption').value = encryption;
        }
    </script>
</body>
</html>
