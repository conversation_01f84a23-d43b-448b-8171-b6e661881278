<?php
// Start output buffering to prevent header issues
ob_start();

require_once '../config/config.php';
require_once '../classes/models.php';

// Require authentication
Auth::requireLogin();

header('Content-Type: application/json');

try {
    $messageModel = new Message();
    $stats = $messageModel->getStats();
    
    echo json_encode([
        'success' => true,
        'count' => $stats['unread']
    ]);
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage()
    ]);
}
