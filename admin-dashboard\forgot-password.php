<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';

// Include PHPMailer classes
use PHPMailer\PHPMailer\PHPMailer;
use <PERSON>HPMailer\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Load Composer's autoloader
require 'vendor/autoload.php';

// Check if user is already logged in
Auth::startSession();
if (Auth::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = Utils::sanitizeInput($_POST['email']);
    
    if (!empty($email) && filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $userModel = new User();
        $user = $userModel->getUserByEmail($email);
        
        if ($user) {
            // Generate reset token
            $token = bin2hex(random_bytes(32));
            $expiresAt = date('Y-m-d H:i:s', strtotime('+1 hour'));
            
            // Save reset token to database
            if ($userModel->createPasswordResetToken($user['user_id'], $token, $expiresAt)) {
                // Send email with reset link
                $resetLink = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . "/reset-password.php?token=" . $token;
                
                if (sendPasswordResetEmail($email, $user['username'], $resetLink)) {
                    $message = 'Password reset instructions have been sent to your email address.';
                    $messageType = 'success';
                } else {
                    $message = 'Failed to send email. Please try again later.';
                    $messageType = 'error';
                }
            } else {
                $message = 'Failed to generate reset token. Please try again.';
                $messageType = 'error';
            }
        } else {
            // Don't reveal if email exists or not for security
            $message = 'If an account with that email exists, password reset instructions have been sent.';
            $messageType = 'success';
        }
    } else {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    }
}

// Email sending function using PHPMailer
function sendPasswordResetEmail($email, $username, $resetLink) {
    $mail = new PHPMailer(true);

    try {
        // Server settings
        $mail->isSMTP();                                            // Send using SMTP
        $mail->Host       = 'smtp.gmail.com';                     // Set the SMTP server to send through
        $mail->SMTPAuth   = true;                                   // Enable SMTP authentication
        $mail->Username   = '<EMAIL>';                 // SMTP username (YOUR GMAIL ADDRESS)
        $mail->Password   = 'lhgd sztv vrxe odws';                    // SMTP password (YOUR GMAIL APP PASSWORD)
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;         // Enable TLS encryption; `PHPMailer::ENCRYPTION_SMTPS` encouraged
        $mail->Port       = 587;                                    // TCP port to connect to, use 465 for `PHPMailer::ENCRYPTION_SMTPS` above

        // Recipients
        $mail->setFrom('<EMAIL>', 'Meleva Tours Admin');
        $mail->addAddress($email, $username);     // Add a recipient

        // Content
        $mail->isHTML(true);                                  // Set email format to HTML
        $mail->Subject = 'Password Reset - Meleva Tours Admin';
        $mail->Body    = "
        <html>
        <head>
            <title>Password Reset</title>
        </head>
        <body>
            <h2>Password Reset Request</h2>
            <p>Hello $username,</p>
            <p>You have requested to reset your password for your Meleva Tours admin account.</p>
            <p>Click the link below to reset your password:</p>
            <p><a href='$resetLink' style='background-color: #f97316; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Reset Password</a></p>
            <p>Or copy and paste this link into your browser:</p>
            <p>$resetLink</p>
            <p>This link will expire in 1 hour.</p>
            <p>If you did not request this password reset, please ignore this email.</p>
            <br>
            <p>Best regards,<br>Meleva Tours Team</p>
        </body>
        </html>
        ";
        $mail->AltBody = 'This is the plain text message body for non-HTML mail clients. Your password reset link is: ' . $resetLink;

        $mail->send();
        return true;
    } catch (Exception $e) {
        error_log("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
        return false;
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forgot Password - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .safari-pattern {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen safari-pattern">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo and Title -->
            <div class="text-center">
                <div class="mx-auto w-20 h-20 lg:w-40 lg:h-40 flex items-center justify-center mb-4">
                    <img src="images/meleva-lg.png" alt="logo">
                </div>
                <h2 class="text-3xl font-bold text-gray-900">Forgot Password</h2>
                <p class="mt-2 text-lg text-gray-600">Enter your email to reset your password</p>
            </div>
            
            <!-- Forgot Password Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <?php if ($message): ?>
                    <div class="mb-4 <?php echo $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'; ?> border px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                            <span><?php echo htmlspecialchars($message); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" class="space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Address
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input 
                                type="email" 
                                id="email" 
                                name="email" 
                                required 
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Enter your email address"
                                value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>"
                            >
                        </div>
                    </div>
                    
                    <div>
                        <button 
                            type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                        >
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-paper-plane text-orange-300 group-hover:text-orange-200"></i>
                            </span>
                            Send Reset Instructions
                        </button>
                    </div>
                </form>
                
                <div class="mt-6 text-center">
                    <a href="login.php" class="text-sm text-orange-600 hover:text-orange-500 font-medium">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Login
                    </a>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    © 2025 Meleva Tours & Travel. All rights reserved.
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const email = document.getElementById('email').value.trim();
            
            if (!email) {
                e.preventDefault();
                alert('Please enter your email address');
                return;
            }
            
            // Basic email validation
            const emailRegex = /^[^
            if (!emailRegex.test(email)) {
                e.preventDefault();
                alert('Please enter a valid email address');
            }
        });
    </script>
</body>
</html>

