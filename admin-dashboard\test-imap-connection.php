<?php
/**
 * Quick IMAP Connection Test
 * Simple script to test IMAP connection with current settings
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>IMAP Connection Test</h2>";
echo "<p>Testing connection with your current settings...</p>";

// Your current configuration
$host = 'mail.melevatours.co.ke';
$port = 993;
$encryption = 'ssl';
$email = '<EMAIL>';
$password = 'hi$Ch9=lYcap{7cA';

echo "<h3>Configuration:</h3>";
echo "<ul>";
echo "<li><strong>Host:</strong> $host</li>";
echo "<li><strong>Port:</strong> $port</li>";
echo "<li><strong>Encryption:</strong> $encryption</li>";
echo "<li><strong>Email:</strong> $email</li>";
echo "<li><strong>Password:</strong> " . str_repeat('*', strlen($password)) . "</li>";
echo "</ul>";

echo "<h3>Test Results:</h3>";

// 1. Check IMAP extension
echo "<p><strong>1. Checking IMAP extension...</strong></p>";
if (extension_loaded('imap')) {
    echo "<p style='color: green;'>✓ IMAP extension is loaded</p>";
} else {
    echo "<p style='color: red;'>✗ IMAP extension is NOT loaded</p>";
    echo "<p><strong>Solution:</strong> Install PHP IMAP extension</p>";
    echo "<p>For XAMPP: Edit php.ini and uncomment 'extension=imap'</p>";
    exit;
}

// 2. Test hostname resolution
echo "<p><strong>2. Testing hostname resolution...</strong></p>";
$ip = gethostbyname($host);
if ($ip !== $host) {
    echo "<p style='color: green;'>✓ Host $host resolves to $ip</p>";
} else {
    echo "<p style='color: red;'>✗ Cannot resolve hostname $host</p>";
    echo "<p><strong>Try these alternatives:</strong></p>";
    echo "<ul>";
    echo "<li>melevatours.co.ke</li>";
    echo "<li>imap.melevatours.co.ke</li>";
    echo "</ul>";
}

// 3. Test network connectivity
echo "<p><strong>3. Testing network connectivity...</strong></p>";
$connection = @fsockopen($host, $port, $errno, $errstr, 10);
if ($connection) {
    echo "<p style='color: green;'>✓ Can connect to $host:$port</p>";
    fclose($connection);
} else {
    echo "<p style='color: red;'>✗ Cannot connect to $host:$port</p>";
    echo "<p><strong>Error:</strong> $errstr ($errno)</p>";
    echo "<p><strong>Try these alternatives:</strong></p>";
    echo "<ul>";
    echo "<li>Port 143 (non-SSL IMAP)</li>";
    echo "<li>Different hostname</li>";
    echo "<li>Check firewall settings</li>";
    echo "</ul>";
}

// 4. Test IMAP authentication
echo "<p><strong>4. Testing IMAP authentication...</strong></p>";

// Clear any previous IMAP errors
if (function_exists('imap_errors')) {
    imap_errors();
    imap_alerts();
}

$hostname = "{{$host}:{$port}/imap/{$encryption}}INBOX";
echo "<p>Connecting to: $hostname</p>";

$inbox = @imap_open($hostname, $email, $password);

if ($inbox) {
    echo "<p style='color: green;'>✓ Successfully connected to IMAP server!</p>";
    
    // Get mailbox info
    $mailboxInfo = imap_mailboxmsginfo($inbox);
    echo "<p><strong>Mailbox Information:</strong></p>";
    echo "<ul>";
    echo "<li>Total messages: {$mailboxInfo->Nmsgs}</li>";
    echo "<li>Unread messages: {$mailboxInfo->Unread}</li>";
    echo "<li>Recent messages: {$mailboxInfo->Recent}</li>";
    echo "<li>Mailbox size: " . number_format($mailboxInfo->Size) . " bytes</li>";
    echo "</ul>";
    
    // Try to get the latest email
    if ($mailboxInfo->Nmsgs > 0) {
        echo "<p><strong>Latest Email:</strong></p>";
        $header = imap_headerinfo($inbox, $mailboxInfo->Nmsgs);
        if ($header) {
            $from = $header->from[0]->mailbox . "@" . $header->from[0]->host;
            $subject = isset($header->subject) ? $header->subject : 'No subject';
            $date = isset($header->date) ? $header->date : 'No date';
            
            echo "<ul>";
            echo "<li><strong>From:</strong> $from</li>";
            echo "<li><strong>Subject:</strong> $subject</li>";
            echo "<li><strong>Date:</strong> $date</li>";
            echo "</ul>";
        }
    } else {
        echo "<p>No emails found in inbox.</p>";
    }
    
    imap_close($inbox);
    
    echo "<h3 style='color: green;'>SUCCESS! Your IMAP configuration is working!</h3>";
    echo "<p>You can now use the email fetcher to automatically capture emails.</p>";
    echo "<p><a href='email-fetcher.php' style='background: #f97316; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Email Fetcher</a></p>";
    
} else {
    echo "<p style='color: red;'>✗ Failed to connect to IMAP server</p>";
    
    // Get IMAP errors
    $errors = imap_errors();
    $alerts = imap_alerts();
    
    if ($errors) {
        echo "<p><strong>IMAP Errors:</strong></p>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li style='color: red;'>$error</li>";
        }
        echo "</ul>";
    }
    
    if ($alerts) {
        echo "<p><strong>IMAP Alerts:</strong></p>";
        echo "<ul>";
        foreach ($alerts as $alert) {
            echo "<li style='color: orange;'>$alert</li>";
        }
        echo "</ul>";
    }
    
    echo "<h3>Troubleshooting Steps:</h3>";
    echo "<ol>";
    echo "<li><strong>Check credentials:</strong> Verify email and password are correct</li>";
    echo "<li><strong>Try different hostname:</strong> melevatours.co.ke instead of mail.melevatours.co.ke</li>";
    echo "<li><strong>Try different port:</strong> 143 for non-SSL IMAP</li>";
    echo "<li><strong>Try different encryption:</strong> 'tls' or 'none' instead of 'ssl'</li>";
    echo "<li><strong>Contact hosting provider:</strong> Ask for correct IMAP settings</li>";
    echo "</ol>";
    
    echo "<h3>Alternative Configurations to Try:</h3>";
    
    $alternatives = [
        ['host' => 'melevatours.co.ke', 'port' => 993, 'encryption' => 'ssl'],
        ['host' => 'mail.melevatours.co.ke', 'port' => 143, 'encryption' => 'tls'],
        ['host' => 'mail.melevatours.co.ke', 'port' => 143, 'encryption' => 'none'],
        ['host' => 'imap.melevatours.co.ke', 'port' => 993, 'encryption' => 'ssl'],
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 20px 0;'>";
    echo "<tr><th>Host</th><th>Port</th><th>Encryption</th><th>Status</th></tr>";
    
    foreach ($alternatives as $alt) {
        $testHostname = "{{$alt['host']}:{$alt['port']}/imap/{$alt['encryption']}}INBOX";
        $testResult = @imap_open($testHostname, $email, $password);
        
        echo "<tr>";
        echo "<td>{$alt['host']}</td>";
        echo "<td>{$alt['port']}</td>";
        echo "<td>{$alt['encryption']}</td>";
        
        if ($testResult) {
            echo "<td style='color: green;'>✓ WORKS!</td>";
            imap_close($testResult);
        } else {
            echo "<td style='color: red;'>✗ Failed</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
}

echo "<hr>";
echo "<p><a href='email-debug.php'>Run Full Diagnostic</a> | <a href='email-config-wizard.php'>Configuration Wizard</a> | <a href='email-setup-guide.php'>Setup Guide</a></p>";
?>
