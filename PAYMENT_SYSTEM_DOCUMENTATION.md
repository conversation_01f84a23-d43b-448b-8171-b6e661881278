# Meleva Tours and Travel - Payment System Documentation

## Overview
The payment system integrates Pesapal API v3.0 to provide secure payment processing for tour bookings. It supports multiple payment methods, partial payments, and comprehensive payment tracking.

## System Architecture

### Payment Flow
1. **Quote Request**: Customer submits tour quote request
2. **Quote Response**: Admin sends quote with payment link
3. **Payment Page**: Customer selects payment method and amount
4. **Payment Processing**: Pesapal handles secure payment
5. **Callback Handling**: System receives payment status
6. **Confirmation**: Customer and admin receive notifications

### Core Components
```
/
├── payment.php              # Payment form and processing
├── payment-success.php      # Success page
├── payment-failed.php       # Failure page
├── payment-status.php       # Status checker
├── payment-callback.php     # Pesapal callback handler
├── payment-ipn.php          # Instant Payment Notification
├── check-pesapal-status.php # Status verification
└── admin-dashboard/
    ├── config/pesapal_config.php # API configuration
    ├── classes/PesapalAPI.php     # API wrapper
    └── payments.php               # Payment management
```

## Pesapal Integration

### API Configuration
**Environment**: Sandbox (for testing) / Live (for production)
**API Version**: 3.0
**Documentation**: https://documenter.getpostman.com/view/6715320/UyxepTv1

**Configuration File**: `admin-dashboard/config/pesapal_config.php`
```php
// Demo Credentials (Kenyan Merchant)
define('PESAPAL_CONSUMER_KEY', 'qkio1BGGYAXTu2JOfm7XSXNruoZsrqEW');
define('PESAPAL_CONSUMER_SECRET', 'osGQ364R49cXKeOYSpaOnT++rHs=');
define('PESAPAL_ENVIRONMENT', 'sandbox'); // or 'live'
```

### API Endpoints
**Sandbox Base URL**: `https://cybqa.pesapal.com/pesapalv3/api/`
**Live Base URL**: `https://pay.pesapal.com/v3/api/`

**Key Endpoints**:
- `Auth/RequestToken` - Authentication
- `Transactions/SubmitOrderRequest` - Submit payment
- `Transactions/GetTransactionStatus` - Check status
- `URLSetup/RegisterIPN` - Register IPN URL

### Authentication
**Method**: OAuth 1.0
**Token Lifetime**: 5 minutes
**Auto-refresh**: Implemented in PesapalAPI class

## Payment Features

### 1. Payment Methods Supported
- **Mobile Money**: M-Pesa, Airtel Money, T-Kash
- **Bank Cards**: Visa, Mastercard
- **Bank Transfers**: Direct bank payments
- **Digital Wallets**: Various e-wallet options

### 2. Payment Types
**Full Payment**: Complete tour package amount
**Partial Payment**: Custom amount with balance tracking
- Minimum: $10 USD
- Balance calculation: Automatic
- Multiple partial payments: Supported

### 3. Currency
**Primary Currency**: USD ($)
**Conversion**: Handled by Pesapal
**Display**: Consistent USD formatting throughout

## Payment Workflow

### 1. Quote-to-Payment Process
```
Quote Request → Admin Review → Quote Acceptance → Payment Link → Payment Processing
```

**Quote Statuses**:
- **Pending**: Awaiting admin review
- **Accepted**: Quote approved, payment link available
- **Partially Paid**: Some payment received
- **Fully Paid**: Complete payment received
- **Rejected**: Quote declined

### 2. Payment Page (payment.php)
**Features**:
- Quote details display
- Amount selection (full/partial)
- Payment method selection
- Customer information form
- Secure payment processing

**Form Fields**:
- Customer name and email
- Phone number (international format)
- Payment amount
- Payment method selection

### 3. Payment Processing
**Security**:
- HTTPS required
- CSRF protection
- Input validation
- Secure token handling

**Process**:
1. Form validation
2. Pesapal order creation
3. Redirect to payment gateway
4. Customer completes payment
5. Callback processing
6. Status update
7. Notifications sent

## Status Management

### Payment Statuses
**Pesapal Statuses**:
- `PENDING`: Payment initiated
- `COMPLETED`: Payment successful
- `FAILED`: Payment failed
- `INVALID`: Invalid transaction
- `REVERSED`: Payment reversed

**System Statuses**:
- `pending`: Awaiting payment
- `processing`: Payment in progress
- `completed`: Payment successful
- `failed`: Payment failed
- `partially_paid`: Partial payment received
- `refunded`: Payment refunded

### Status Colors (UI)
- **Green**: Fully paid/completed
- **Yellow/Orange**: Partially paid
- **Blue**: Accepted/pending
- **Red**: Failed/rejected
- **Gray**: Pending review

## Database Schema

### Payments Table
```sql
CREATE TABLE payments (
    payment_id INT AUTO_INCREMENT PRIMARY KEY,
    quote_id INT NOT NULL,
    pesapal_tracking_id VARCHAR(255),
    pesapal_merchant_reference VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50),
    status ENUM('pending','processing','completed','failed','refunded'),
    pesapal_status VARCHAR(50),
    customer_name VARCHAR(255),
    customer_email VARCHAR(255),
    customer_phone VARCHAR(20),
    payment_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id)
);
```

### Quotes Table (Payment-Related Fields)
```sql
ALTER TABLE quotes ADD COLUMN (
    total_amount DECIMAL(10,2),
    paid_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_status ENUM('pending','partially_paid','fully_paid') DEFAULT 'pending',
    payment_link_token VARCHAR(255),
    payment_link_expires TIMESTAMP NULL
);
```

## API Integration Details

### PesapalAPI Class
**Location**: `admin-dashboard/classes/PesapalAPI.php`

**Key Methods**:
- `authenticate()`: Get access token
- `submitOrderRequest()`: Create payment order
- `getTransactionStatus()`: Check payment status
- `registerIPN()`: Register callback URL

**Error Handling**:
- Connection timeouts
- API rate limiting
- Invalid responses
- Network failures

### Callback Handling
**IPN URL**: `https://yourdomain.com/payment-ipn.php`
**Callback URL**: `https://yourdomain.com/payment-callback.php`

**Security**:
- IP whitelist verification
- Signature validation
- Duplicate prevention
- Error logging

## Email Notifications

### Email Types
1. **Payment Confirmation**: Sent on successful payment
2. **Payment Failure**: Sent on failed payment
3. **Partial Payment**: Sent with remaining balance
4. **Admin Notification**: Payment status updates

### Email Templates
**Features**:
- Professional HTML design
- Company branding
- Payment details
- Next steps information
- Contact information

**Email Service**: PHPMailer with SMTP
**Sender**: Different addresses for different notification types

## Security Features

### Payment Security
- **HTTPS**: Required for all payment pages
- **CSRF Protection**: All forms protected
- **Input Validation**: Server and client-side
- **SQL Injection Prevention**: Prepared statements
- **XSS Protection**: Output sanitization

### Data Protection
- **PCI Compliance**: No card data stored locally
- **Encryption**: Sensitive data encrypted
- **Access Control**: Admin-only payment management
- **Audit Trail**: All payment actions logged

### Fraud Prevention
- **Amount Validation**: Reasonable amount limits
- **Duplicate Prevention**: Transaction deduplication
- **Rate Limiting**: Payment attempt limits
- **Monitoring**: Suspicious activity detection

## Admin Dashboard Integration

### Payment Management (payments.php)
**Features**:
- Payment history viewing
- Status updates
- Refund processing
- Revenue analytics
- Export functionality

**Statistics Display**:
- Total revenue
- Failed payments count
- Pending amounts
- Payment trends

**Table Features**:
- Pagination (30 items per page)
- Status filtering
- Date range filtering
- Export to PDF/Excel

### Quote Integration
**Payment Links**:
- Automatic generation
- Expiration handling
- Secure token system
- One-time use tokens

**Status Synchronization**:
- Real-time status updates
- Automatic balance calculation
- Payment history tracking
- Customer notifications

## Error Handling & Logging

### Error Types
1. **API Errors**: Pesapal communication issues
2. **Validation Errors**: Form input problems
3. **Database Errors**: Data storage issues
4. **Network Errors**: Connection problems

### Logging System
**Log Files**:
- `payment_errors.log`: Payment-specific errors
- `pesapal_api.log`: API communication logs
- `payment_transactions.log`: Transaction history

**Log Levels**:
- ERROR: Critical issues
- WARNING: Potential problems
- INFO: General information
- DEBUG: Detailed debugging

## Testing & Development

### Sandbox Testing
**Test Cards**: Provided by Pesapal
**Test Amounts**: Various amounts for different scenarios
**Test Scenarios**:
- Successful payments
- Failed payments
- Partial payments
- Network timeouts

### Development Setup
1. Configure sandbox credentials
2. Set up local SSL certificate
3. Configure callback URLs
4. Test payment flow
5. Verify email notifications

## Deployment & Production

### Production Checklist
- [ ] Update to live Pesapal credentials
- [ ] Configure production callback URLs
- [ ] Enable SSL certificate
- [ ] Set up monitoring
- [ ] Configure backup systems
- [ ] Test payment flow
- [ ] Set up error alerting

### Monitoring
**Key Metrics**:
- Payment success rate
- Average transaction time
- Error frequency
- Revenue tracking

**Alerts**:
- Payment failures
- API errors
- High error rates
- System downtime

## Troubleshooting

### Common Issues
1. **Payment Failures**: Check API credentials and network
2. **Callback Issues**: Verify URL accessibility
3. **Status Sync**: Check IPN configuration
4. **Email Problems**: Verify SMTP settings

### Debug Steps
1. Check error logs
2. Verify API credentials
3. Test callback URLs
4. Validate SSL certificate
5. Check database connections

### Support Resources
- Pesapal Developer Documentation
- API Status Page
- Technical Support Contact
- Community Forums

## Maintenance

### Regular Tasks
- Monitor payment success rates
- Review error logs
- Update API credentials
- Backup payment data
- Test callback functionality

### Updates
- API version updates
- Security patches
- Feature enhancements
- Bug fixes
- Performance optimizations
