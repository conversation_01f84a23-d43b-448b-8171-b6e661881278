# Authentication Troubleshooting Guide

## 🎯 **Your Exact Issue: Authentication Failed**

The diagnostic shows that everything is working correctly EXCEPT authentication. This means:
- ✅ IMAP extension is loaded
- ✅ Network connectivity is working
- ✅ Server hostname resolves correctly
- ❌ **Email credentials are being rejected**

## 🔍 **Root Causes & Solutions**

### **Cause 1: Wrong Email Password (Most Common)**

**Check This First:**
1. Can you log into webmail with the same credentials?
2. Go to: `https://webmail.melevatours.co.ke` or your hosting control panel
3. Try logging in with:
   - Email: `<EMAIL>`
   - Password: `1V^bAvDR!%)6,C&A`

**If webmail login fails:** The password is wrong
**If webmail login works:** Continue to other causes below

### **Cause 2: IMAP Disabled for Email Accounts**

Many hosting providers disable IMAP by default for security reasons.

**How to Check:**
1. Log into your hosting control panel (cPanel, Plesk, etc.)
2. Go to "Email Accounts" or "Mail"
3. Look for IMAP settings or "Mail Client Configuration"
4. Check if IMAP is enabled

**How to Fix:**
- Enable IMAP in your hosting control panel
- Or contact your hosting provider: "Please enable IMAP access for my email accounts"

### **Cause 3: App-Specific Passwords Required**

Some hosting providers require different passwords for IMAP access vs. webmail access.

**How to Check:**
1. Look in your hosting control panel for "App Passwords" or "Mail Client Passwords"
2. Check if there's a separate section for IMAP/POP3 passwords

**How to Fix:**
- Generate app-specific passwords for IMAP access
- Use these instead of your regular email password

### **Cause 4: Email Accounts Don't Exist**

**How to Check:**
1. Log into your hosting control panel
2. Go to "Email Accounts"
3. Verify these accounts exist:
   - `<EMAIL>`
   - `<EMAIL>`

**How to Fix:**
- Create the email accounts if they don't exist
- Set passwords for them

## 🚀 **Step-by-Step Troubleshooting**

### **Step 1: Test Email Login Tool**
Use: `admin-dashboard/test-email-login.php`

This lets you:
- Test different passwords
- Try different server settings
- See exact error messages

### **Step 2: Contact Your Hosting Provider**

If the login tool doesn't work, contact your hosting provider with these specific questions:

**Email Template:**
```
Subject: Need IMAP Settings for Email Accounts

Hi,

I'm trying to set up IMAP access for my email accounts on domain melevatours.co.ke.

I need help with:
1. Are IMAP services enabled for my domain?
2. What are the correct IMAP server settings?
3. Do I need app-specific passwords for IMAP access?
4. Are these email accounts active: <EMAIL>.<NAME_EMAIL>?

Current settings I'm trying:
- Host: mail.melevatours.co.ke
- Port: 993
- Encryption: SSL

Error message: "Authentication failed"

Please provide the correct IMAP settings.

Thanks!
```

### **Step 3: Try Alternative Authentication Methods**

Some servers use different authentication methods:

**Test These Combinations:**
1. **Standard:** `mail.melevatours.co.ke:993/ssl`
2. **Alternative host:** `melevatours.co.ke:993/ssl`
3. **Non-SSL:** `mail.melevatours.co.ke:143/tls`
4. **No encryption:** `mail.melevatours.co.ke:143/none`

## 🔧 **Common Hosting Provider Solutions**

### **cPanel Hosting**
1. Login to cPanel
2. Go to "Email Accounts"
3. Click "Configure Mail Client" next to your email
4. Look for IMAP settings
5. Check if "Secure SSL/TLS Settings" are different

### **Plesk Hosting**
1. Login to Plesk
2. Go to "Mail"
3. Click on your email account
4. Check "Mail settings"
5. Verify IMAP is enabled

### **DirectAdmin**
1. Login to DirectAdmin
2. Go to "E-Mail Accounts"
3. Click "Modify" next to your email
4. Check IMAP settings

## 📞 **What to Ask Your Hosting Provider**

**Essential Questions:**
1. "Is IMAP enabled for my email accounts on melevatours.co.ke?"
2. "What are the exact IMAP server settings (hostname, port, encryption)?"
3. "Do I need app-specific passwords for IMAP access?"
4. "Are there any firewall restrictions on IMAP connections?"
5. "Can you test IMAP login from your end with my credentials?"

**Provide Them:**
- Your domain: `melevatours.co.ke`
- Email accounts: `<EMAIL>`, `<EMAIL>`
- Error message: "Authentication failed"
- What you're trying to do: "Set up automated email fetching via PHP IMAP"

## 🎯 **Quick Fixes to Try Right Now**

### **Fix 1: Test Different Passwords**
1. Go to `admin-dashboard/test-email-login.php`
2. Try your cPanel password (if different from email password)
3. Try any app-specific passwords you might have

### **Fix 2: Check Email Account Status**
1. Try sending an email TO `<EMAIL>`
2. Check if it bounces back
3. If it bounces, the account doesn't exist

### **Fix 3: Test Webmail Access**
1. Go to your webmail (usually `webmail.melevatours.co.ke`)
2. Try logging in with the same credentials
3. If it fails, you have the wrong password

### **Fix 4: Check for Typos**
Double-check your password for:
- Extra spaces
- Wrong capitalization
- Similar-looking characters (0 vs O, 1 vs l, etc.)

## 🔄 **Next Steps Based on Results**

### **If Webmail Works But IMAP Doesn't:**
- IMAP is likely disabled
- Contact hosting provider to enable it

### **If Webmail Doesn't Work:**
- Wrong password
- Account doesn't exist
- Reset password in hosting control panel

### **If Nothing Works:**
- Contact hosting provider immediately
- Ask them to test IMAP access from their end

## 📋 **Information to Gather**

Before contacting support, gather this information:
- [ ] Hosting provider name
- [ ] Control panel type (cPanel, Plesk, etc.)
- [ ] Can you access webmail?
- [ ] Do the email accounts exist?
- [ ] Are you using the correct passwords?
- [ ] Any special IMAP settings in your control panel?

The authentication failure is definitely fixable - it's usually just a matter of getting the right credentials or enabling IMAP access! 🎯
