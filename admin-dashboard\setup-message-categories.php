<?php
/**
 * Setup script to add message categories and improve message handling
 * Run this once to update your database with the new message category system
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'classes/models.php';

// Only allow execution from command line or admin users
if (php_sapi_name() !== 'cli') {
    // Include security middleware for web access
    require_once 'includes/security_middleware.php';
    
    // Require admin role
    requireRole('admin');
    
    echo "<h1>Message Categories Setup</h1>";
    echo "<p>This script will update the messages system to support categories and better reply handling.</p>";
    echo "<p><strong>Warning:</strong> This will modify your database structure. Make sure to backup your database first!</p>";
    
    if (!isset($_POST['confirm_setup'])) {
        echo '<form method="POST">';
        echo '<input type="hidden" name="confirm_setup" value="1">';
        echo csrf_field();
        echo '<button type="submit" onclick="return confirm(\'Are you sure you want to proceed? This will modify your database structure!\')" style="background: #f97316; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Proceed with Setup</button>';
        echo '</form>';
        exit;
    }
    
    echo "<pre>";
}

try {
    $db = DatabaseConfig::getConnection();
    
    echo "Starting message categories setup...\n";
    
    // 1. Check if message_category column exists
    echo "1. Checking message_category column...\n";
    
    $stmt = $db->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        echo "   Adding message_category column...\n";
        $db->exec("ALTER TABLE messages ADD COLUMN message_category ENUM('contact', 'quote', 'general') DEFAULT 'general' AFTER message_type");
        echo "   ✓ message_category column added\n";
    } else {
        echo "   ✓ message_category column already exists\n";
    }
    
    // 2. Check if parent_message_id column exists
    echo "2. Checking parent_message_id column...\n";
    
    $stmt = $db->prepare("SHOW COLUMNS FROM messages LIKE 'parent_message_id'");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        echo "   Adding parent_message_id column...\n";
        $db->exec("ALTER TABLE messages ADD COLUMN parent_message_id INT NULL AFTER original_message_id");
        echo "   ✓ parent_message_id column added\n";
    } else {
        echo "   ✓ parent_message_id column already exists\n";
    }
    
    // 3. Check if is_reply column exists
    echo "3. Checking is_reply column...\n";
    
    $stmt = $db->prepare("SHOW COLUMNS FROM messages LIKE 'is_reply'");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        echo "   Adding is_reply column...\n";
        $db->exec("ALTER TABLE messages ADD COLUMN is_reply BOOLEAN DEFAULT FALSE AFTER parent_message_id");
        echo "   ✓ is_reply column added\n";
    } else {
        echo "   ✓ is_reply column already exists\n";
    }
    
    // 4. Add foreign key constraint for parent_message_id if it doesn't exist
    echo "4. Checking foreign key constraints...\n";
    
    $stmt = $db->prepare("SELECT CONSTRAINT_NAME FROM information_schema.KEY_COLUMN_USAGE WHERE TABLE_NAME = 'messages' AND COLUMN_NAME = 'parent_message_id' AND CONSTRAINT_NAME != 'PRIMARY'");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        echo "   Adding foreign key constraint for parent_message_id...\n";
        try {
            $db->exec("ALTER TABLE messages ADD FOREIGN KEY (parent_message_id) REFERENCES messages(message_id) ON DELETE SET NULL");
            echo "   ✓ Foreign key constraint added\n";
        } catch (Exception $e) {
            echo "   ! Foreign key constraint already exists or couldn't be added: " . $e->getMessage() . "\n";
        }
    } else {
        echo "   ✓ Foreign key constraint already exists\n";
    }
    
    // 5. Add indexes for better performance
    echo "5. Adding indexes...\n";
    
    try {
        $db->exec("ALTER TABLE messages ADD INDEX idx_message_category (message_category)");
        echo "   ✓ Index on message_category added\n";
    } catch (Exception $e) {
        echo "   ! Index on message_category already exists\n";
    }
    
    try {
        $db->exec("ALTER TABLE messages ADD INDEX idx_parent_message_id (parent_message_id)");
        echo "   ✓ Index on parent_message_id added\n";
    } catch (Exception $e) {
        echo "   ! Index on parent_message_id already exists\n";
    }
    
    // 6. Update existing messages with categories based on subject
    echo "6. Updating existing message categories...\n";
    
    $stmt = $db->prepare("UPDATE messages SET message_category = 'contact' WHERE (subject LIKE '%Contact Form%' OR subject LIKE '%contact%' OR subject LIKE '%Contact%') AND message_category = 'general'");
    $stmt->execute();
    $contactUpdated = $stmt->rowCount();
    echo "   Updated $contactUpdated messages to 'contact' category\n";
    
    $stmt = $db->prepare("UPDATE messages SET message_category = 'quote' WHERE (subject LIKE '%Quote Request%' OR subject LIKE '%quote%' OR subject LIKE '%Quote%' OR subject LIKE '%booking%' OR subject LIKE '%Booking%') AND message_category = 'general'");
    $stmt->execute();
    $quoteUpdated = $stmt->rowCount();
    echo "   Updated $quoteUpdated messages to 'quote' category\n";
    
    // 7. Create a view for better message management (optional)
    echo "7. Creating message threads view...\n";
    
    try {
        $db->exec("CREATE OR REPLACE VIEW message_threads AS
            SELECT 
                m.*,
                CASE 
                    WHEN m.parent_message_id IS NULL THEN m.message_id
                    ELSE COALESCE(pm.parent_message_id, m.parent_message_id)
                END as thread_root_id,
                (SELECT COUNT(*) FROM messages m2 WHERE m2.parent_message_id = m.message_id) as reply_count
            FROM messages m
            LEFT JOIN messages pm ON m.parent_message_id = pm.message_id
            ORDER BY m.received_at DESC");
        echo "   ✓ Message threads view created\n";
    } catch (Exception $e) {
        echo "   ! Could not create view: " . $e->getMessage() . "\n";
    }
    
    echo "\nSetup completed successfully!\n";
    echo "\nSummary of changes:\n";
    echo "- Added message_category column for better message organization\n";
    echo "- Added parent_message_id and is_reply columns for reply tracking\n";
    echo "- Added indexes for better performance\n";
    echo "- Updated existing messages with appropriate categories\n";
    echo "- Created message_threads view for advanced querying\n";
    echo "\nThe messages system now supports:\n";
    echo "1. Separate filtering for Contact and Quote messages\n";
    echo "2. Better reply tracking and threading\n";
    echo "3. Improved performance with proper indexes\n";
    echo "\nNext steps:\n";
    echo "1. Test the message filtering in the admin dashboard\n";
    echo "2. Set up email reply handling if needed\n";
    echo "3. Monitor the system for any issues\n";
    
} catch (Exception $e) {
    echo "Error during setup: " . $e->getMessage() . "\n";
    echo "Setup failed. Please check the error and try again.\n";
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "</pre>";
    echo "<p><strong>Setup completed!</strong> You can now use the improved message system.</p>";
    echo "<p><a href='messages.php'>Go to Messages</a> | <a href='index.php'>Go to Dashboard</a></p>";
}
?>
