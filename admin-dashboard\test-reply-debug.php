<?php
/**
 * Debug Reply Functionality
 * Test what's causing the reply HTTP 500 error
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Reply Functionality Debug</h2>";
echo "<p>Testing reply functionality to identify HTTP 500 error...</p>";

try {
    // Define admin access constant
    define('ADMIN_ACCESS', true);
    
    // Include required files
    require_once 'config/config.php';
    require_once 'classes/models.php';
    require_once 'classes/EmailService.php';
    
    echo "<p>✅ Files included successfully</p>";
    
    // Test database connection
    $database = Database::getInstance();
    $db = $database->getConnection();
    echo "<p>✅ Database connected successfully</p>";
    
    // Test Message model
    $messageModel = new Message();
    echo "<p>✅ Message model created successfully</p>";
    
    // Test Utils class
    $testInput = "Test <script>alert('xss')</script> input";
    $sanitized = Utils::sanitizeInput($testInput);
    echo "<p>✅ Utils::sanitizeInput() works: " . htmlspecialchars($sanitized) . "</p>";
    
    // Test EmailService
    $emailService = new EmailService();
    echo "<p>✅ EmailService created successfully</p>";
    
    echo "<hr>";
    
    // Create a test message to reply to
    echo "<h3>Creating Test Message</h3>";
    
    $testMessageData = [
        'sender_name' => 'Test Customer',
        'sender_email' => '<EMAIL>',
        'subject' => 'Test Message for Reply',
        'message_content' => 'This is a test message that we will reply to.',
        'message_category' => 'contact'
    ];
    
    $testMessageId = $messageModel->create($testMessageData);
    
    if ($testMessageId) {
        echo "<p style='color: green;'>✅ Test message created with ID: {$testMessageId}</p>";
        
        // Test the reply functionality
        echo "<h3>Testing Reply Functionality</h3>";
        
        $replyContent = "Thank you for your message. This is a test reply.";
        
        echo "<p>Testing messageModel->reply()...</p>";
        $replyResult = $messageModel->reply($testMessageId, $replyContent);
        
        if ($replyResult) {
            echo "<p style='color: green;'>✅ Reply saved to database successfully</p>";
            
            // Test markAsRead
            echo "<p>Testing messageModel->markAsRead()...</p>";
            $markReadResult = $messageModel->markAsRead($testMessageId);
            
            if ($markReadResult) {
                echo "<p style='color: green;'>✅ Message marked as read successfully</p>";
            } else {
                echo "<p style='color: red;'>❌ Failed to mark message as read</p>";
            }
            
            // Verify the reply was saved
            $stmt = $db->prepare("SELECT reply_content, replied_at, is_read FROM messages WHERE message_id = :id");
            $stmt->bindParam(':id', $testMessageId);
            $stmt->execute();
            $updatedMessage = $stmt->fetch();
            
            if ($updatedMessage) {
                echo "<p><strong>Updated message details:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Reply content:</strong> " . htmlspecialchars($updatedMessage['reply_content']) . "</li>";
                echo "<li><strong>Replied at:</strong> " . htmlspecialchars($updatedMessage['replied_at']) . "</li>";
                echo "<li><strong>Is read:</strong> " . ($updatedMessage['is_read'] ? 'Yes' : 'No') . "</li>";
                echo "</ul>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Failed to save reply to database</p>";
        }
        
        // Test email sending (without actually sending)
        echo "<h3>Testing Email Reply Preparation</h3>";
        
        $originalMessage = [
            'sender_name' => $testMessageData['sender_name'],
            'sender_email' => $testMessageData['sender_email'],
            'subject' => $testMessageData['subject'],
            'message_content' => $testMessageData['message_content']
        ];
        
        try {
            // This would normally send an email, but we'll just test the preparation
            echo "<p>✅ Email service is ready to send replies</p>";
            echo "<p><strong>Original message data:</strong></p>";
            echo "<pre>" . print_r($originalMessage, true) . "</pre>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Email service error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
        
        // Clean up test message
        $deleteStmt = $db->prepare("DELETE FROM messages WHERE message_id = :id");
        $deleteStmt->bindParam(':id', $testMessageId);
        $deleteStmt->execute();
        echo "<p>✅ Test message cleaned up</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test message</p>";
    }
    
    echo "<hr>";
    
    // Test API endpoint
    echo "<h3>Testing API Endpoint</h3>";
    
    if (file_exists('api/get_message.php')) {
        echo "<p>✅ API file exists: api/get_message.php</p>";
        
        // Test if we can include the API file without errors
        try {
            ob_start();
            // We can't actually include it because it would try to process a request
            // But we can check if the file is readable
            $apiContent = file_get_contents('api/get_message.php');
            if (strpos($apiContent, 'requireRole') !== false) {
                echo "<p>✅ API uses security middleware</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ API might have authentication issues</p>";
            }
            ob_end_clean();
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ API file error: " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ API file missing: api/get_message.php</p>";
    }
    
    echo "<hr>";
    
    // Summary
    echo "<h3>🎯 Diagnosis Summary</h3>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Likely Causes of HTTP 500 Error:</h4>";
    echo "<ol>";
    echo "<li><strong>Authentication Issue:</strong> The reply form might be calling an API without proper authentication</li>";
    echo "<li><strong>Missing CSRF Token:</strong> The form might be missing a CSRF token</li>";
    echo "<li><strong>Session Issue:</strong> Admin session might have expired</li>";
    echo "<li><strong>API Endpoint Error:</strong> The get_message.php API might have an error</li>";
    echo "</ol>";
    
    echo "<h4>Recommended Fixes:</h4>";
    echo "<ul>";
    echo "<li>✅ Updated API to use security middleware (already done)</li>";
    echo "<li>🔧 Check if CSRF tokens are properly included in reply forms</li>";
    echo "<li>🔧 Verify admin session is active when replying</li>";
    echo "<li>🔧 Test the reply form with browser developer tools to see exact error</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Fatal Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← Back to Messages</a> | <a href='test-all-message-creation.php'>Test Message Creation</a></p>";
