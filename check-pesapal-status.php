<?php
/**
 * Check Pesapal Transaction Status
 * This endpoint helps debug what Pesapal is actually returning
 */

header('Content-Type: application/json');

require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/config/pesapal_config.php';
require_once 'admin-dashboard/classes/PesapalService.php';

$trackingId = $_GET['tracking_id'] ?? null;

if (!$trackingId) {
    http_response_code(400);
    echo json_encode(['error' => 'Tracking ID is required']);
    exit;
}

try {
    $pesapalService = new PesapalService();
    
    // Get auth token
    $authToken = $pesapalService->getAuthToken();
    
    if (!$authToken) {
        throw new Exception('Failed to get Pesapal auth token');
    }
    
    // Get transaction status
    $transactionStatus = $pesapalService->getTransactionStatus($authToken, $trackingId);
    
    if (!$transactionStatus) {
        throw new Exception('Failed to get transaction status from Pesapal');
    }
    
    // Return the full response for debugging
    echo json_encode([
        'success' => true,
        'tracking_id' => $trackingId,
        'auth_token_received' => !empty($authToken),
        'pesapal_response' => $transactionStatus,
        'payment_status_description' => $transactionStatus['payment_status_description'] ?? 'Not provided',
        'status_code' => $transactionStatus['status_code'] ?? 'Not provided',
        'payment_method' => $transactionStatus['payment_method'] ?? 'Not provided',
        'amount' => $transactionStatus['amount'] ?? 'Not provided',
        'currency' => $transactionStatus['currency'] ?? 'Not provided',
        'created_date' => $transactionStatus['created_date'] ?? 'Not provided',
        'confirmation_code' => $transactionStatus['confirmation_code'] ?? 'Not provided',
        'mapped_status' => mapPesapalStatus($transactionStatus['payment_status_description'] ?? ''),
        'debug_info' => [
            'pesapal_environment' => PESAPAL_ENVIRONMENT,
            'base_url' => PESAPAL_BASE_URL,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage(),
        'tracking_id' => $trackingId,
        'debug_info' => [
            'pesapal_environment' => PESAPAL_ENVIRONMENT,
            'base_url' => PESAPAL_BASE_URL,
            'timestamp' => date('Y-m-d H:i:s')
        ]
    ], JSON_PRETTY_PRINT);
}

/**
 * Map Pesapal status to our internal status
 */
function mapPesapalStatus($pesapalStatus) {
    switch (strtolower($pesapalStatus)) {
        case 'completed':
        case 'success':
            return 'completed';
        case 'failed':
        case 'invalid':
            return 'failed';
        case 'cancelled':
            return 'cancelled';
        default:
            return 'pending';
    }
}
?>
