<?php
require_once '../config/config.php';
require_once '../classes/models.php';
require_once '../classes/additional_models.php';

// Require authentication
Auth::requireLogin();

// Set content type to JSON
header('Content-Type: application/json');

// Get the request method and path
$method = $_SERVER['REQUEST_METHOD'];
$requestUri = $_SERVER['REQUEST_URI'];

// Parse the URL to extract resource type and ID
// Expected format: /api/images.php/destinations/123/images or /api/images.php/packages/123/images
$pathInfo = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
$pathParts = explode('/', trim($pathInfo, '/'));

$resourceType = null;
$resourceId = null;

if (count($pathParts) >= 3) {
    $resourceType = $pathParts[0]; // 'destinations' or 'packages'
    $resourceId = intval($pathParts[1]);
    $action = $pathParts[2]; // 'images'
}

try {
    switch ($method) {
        case 'GET':
            if ($resourceType && $resourceId && isset($action) && $action === 'images') {
                $imageModel = new Image();
                $images = [];
                
                if ($resourceType === 'destinations') {
                    $images = $imageModel->findByType('destination', $resourceId);
                } elseif ($resourceType === 'packages') {
                    $images = $imageModel->findByType('tour_package', $resourceId);
                } else {
                    throw new Exception('Invalid resource type');
                }
                
                // Process images for frontend display
                foreach ($images as &$image) {
                    // The URLs are stored as 'upload/images/destinations/filename.jpg'
                    // Since the API is called from admin-dashboard context, these paths work directly
                    // No modification needed - the stored URLs are correct for the admin dashboard

                    // Decode HTML entities in alt_text for proper display
                    if (isset($image['alt_text'])) {
                        $image['alt_text'] = html_entity_decode($image['alt_text'], ENT_QUOTES, 'UTF-8');
                    }

                    $image['is_display_image'] = (bool)$image['is_display_image'];
                }
                
                echo json_encode([
                    'success' => true,
                    'data' => $images
                ]);
            } else {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'error' => 'Invalid request format. Expected: /destinations/{id}/images or /packages/{id}/images'
                ]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode([
                'success' => false,
                'error' => 'Method not allowed'
            ]);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
