<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';

// Initialize models
$destinationModel = new Destination();
$tourPackageModel = new TourPackage();

// Fetch destinations from database (limit to 8 for display)
$destinations = $destinationModel->findAllWithImages();

// Ensure we always have exactly 8 cards
$displayDestinations = array_slice($destinations, 0, 8);

// Fill remaining slots with placeholder data if needed
while (count($displayDestinations) < 8) {
    $displayDestinations[] = [
        'destination_id' => null,
        'name' => 'Coming Soon',
        'short_description' => 'New exciting destination will be available soon. Stay tuned for amazing adventures!',
        'price' => 0,
        'display_image_url' => null,
        'created_by' => 'Meleva Tours'
    ];
}

// Fetch featured tour packages (limit to 7 for display)
$featuredPackages = $tourPackageModel->findFeaturedWithDetails(7);

// Create placeholder packages if we have fewer than 7 featured packages
$displayPackages = $featuredPackages;
while (count($displayPackages) < 7) {
    $displayPackages[] = [
        'tour_package_id' => null,
        'name' => 'Coming Soon',
        'description' => 'New exciting tour package will be available soon. Stay tuned for amazing adventures!',
        'price' => 0,
        'duration' => 'TBD',
        'display_image_url' => null,
        'type_name' => 'Adventure Tours'
    ];
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meleva Tours & Travel | Authentic African Safari Adventures</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="style.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <style>
        /* ===================================
           INDEX PAGE SPECIFIC STYLES
           =================================== */

        .destination-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }

        .destination-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            border-left-color: #f97316;
        }

        /* Custom navigation buttons for swiper */
        .swiper-button-next-custom,
        .swiper-button-prev-custom {
            width: 48px;
            height: 48px;
            background: white;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            color: #f97316;
            transition: all 0.3s ease;
            transform-origin: center center;
        }

        .swiper-button-next-custom:hover,
        .swiper-button-prev-custom:hover {
            color: #ea580c;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
        }

        /* Scale effect for navigation buttons without affecting position */
        .swiper-button-next-custom:hover svg,
        .swiper-button-prev-custom:hover svg {
            transform: scale(1.1);
        }

        /* Hero section animations */
        .hero-content {
            animation: fadeInUp 1s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Section animations */
        .animate-fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Hero Section Styles */
        .hero-overlay {
            background: linear-gradient(135deg, rgba(0, 0, 0, 0.6), rgba(249, 115, 22, 0.3));
        }

        .text-shadow {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
        }

        .parallax-bg {
            background-attachment: fixed;
        }

        @media (max-width: 768px) {
            .parallax-bg {
                background-attachment: scroll;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="relative h-[100vh] bg-cover bg-center bg-no-repeat parallax-bg" style="background-image: url('images/hero-bg.jpg')">
        <div class="absolute inset-0 hero-overlay"></div>
        
        <!-- Hero Content -->
        <div class="relative z-10 flex flex-col items-center justify-center text-center h-full px-4">
            <h1 class="text-4xl md:text-6xl font-semibold text-white mb-4 text-shadow">
                Explore <span class="text-orange-400">Africa</span> with Us
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-6"></div>
            <p class="text-xl md:text-2xl text-white mb-8 max-w-3xl text-shadow">Experience authentic and unforgettable safari adventures tailored just for you.</p>
            <div class="flex flex-col sm:flex-row gap-4">
                <a href="#destinations" class="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105">Explore Destinations</a>
                <a href="request-quote.php" class="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-8 py-4 rounded-full font-semibold transition duration-300">Plan Your Safari</a>
            </div>
        </div>
        
        <!-- Scroll Indicator -->
        <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="3" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
        </div>
    </section>

    <!-- Popular Destinations Section -->
    <section id="destinations" class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Popular <span class="gradient-text">Destinations</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Discover Kenya's most breathtaking destinations, from world-renowned safari parks to pristine coastal paradises
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php foreach ($displayDestinations as $index => $destination): ?>
                    <!-- Destination Card <?php echo $index + 1; ?> -->
                    <div class="bg-white rounded-2xl shadow-lg overflow-hidden destination-card image-hover-zoom">
                        <?php if ($destination['display_image_url']): ?>
                            <img data-src="admin-dashboard/<?php echo htmlspecialchars($destination['display_image_url']); ?>"
                                alt="<?php echo htmlspecialchars($destination['name']); ?>"
                                class="w-full h-48 object-cover lazy-image"
                                loading="lazy"
                                decoding="async">
                        <?php else: ?>
                            <div class="w-full h-48 bg-gray-200 flex items-center justify-center">
                                <div class="text-center text-gray-500">
                                    <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <p class="text-sm">No Image Available</p>
                                </div>
                            </div>
                        <?php endif; ?>

                        <div class="relative p-6 pt-2 h-32">
                            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                                <?php echo Utils::displayText($destination['name']); ?>
                            </h3>
                            <p class="text-gray-700 mb-4 text-sm line-clamp-2">
                                <?php
                                $description = $destination['short_description'] ?: 'Discover this amazing destination with unique experiences and unforgettable memories.';
                                echo Utils::displayText($description);
                                ?>
                            </p>
                            <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                <span class="text-base font-semibold gradient-text">
                                    <?php if ($destination['price'] > 0): ?>
                                        From $<?php echo number_format($destination['price']); ?>
                                    <?php else: ?>
                                        From $0
                                    <?php endif; ?>
                                </span>
                                <?php if ($destination['destination_id']): ?>
                                    <a href="destination-details.php?id=<?php echo $destination['destination_id']; ?>&from=index"
                                    class="text-orange-500 hover:text-orange-600 font-medium transition duration-300">
                                        Learn More →
                                    </a>
                                <?php else: ?>
                                    <span class="text-gray-400 font-medium">
                                        Coming Soon
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- View More Destinations Button -->
            <div class="text-center mt-12">
                <a href="tours.php#destinations" class="inline-block bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105">
                    View All Destinations
                </a>
            </div>
        </div>
    </section>

    <!-- Featured Tour Packages Section - Now with dark background -->
    <section id="packages" class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold mb-6">
                    Featured <span class="text-orange-400">Tour Packages</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Carefully curated safari experiences designed to create unforgettable memories
                </p>
            </div>
            
            <!-- Navigation and Swiper Container -->
            <div class="relative">
                <!-- Navigation Buttons - Outside the swiper (hidden on mobile) -->
                <button class="swiper-button-prev-custom hidden md:flex absolute left-0 top-1/2 -translate-y-1/2 -translate-x-4 z-10 w-12 h-12 bg-gray-900 rounded-full shadow-lg items-center justify-center text-orange-500 hover:text-orange-600 hover:shadow-xl transition-all duration-300 group">
                    <svg class="w-6 h-6 transition-transform" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
                    </svg>
                </button>

                <button class="swiper-button-next-custom hidden md:flex absolute right-0 top-1/2 -translate-y-1/2 translate-x-4 z-10 w-12 h-12 bg-gray-900 rounded-full shadow-lg items-center justify-center text-orange-500 hover:text-orange-600 hover:shadow-xl transition-all duration-300 group">
                    <svg class="w-6 h-6 transition-transform" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                    </svg>
                </button>

                <!-- Swiper Container -->
                <div class="swiper mx-0 md:mx-8">
                    <div class="swiper-wrapper">
                        <?php foreach ($displayPackages as $index => $package): ?>
                            <!-- Package Slide <?php echo $index + 1; ?> -->
                            <div class="swiper-slide p-4">
                                <div class="bg-gray-900 rounded-2xl shadow-lg overflow-hidden card-hover image-hover-zoom">
                                    <?php if ($package['display_image_url']): ?>
                                        <img data-src="admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?>"
                                            alt="<?php echo htmlspecialchars($package['name']); ?>"
                                            class="w-full h-48 object-cover lazy-image"
                                            loading="lazy"
                                            decoding="async">
                                    <?php else: ?>
                                        <div class="w-full h-48 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                            <div class="text-center text-white">
                                                <svg class="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                </svg>
                                                <p class="text-sm">Coming Soon</p>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <div class="relative pt-4 p-6 h-48">
                                        <h3 class="text-lg font-semibold text-white mb-2 line-clamp-2">
                                            <?php
                                            if ($package['tour_package_id']) {
                                                // Smart formatting based on package type and duration
                                                $packageName = $package['name'];
                                                $tourType = $package['type_name'] ?: 'Tour';
                                                $duration = $package['duration'];

                                                // Check if tour type already implies duration
                                                $lowerTourType = strtolower($tourType);
                                                $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                                                    strpos($lowerTourType, 'overnight') !== false ||
                                                                    strpos($lowerTourType, 'night') !== false);

                                                if ($typeImpliesDuration) {
                                                    // Format: Tour Type + Package Name
                                                    $formattedName = $tourType . ' ' . $packageName;
                                                } elseif (!empty($duration)) {
                                                    // Format: Duration + Package Name + Tour Type
                                                    $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                                } else {
                                                    // Format: Package Name + Tour Type
                                                    $formattedName = $packageName . ' ' . $tourType;
                                                }

                                                echo Utils::displayText($formattedName);
                                            } else {
                                                echo Utils::displayText($package['name']);
                                            }
                                            ?>
                                        </h3>
                                        <p class="text-gray-200 mb-4 text-base line-clamp-2">
                                            <?php
                                            $description = $package['description'] ?: 'Discover this amazing tour package with unique experiences and unforgettable memories.';
                                            echo Utils::displayText($description);
                                            ?>
                                        </p>
                                        <div class="absolute bottom-0 inset-x-6 mb-4 flex justify-between items-center">
                                            <span class="text-base font-semibold text-orange-400">
                                                $<?php echo number_format($package['price']); ?>
                                            </span>
                                            <?php if ($package['tour_package_id']): ?>
                                                <a href="package-details.php?id=<?php echo $package['tour_package_id']; ?>&from=index"
                                                class="text-sm bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-full font-medium transition duration-300 flex items-center">
                                                    View Details <i class="fas fa-arrow-right ml-2"></i>
                                                </a>
                                            <?php else: ?>
                                                <span class="text-sm bg-gray-600 text-white px-6 py-2 rounded-full font-medium">
                                                    Coming Soon
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination Bullets -->
                    <div class="swiper-pagination mt-8"></div>
                </div>
            </div>

            <!-- View More Packages Button -->
            <div class="text-center mt-12">
                <a href="tours.php#packages" class="inline-block bg-orange-500 hover:bg-orange-600 text-white px-8 py-3 rounded-full font-semibold transition duration-300 transform hover:scale-105">
                    View All Packages
                </a>
            </div>
        </div>
    </section>

    <!-- Why Explore with Us Section - Now with light background and new layout -->
    <section class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <!-- Centered Heading Section -->
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    Why Explore with <span class="gradient-text">Meleva Tours</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Experience the difference of authentic African adventures with local expertise and personalized service
                </p>
            </div>
            
            <!-- Left-Right Content Division -->
            <div class="flex flex-col lg:flex-row gap-12 items-center">
                <!-- Key Points on Left -->
                <div class="lg:w-1/2">
                    <div class="space-y-8">
                        <!-- Key Point 1 -->
                        <div class="flex items-start gap-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Local Expertise</h3>
                                <p class="text-gray-600 leading-relaxed">Our local experts from Mavueni, Kilifi County ensure authentic and immersive experiences, sharing deep knowledge of Africa's culture and wildlife.</p>
                            </div>
                        </div>
                        
                        <!-- Key Point 2 -->
                        <div class="flex items-start gap-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Tailored Itineraries</h3>
                                <p class="text-gray-600 leading-relaxed">Customized travel plans designed to match your interests, from adventure to relaxation, for a perfect journey that exceeds expectations.</p>
                            </div>
                        </div>
                        
                        <!-- Key Point 3 -->
                        <div class="flex items-start gap-6">
                            <div class="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">Sustainable Travel</h3>
                                <p class="text-gray-600 leading-relaxed">We prioritize eco-friendly practices and support local communities, ensuring your trip makes a positive impact on conservation efforts.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Image on Right -->
                <div class="lg:w-1/2">
                    <div class="relative rounded-2xl overflow-hidden shadow-xl h-96 lg:h-[500px] transform transition-all duration-500 hover:shadow-2xl">
                        <img src="images/why-us.jpg" alt="Meleva Tours Safari Experience" 
                            class="w-full h-full object-cover transition-transform duration-500 hover:scale-105">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent"></div>
                        <div class="absolute bottom-0 left-0 p-8 text-white">
                            <h3 class="text-2xl font-bold mb-2">Authentic African Experiences</h3>
                            <p class="text-lg">From Kilifi to the heart of Kenya's wilderness</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Testimonials Section -->
    <section class="py-10 px-4 bg-white">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-semibold text-gray-900 mb-6">
                    What Our <span class="gradient-text">Travelers Say</span>
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-orange-500 to-red-500 mx-auto mb-8"></div>
                <p class="text-lg md:text-xl text-gray-700 max-w-3xl mx-auto">
                    Real experiences from adventurers who have explored Africa with us
                </p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Testimonial 1 -->
                <div class="bg-white rounded-2xl shadow-lg p-8 card-hover border-l-4 safari-border">
                    <div class="flex items-center mb-4">
                        <div class="flex text-orange-500">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">"The safari experience was absolutely amazing! The guides were incredibly knowledgeable and made every moment unforgettable. Meleva Tours exceeded all our expectations."</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            E
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">Eva K.</p>
                            <p class="text-sm text-gray-500">Nairobi, Kenya - June 2025</p>
                        </div>
                    </div>
                </div>
                
                <!-- Testimonial 2 -->
                <div class="bg-white rounded-2xl shadow-lg p-8 card-hover border-l-4 safari-border">
                    <div class="flex items-center mb-4">
                        <div class="flex text-orange-500">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">"Diani Beach was absolute paradise, and the planning was seamless from start to finish. I highly recommend Meleva Tours for anyone seeking authentic African experiences!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            S
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">Sarah M.</p>
                            <p class="text-sm text-gray-500">Mombasa, Kenya - May 2025</p>
                        </div>
                    </div>
                </div>
                
                <!-- Testimonial 3 -->
                <div class="bg-white rounded-2xl shadow-lg p-8 card-hover border-l-4 safari-border">
                    <div class="flex items-center mb-4">
                        <div class="flex text-orange-500">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">"Mount Kenya trek was challenging but incredibly rewarding. The team provided excellent support throughout the journey. An adventure I'll never forget!"</p>
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center text-white font-semibold mr-4">
                            J
                        </div>
                        <div>
                            <p class="font-semibold text-gray-900">James L.</p>
                            <p class="text-sm text-gray-500">Nakuru, Kenya - April 2025</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Call to Action Section -->
    <section class="py-10 px-4 bg-gradient-to-r from-orange-500 to-red-500 text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-semibold mb-6">Ready to Discover Africa?</h2>
            <p class="text-lg md:text-xl mb-8 opacity-90">
                Let us craft your perfect African adventure. From the coastal beauty of Kilifi to the wild heart of Kenya's national parks, your journey of a lifetime awaits.
            </p>
            <div class="flex flex-col md:flex-row gap-4 justify-center">
                <a href="request-quote.php" class="bg-white text-orange-500 hover:bg-gray-100 px-8 py-4 rounded-full font-semibold transition duration-300 transform hover:scale-105">
                    Plan Your Safari
                </a>
                <a href="about.php" class="border-2 border-white text-white hover:bg-white hover:text-orange-500 px-8 py-4 rounded-full font-semibold transition duration-300">
                    Learn About Us
                </a>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>
    <script>
        // ===================================
        // INDEX PAGE SPECIFIC JAVASCRIPT
        // ===================================

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Swiper for tour packages
            const swiper = new Swiper('.swiper', {
                loop: true,
                slidesPerView: 1,
                spaceBetween: 20,
                navigation: {
                    nextEl: '.swiper-button-next-custom',
                    prevEl: '.swiper-button-prev-custom',
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    dynamicBullets: true,
                    dynamicMainBullets: 3,
                },
                breakpoints: {
                    640: {
                        slidesPerView: 2,
                    },
                    1024: {
                        slidesPerView: 3,
                    },
                },
            });

            // Intersection Observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            // Observe all sections for fade-in animation
            document.querySelectorAll('section').forEach(section => {
                observer.observe(section);
            });
        });
    </script>

    <!-- Image Optimization -->
    <script src="js/image-optimizer.js"></script>

    <?php include 'footer.php'; ?>

</body>
</html>

