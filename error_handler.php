<?php
/**
 * Universal Error Handler for Meleva Tours
 * This file provides a fallback 404 handling mechanism
 * that works regardless of Apache .htaccess configuration
 */

// Function to handle 404 errors
function handle404Error($requestedUrl = '') {
    // Set proper HTTP status code
    if (!headers_sent()) {
        http_response_code(404);
        header('Status: 404 Not Found');
    }
    
    // Store the requested URL for display in 404 page
    $_SERVER['REQUEST_URI'] = $requestedUrl ?: ($_SERVER['REQUEST_URI'] ?? '');
    
    // Include the 404 page
    include_once __DIR__ . '/404.php';
    exit;
}

// Check if this is being called directly
if (basename($_SERVER['SCRIPT_NAME']) === 'error_handler.php') {
    handle404Error();
}

// Auto-detect 404 situations
$currentScript = $_SERVER['SCRIPT_FILENAME'] ?? '';
$requestUri = $_SERVER['REQUEST_URI'] ?? '';

// If the requested PHP file doesn't exist, show 404
if (!empty($currentScript) && !file_exists($currentScript) && strpos($requestUri, '.php') !== false) {
    handle404Error($requestUri);
}
?>
