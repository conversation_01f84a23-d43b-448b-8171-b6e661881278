<?php
/**
 * Email Configuration Wizard
 * Helps configure IMAP/POP3 settings for email fetching
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';

// Include security middleware for web access
require_once 'includes/security_middleware.php';
requireRole('admin');

$message = '';
$messageType = '';
$testResults = [];

// Handle configuration test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_config'])) {
    $protocol = $_POST['protocol'];
    $host = $_POST['host'];
    $port = intval($_POST['port']);
    $encryption = $_POST['encryption'];
    $email = $_POST['email'];
    $password = $_POST['password'];
    
    $testResults = testEmailConnection($protocol, $host, $port, $encryption, $email, $password);
}

// Handle configuration save
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_config'])) {
    $config = [
        'protocol' => $_POST['protocol'],
        'host' => $_POST['host'],
        'port' => intval($_POST['port']),
        'encryption' => $_POST['encryption'],
        'accounts' => [
            [
                'email' => $_POST['info_email'],
                'password' => $_POST['info_password'],
                'category' => 'contact'
            ],
            [
                'email' => $_POST['booking_email'],
                'password' => $_POST['booking_password'],
                'category' => 'quote'
            ]
        ]
    ];
    
    if (saveEmailConfig($config)) {
        $message = 'Email configuration saved successfully!';
        $messageType = 'success';
    } else {
        $message = 'Failed to save email configuration.';
        $messageType = 'error';
    }
}

function testEmailConnection($protocol, $host, $port, $encryption, $email, $password) {
    $results = [
        'success' => false,
        'message' => '',
        'details' => []
    ];
    
    // Check if required extension is available
    if ($protocol === 'imap' && !extension_loaded('imap')) {
        $results['message'] = 'IMAP extension is not installed';
        $results['details'][] = 'Install PHP IMAP extension to use IMAP protocol';
        return $results;
    }
    
    try {
        if ($protocol === 'imap') {
            $hostname = "{{$host}:{$port}/imap/{$encryption}}INBOX";
            $connection = imap_open($hostname, $email, $password);
            
            if ($connection) {
                $mailboxInfo = imap_mailboxmsginfo($connection);
                $results['success'] = true;
                $results['message'] = 'IMAP connection successful!';
                $results['details'][] = "Total messages: " . $mailboxInfo->Nmsgs;
                $results['details'][] = "Unread messages: " . $mailboxInfo->Unread;
                imap_close($connection);
            } else {
                $results['message'] = 'IMAP connection failed: ' . imap_last_error();
            }
        } else {
            // POP3 test would go here
            $results['message'] = 'POP3 testing not implemented yet';
        }
    } catch (Exception $e) {
        $results['message'] = 'Connection error: ' . $e->getMessage();
    }
    
    return $results;
}

function saveEmailConfig($config) {
    $configFile = __DIR__ . '/config/email-config.php';
    $configContent = "<?php\n// Auto-generated email configuration\nreturn " . var_export($config, true) . ";\n";
    
    return file_put_contents($configFile, $configContent) !== false;
}

// Get common hosting provider settings
$hostingProviders = [
    'cpanel' => [
        'name' => 'cPanel/WHM (Most Shared Hosting)',
        'imap_host' => 'mail.yourdomain.com',
        'imap_port' => 993,
        'imap_encryption' => 'ssl',
        'pop3_host' => 'mail.yourdomain.com',
        'pop3_port' => 995,
        'pop3_encryption' => 'ssl'
    ],
    'gmail' => [
        'name' => 'Gmail/Google Workspace',
        'imap_host' => 'imap.gmail.com',
        'imap_port' => 993,
        'imap_encryption' => 'ssl',
        'pop3_host' => 'pop.gmail.com',
        'pop3_port' => 995,
        'pop3_encryption' => 'ssl'
    ],
    'outlook' => [
        'name' => 'Outlook/Hotmail',
        'imap_host' => 'outlook.office365.com',
        'imap_port' => 993,
        'imap_encryption' => 'ssl',
        'pop3_host' => 'outlook.office365.com',
        'pop3_port' => 995,
        'pop3_encryption' => 'ssl'
    ],
    'custom' => [
        'name' => 'Custom/Other Provider',
        'imap_host' => 'melevatours.co.ke',
        'imap_port' => 993,
        'imap_encryption' => 'ssl',
        'pop3_host' => 'melevatours.co.ke',
        'pop3_port' => 995,
        'pop3_encryption' => 'ssl'
    ]
];

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Configuration Wizard - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gray-900">Email Configuration Wizard</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="email-setup-guide.php" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Setup Guide
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Test Results -->
            <?php if (!empty($testResults)): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $testResults['success'] ? 'bg-green-100 border border-green-200' : 'bg-red-100 border border-red-200'; ?>">
                    <h3 class="font-semibold <?php echo $testResults['success'] ? 'text-green-800' : 'text-red-800'; ?> mb-2">
                        <i class="fas fa-<?php echo $testResults['success'] ? 'check-circle' : 'times-circle'; ?> mr-2"></i>
                        Connection Test Results
                    </h3>
                    <p class="<?php echo $testResults['success'] ? 'text-green-700' : 'text-red-700'; ?> mb-2">
                        <?php echo htmlspecialchars($testResults['message']); ?>
                    </p>
                    <?php if (!empty($testResults['details'])): ?>
                        <ul class="<?php echo $testResults['success'] ? 'text-green-600' : 'text-red-600'; ?> text-sm list-disc list-inside">
                            <?php foreach ($testResults['details'] as $detail): ?>
                                <li><?php echo htmlspecialchars($detail); ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Configuration Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Email Server Configuration</h3>
                    <p class="text-sm text-gray-600">Configure your email server settings for automatic email fetching</p>
                </div>
                
                <form method="POST" class="p-6 space-y-6">
                    <?php echo csrf_field(); ?>
                    
                    <!-- Hosting Provider Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Select Your Hosting Provider
                        </label>
                        <select id="hosting-provider" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent" onchange="updateSettings()">
                            <?php foreach ($hostingProviders as $key => $provider): ?>
                                <option value="<?php echo $key; ?>" <?php echo $key === 'custom' ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($provider['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- Protocol Selection -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            Email Protocol
                        </label>
                        <div class="flex space-x-4">
                            <label class="flex items-center">
                                <input type="radio" name="protocol" value="imap" checked class="text-orange-600 focus:ring-orange-500">
                                <span class="ml-2">IMAP (Recommended)</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="protocol" value="pop3" class="text-orange-600 focus:ring-orange-500">
                                <span class="ml-2">POP3</span>
                            </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">IMAP keeps emails on server, POP3 downloads them</p>
                    </div>
                    
                    <!-- Server Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="host" class="block text-sm font-medium text-gray-700 mb-2">
                                Mail Server Host
                            </label>
                            <input type="text" id="host" name="host" value="melevatours.co.ke" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="port" class="block text-sm font-medium text-gray-700 mb-2">
                                Port
                            </label>
                            <input type="number" id="port" name="port" value="993" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="encryption" class="block text-sm font-medium text-gray-700 mb-2">
                                Encryption
                            </label>
                            <select id="encryption" name="encryption" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                <option value="ssl" selected>SSL</option>
                                <option value="tls">TLS</option>
                                <option value="none">None</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Test Connection -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-medium text-blue-900 mb-3">Test Connection</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label for="email" class="block text-sm font-medium text-blue-700 mb-1">
                                    Test Email Address
                                </label>
                                <input type="email" id="email" name="email" value="<EMAIL>"
                                       class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            
                            <div>
                                <label for="password" class="block text-sm font-medium text-blue-700 mb-1">
                                    Password
                                </label>
                                <input type="password" id="password" name="password" value="hi$Ch9=lYcap{7cA"
                                       class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                        
                        <button type="submit" name="test_config" 
                                class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-300">
                            <i class="fas fa-plug mr-2"></i>Test Connection
                        </button>
                    </div>
                    
                    <!-- Account Configuration -->
                    <div class="border-t pt-6">
                        <h4 class="font-medium text-gray-900 mb-4">Email Accounts Configuration</h4>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Info Account -->
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                <h5 class="font-medium text-purple-900 mb-3">
                                    <i class="fas fa-envelope mr-2"></i>Contact Email Account
                                </h5>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-purple-700 mb-1">Email</label>
                                        <input type="email" name="info_email" value="<EMAIL>" required
                                               class="w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-purple-700 mb-1">Password</label>
                                        <input type="password" name="info_password" value="hi$Ch9=lYcap{7cA" required
                                               class="w-full px-3 py-2 border border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Booking Account -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h5 class="font-medium text-blue-900 mb-3">
                                    <i class="fas fa-file-invoice-dollar mr-2"></i>Booking Email Account
                                </h5>
                                <div class="space-y-3">
                                    <div>
                                        <label class="block text-sm font-medium text-blue-700 mb-1">Email</label>
                                        <input type="email" name="booking_email" value="<EMAIL>" required
                                               class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-blue-700 mb-1">Password</label>
                                        <input type="password" name="booking_password" value="hi$Ch9=lYcap{7cA" required
                                               class="w-full px-3 py-2 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Save Configuration -->
                    <div class="flex justify-end space-x-4 pt-6 border-t">
                        <a href="email-setup-guide.php" 
                           class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                            Cancel
                        </a>
                        <button type="submit" name="save_config"
                                class="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition duration-300">
                            <i class="fas fa-save mr-2"></i>Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        const hostingProviders = <?php echo json_encode($hostingProviders); ?>;
        
        function updateSettings() {
            const provider = document.getElementById('hosting-provider').value;
            const settings = hostingProviders[provider];
            
            if (settings) {
                document.getElementById('host').value = settings.imap_host;
                document.getElementById('port').value = settings.imap_port;
                document.getElementById('encryption').value = settings.imap_encryption;
            }
        }
        
        // Update settings when protocol changes
        document.querySelectorAll('input[name="protocol"]').forEach(radio => {
            radio.addEventListener('change', function() {
                const provider = document.getElementById('hosting-provider').value;
                const settings = hostingProviders[provider];
                
                if (settings) {
                    if (this.value === 'imap') {
                        document.getElementById('host').value = settings.imap_host;
                        document.getElementById('port').value = settings.imap_port;
                        document.getElementById('encryption').value = settings.imap_encryption;
                    } else {
                        document.getElementById('host').value = settings.pop3_host;
                        document.getElementById('port').value = settings.pop3_port;
                        document.getElementById('encryption').value = settings.pop3_encryption;
                    }
                }
            });
        });
    </script>
</body>
</html>
