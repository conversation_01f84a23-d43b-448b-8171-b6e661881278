<?php
/**
 * Test Contact Form Message Creation
 * Debug why contact form is failing
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Contact Form Debug Test</h2>";
echo "<p>Testing message creation to debug contact form issues...</p>";

try {
    // Include required files
    require_once 'config/config.php';
    require_once 'classes/models.php';
    
    echo "<p>✅ Files included successfully</p>";
    
    // Test database connection
    $database = Database::getInstance();
    $db = $database->getConnection();
    echo "<p>✅ Database connected successfully</p>";
    
    // Test Message model
    $messageModel = new Message();
    echo "<p>✅ Message model created successfully</p>";
    
    // Check table structure
    echo "<h3>Checking messages table structure:</h3>";
    $stmt = $db->prepare("DESCRIBE messages");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test message creation with the exact same data as contact form
    echo "<h3>Testing message creation:</h3>";
    
    $testMessageData = [
        'sender_name' => 'Test User',
        'sender_email' => '<EMAIL>',
        'subject' => 'Contact Form - Test Message',
        'message_content' => "Name: Test User\nEmail: <EMAIL>\nPhone: +254123456789\n\nMessage:\nThis is a test message from the contact form debug.",
        'message_category' => 'contact'
    ];
    
    echo "<p><strong>Test data:</strong></p>";
    echo "<pre>" . print_r($testMessageData, true) . "</pre>";
    
    // Try to create the message
    echo "<p>Attempting to create message...</p>";
    
    $messageId = $messageModel->create($testMessageData);
    
    if ($messageId) {
        echo "<p style='color: green;'>✅ SUCCESS! Message created with ID: {$messageId}</p>";
        
        // Verify the message was saved correctly
        $stmt = $db->prepare("SELECT * FROM messages WHERE message_id = :id");
        $stmt->bindParam(':id', $messageId);
        $stmt->execute();
        $savedMessage = $stmt->fetch();
        
        if ($savedMessage) {
            echo "<p><strong>Saved message details:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
            foreach ($savedMessage as $key => $value) {
                if (!is_numeric($key)) {
                    echo "<tr><td><strong>{$key}</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
            }
            echo "</table>";
        }
        
        // Clean up test message
        $deleteStmt = $db->prepare("DELETE FROM messages WHERE message_id = :id");
        $deleteStmt->bindParam(':id', $messageId);
        $deleteStmt->execute();
        echo "<p>✅ Test message cleaned up</p>";
        
    } else {
        echo "<p style='color: red;'>❌ FAILED! Message creation returned false</p>";
        
        // Try to get more details about the error
        echo "<p><strong>Debugging the create method:</strong></p>";
        
        // Test with basic data (without message_category)
        $basicMessageData = [
            'sender_name' => 'Test User Basic',
            'sender_email' => '<EMAIL>',
            'subject' => 'Basic Test Message',
            'message_content' => 'This is a basic test message without category.'
        ];
        
        echo "<p>Trying basic message creation (without category)...</p>";
        $basicMessageId = $messageModel->create($basicMessageData);
        
        if ($basicMessageId) {
            echo "<p style='color: green;'>✅ Basic message creation works! ID: {$basicMessageId}</p>";
            echo "<p style='color: orange;'>⚠️ Issue is with the message_category field</p>";
            
            // Clean up
            $deleteStmt = $db->prepare("DELETE FROM messages WHERE message_id = :id");
            $deleteStmt->bindParam(':id', $basicMessageId);
            $deleteStmt->execute();
            
        } else {
            echo "<p style='color: red;'>❌ Even basic message creation fails</p>";
            echo "<p>This indicates a fundamental issue with the Message model or database</p>";
        }
    }
    
    // Test direct SQL insertion
    echo "<h3>Testing direct SQL insertion:</h3>";
    
    try {
        $sql = "INSERT INTO messages (sender_name, sender_email, subject, message_content, message_category) VALUES (:sender_name, :sender_email, :subject, :message_content, :message_category)";
        $stmt = $db->prepare($sql);
        
        $stmt->bindParam(':sender_name', $testMessageData['sender_name']);
        $stmt->bindParam(':sender_email', $testMessageData['sender_email']);
        $stmt->bindParam(':subject', $testMessageData['subject']);
        $stmt->bindParam(':message_content', $testMessageData['message_content']);
        $stmt->bindParam(':message_category', $testMessageData['message_category']);
        
        if ($stmt->execute()) {
            $directInsertId = $db->lastInsertId();
            echo "<p style='color: green;'>✅ Direct SQL insertion works! ID: {$directInsertId}</p>";
            echo "<p style='color: orange;'>⚠️ Issue is with the Message model's create() method</p>";
            
            // Clean up
            $deleteStmt = $db->prepare("DELETE FROM messages WHERE message_id = :id");
            $deleteStmt->bindParam(':id', $directInsertId);
            $deleteStmt->execute();
            
        } else {
            echo "<p style='color: red;'>❌ Direct SQL insertion also fails</p>";
            $errorInfo = $stmt->errorInfo();
            echo "<p><strong>SQL Error:</strong> " . htmlspecialchars($errorInfo[2]) . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ SQL Exception: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<hr>";
    echo "<h3>Summary & Recommendations:</h3>";
    
    if ($messageId) {
        echo "<p style='color: green;'><strong>✅ GOOD NEWS:</strong> Message creation is working!</p>";
        echo "<p>The contact form issue might be elsewhere. Check:</p>";
        echo "<ul>";
        echo "<li>Email service configuration</li>";
        echo "<li>Form validation</li>";
        echo "<li>CSRF token issues</li>";
        echo "</ul>";
    } else {
        echo "<p style='color: red;'><strong>❌ ISSUE FOUND:</strong> Message creation is failing!</p>";
        echo "<p>Recommended fixes:</p>";
        echo "<ul>";
        echo "<li>Check if message_category column exists and has correct type</li>";
        echo "<li>Update Message model's create() method</li>";
        echo "<li>Verify database permissions</li>";
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Fatal Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← Back to Messages</a> | <a href='../contact.php'>Test Contact Form</a></p>";
?>
