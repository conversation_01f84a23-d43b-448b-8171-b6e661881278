<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';
require_once 'classes/booking_models.php';

// Require authentication
Auth::requireLogin();

// Get parameters
$format = $_POST['format'] ?? 'pdf';
$reportType = $_POST['report_type'] ?? 'financial_summary';
$dateRange = $_POST['date_range'] ?? '30';

// Initialize models
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();
$reportData = $reportModel->generateCustomReport($reportType, $dateRange);

// Generate filename
$filename = ucfirst(str_replace('_', ' ', $reportType)) . '_Report_' . date('Y-m-d');

if ($format === 'pdf') {
    // Generate PDF Report
    generatePDFReport($stats, $reportData, $filename);
} elseif ($format === 'excel') {
    // Generate Excel Report
    generateExcelReport($stats, $reportData, $filename);
}

function generatePDFReport($stats, $reportData, $filename) {
    // Create a CSV-like text report that can be easily viewed
    $content = "MELEVA TOURS AND TRAVEL - BUSINESS REPORT\n";
    $content .= "=" . str_repeat("=", 50) . "\n\n";
    $content .= "Report Type: " . ucfirst(str_replace('_', ' ', $reportData['type'])) . "\n";
    $content .= "Generated: " . date('F j, Y g:i A') . "\n";
    $content .= "Period: Last " . $reportData['date_range'] . " days\n\n";

    $content .= "KEY METRICS OVERVIEW\n";
    $content .= "-" . str_repeat("-", 20) . "\n";
    $content .= "Total Quotes: " . ($stats['total_quotes'] ?? 0) . "\n";
    $content .= "Total Bookings: " . ($stats['total_bookings'] ?? 0) . "\n";
    $content .= "Total Revenue: $" . number_format($stats['total_revenue'] ?? 0, 2) . "\n";
    $content .= "Pending Payments: " . ($stats['pending_payments'] ?? 0) . "\n\n";

    $content .= "DETAILED REPORT DATA\n";
    $content .= "-" . str_repeat("-", 20) . "\n";

    foreach ($reportData['data'] as $key => $value) {
        if (is_numeric($value)) {
            $displayValue = is_float($value) ? number_format($value, 2) : $value;
            if (strpos($key, 'amount') !== false || strpos($key, 'revenue') !== false) {
                $displayValue = '$' . $displayValue;
            }
        } else {
            $displayValue = $value;
        }
        $content .= ucfirst(str_replace('_', ' ', $key)) . ": " . $displayValue . "\n";
    }

    $content .= "\n" . str_repeat("=", 60) . "\n";
    $content .= "Report generated by Meleva Tours and Travel Admin Dashboard\n";

    // Set headers for text file download
    header('Content-Type: text/plain; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '.txt"');
    header('Content-Length: ' . strlen($content));

    echo $content;
    exit;
}

function generateExcelReport($stats, $reportData, $filename) {
    // Set headers for Excel download
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
    header('Pragma: no-cache');
    header('Expires: 0');
    
    // Generate Excel content using HTML table format
    echo '<?xml version="1.0"?>
    <Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"
     xmlns:o="urn:schemas-microsoft-com:office:office"
     xmlns:x="urn:schemas-microsoft-com:office:excel"
     xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"
     xmlns:html="http://www.w3.org/TR/REC-html40">
     <Worksheet ss:Name="Report">
      <Table>
       <Row>
        <Cell><Data ss:Type="String">Meleva Tours and Travel - ' . ucfirst(str_replace('_', ' ', $reportData['type'])) . '</Data></Cell>
       </Row>
       <Row>
        <Cell><Data ss:Type="String">Generated: ' . date('F j, Y g:i A') . '</Data></Cell>
       </Row>
       <Row>
        <Cell><Data ss:Type="String">Period: Last ' . $reportData['date_range'] . ' days</Data></Cell>
       </Row>
       <Row></Row>
       <Row>
        <Cell><Data ss:Type="String">Key Metrics</Data></Cell>
        <Cell><Data ss:Type="String">Value</Data></Cell>
       </Row>
       <Row>
        <Cell><Data ss:Type="String">Total Quotes</Data></Cell>
        <Cell><Data ss:Type="Number">' . ($stats['total_quotes'] ?? 0) . '</Data></Cell>
       </Row>
       <Row>
        <Cell><Data ss:Type="String">Total Bookings</Data></Cell>
        <Cell><Data ss:Type="Number">' . ($stats['total_bookings'] ?? 0) . '</Data></Cell>
       </Row>
       <Row>
        <Cell><Data ss:Type="String">Total Revenue</Data></Cell>
        <Cell><Data ss:Type="Number">' . ($stats['total_revenue'] ?? 0) . '</Data></Cell>
       </Row>
       <Row>
        <Cell><Data ss:Type="String">Pending Payments</Data></Cell>
        <Cell><Data ss:Type="Number">' . ($stats['pending_payments'] ?? 0) . '</Data></Cell>
       </Row>
       <Row></Row>
       <Row>
        <Cell><Data ss:Type="String">Detailed Data</Data></Cell>
        <Cell><Data ss:Type="String">Value</Data></Cell>
       </Row>';
    
    foreach ($reportData['data'] as $key => $value) {
        $dataType = is_numeric($value) ? 'Number' : 'String';
        echo '<Row>
                <Cell><Data ss:Type="String">' . ucfirst(str_replace('_', ' ', $key)) . '</Data></Cell>
                <Cell><Data ss:Type="' . $dataType . '">' . $value . '</Data></Cell>
              </Row>';
    }
    
    echo '
      </Table>
     </Worksheet>
    </Workbook>';
}
?>
