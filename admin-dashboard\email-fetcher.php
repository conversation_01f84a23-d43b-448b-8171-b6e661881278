<?php
/**
 * Email <PERSON>tcher - Fetch emails from IMAP and add them to messages system
 * This script connects to your email server and fetches new emails
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';

class EmailFetcher {
    private $messageModel;
    private $config;
    
    public function __construct() {
        $this->messageModel = new Message();
        
        // Email server configuration
        $this->config = [
            'host' => 'mail.melevatours.co.ke',
            'port' => 993,
            'encryption' => 'ssl',
            'accounts' => [
                [
                    'email' => '<EMAIL>',
                    'password' => 'hi$Ch9=lYcap{7cA',
                    'category' => 'contact'
                ],
                [
                    'email' => '<EMAIL>',
                    'password' => '1V^bAvDR!%)6,C&A',
                    'category' => 'quote'
                ]
            ]
        ];
    }
    
    /**
     * Check if IMAP extension is available
     */
    public function checkIMAPSupport() {
        return extension_loaded('imap');
    }

    /**
     * Get configuration
     */
    public function getConfig() {
        return $this->config;
    }
    
    /**
     * Fetch emails from all configured accounts
     */
    public function fetchAllEmails() {
        if (!$this->checkIMAPSupport()) {
            throw new Exception('IMAP extension is not installed. Please install php-imap extension.');
        }
        
        $totalFetched = 0;
        $errors = [];
        
        foreach ($this->config['accounts'] as $account) {
            try {
                $fetched = $this->fetchEmailsFromAccount($account);
                $totalFetched += $fetched;
                echo "Fetched {$fetched} emails from {$account['email']}\n";
            } catch (Exception $e) {
                $error = "Error fetching from {$account['email']}: " . $e->getMessage();
                $errors[] = $error;
                echo $error . "\n";
                error_log($error);
            }
        }
        
        return [
            'total_fetched' => $totalFetched,
            'errors' => $errors
        ];
    }
    
    /**
     * Fetch emails from a specific account
     */
    private function fetchEmailsFromAccount($account) {
        $hostname = "{{$this->config['host']}:{$this->config['port']}/imap/{$this->config['encryption']}}INBOX";
        
        // Connect to email server
        $inbox = imap_open($hostname, $account['email'], $account['password']) 
            or throw new Exception('Cannot connect to email: ' . imap_last_error());
        
        // Get unread emails and recent emails (within last 2 hours)
        $unreadEmails = imap_search($inbox, 'UNSEEN');
        $recentEmails = imap_search($inbox, 'SINCE "' . date('d-M-Y', strtotime('-2 hours')) . '"');

        // Combine and deduplicate email numbers
        $emails = array_unique(array_merge(
            $unreadEmails ? $unreadEmails : [],
            $recentEmails ? $recentEmails : []
        ));

        if (empty($emails)) {
            imap_close($inbox);
            return 0;
        }
        
        $fetchedCount = 0;
        
        foreach ($emails as $emailNumber) {
            try {
                $processed = $this->processEmail($inbox, $emailNumber, $account['category']);
                if ($processed) {
                    $fetchedCount++;
                    // Mark as read after successful processing
                    imap_setflag_full($inbox, $emailNumber, "\\Seen");
                }
            } catch (Exception $e) {
                echo "Error processing email #{$emailNumber}: " . $e->getMessage() . "\n";
                error_log("Email processing error: " . $e->getMessage());
            }
        }
        
        imap_close($inbox);
        return $fetchedCount;
    }
    
    /**
     * Process a single email
     */
    private function processEmail($inbox, $emailNumber, $defaultCategory) {
        // Get email header
        $header = imap_headerinfo($inbox, $emailNumber);
        
        // Get email body
        $body = $this->getEmailBody($inbox, $emailNumber);
        
        // Extract sender information
        $senderEmail = $this->extractSenderEmail($header);
        $senderName = $this->extractSenderName($header);

        // Skip emails from our own domain (outgoing emails)
        if ($this->isFromOurDomain($senderEmail)) {
            echo "Skipping outgoing email from our domain: {$senderEmail}\n";
            return false;
        }

        // Get subject
        $subject = isset($header->subject) ? $this->decodeHeader($header->subject) : 'No Subject';
        
        // Determine if this is a reply
        $isReply = $this->isReplyEmail($subject, $body);
        $parentMessageId = $isReply ? $this->findParentMessage($senderEmail, $subject) : null;
        
        // Determine category
        $category = $this->determineCategory($subject, $body, $defaultCategory);
        
        // Clean the email body
        $cleanBody = $this->cleanEmailBody($body);

        // Check for duplicate emails (same sender, subject, and recent timestamp)
        $receivedAt = date('Y-m-d H:i:s', $header->udate);
        if ($this->isDuplicateEmail($senderEmail, $subject, $receivedAt)) {
            echo "Skipping duplicate email: {$subject} from {$senderEmail}\n";
            return false;
        }

        // Prepare message data using fields that exist in your database
        $messageData = [
            'sender_name' => $senderName,
            'sender_email' => $senderEmail,
            'subject' => $subject,
            'message_content' => $cleanBody,
            'message_type' => 'incoming',
            'message_category' => $category,
            'conversation_id' => null, // Can be used for threading later
            'email_message_id' => null, // Can store email's Message-ID header
            'in_reply_to' => null, // Can store In-Reply-To header
            'email_thread_id' => null, // Can store thread ID
            'email_status' => null, // Can track email status
            'original_message_id' => $parentMessageId, // Link to parent message
            'received_at' => $receivedAt // Use actual email timestamp
        ];
        
        // Save to database
        $messageId = $this->messageModel->create($messageData);

        if ($messageId) {
            echo "Saved email: {$subject} from {$senderEmail} (ID: {$messageId})\n";

            // Try to auto-link to quote
            $linked = $this->messageModel->autoLinkToQuote($messageId);
            if ($linked) {
                echo "Auto-linked message to quote\n";
            }

            return true;
        }

        return false;
    }
    
    /**
     * Get email body (handles multipart emails)
     */
    private function getEmailBody($inbox, $emailNumber) {
        $body = '';
        
        // Try to get HTML body first
        $structure = imap_fetchstructure($inbox, $emailNumber);
        
        if (isset($structure->parts)) {
            // Multipart email
            foreach ($structure->parts as $partNumber => $part) {
                $partBody = imap_fetchbody($inbox, $emailNumber, $partNumber + 1);
                
                // Decode if needed
                if ($part->encoding == 3) { // Base64
                    $partBody = base64_decode($partBody);
                } elseif ($part->encoding == 4) { // Quoted-printable
                    $partBody = quoted_printable_decode($partBody);
                }
                
                // Check if this is text content
                if ($part->subtype == 'PLAIN' || $part->subtype == 'HTML') {
                    $body .= $partBody;
                }
            }
        } else {
            // Simple email
            $body = imap_body($inbox, $emailNumber);
            
            // Decode if needed
            if ($structure->encoding == 3) {
                $body = base64_decode($body);
            } elseif ($structure->encoding == 4) {
                $body = quoted_printable_decode($body);
            }
        }
        
        return $body;
    }
    
    /**
     * Extract sender email from header
     */
    private function extractSenderEmail($header) {
        if (isset($header->from[0])) {
            return $header->from[0]->mailbox . '@' . $header->from[0]->host;
        }
        return '<EMAIL>';
    }
    
    /**
     * Extract sender name from header
     */
    private function extractSenderName($header) {
        if (isset($header->from[0]->personal)) {
            return $this->decodeHeader($header->from[0]->personal);
        }
        return $this->extractSenderEmail($header);
    }
    
    /**
     * Decode email header (handles encoding)
     */
    private function decodeHeader($header) {
        $decoded = imap_mime_header_decode($header);
        $result = '';
        foreach ($decoded as $part) {
            $result .= $part->text;
        }
        return $result;
    }
    
    /**
     * Check if email is a reply
     */
    private function isReplyEmail($subject, $body) {
        $subject = strtolower($subject);
        return (strpos($subject, 're:') === 0 || 
                strpos($subject, 'reply') !== false ||
                strpos($body, 'wrote:') !== false ||
                strpos($body, 'On ') === 0);
    }
    
    /**
     * Find parent message for replies
     */
    private function findParentMessage($senderEmail, $subject) {
        try {
            // Remove "Re:" and similar prefixes
            $cleanSubject = preg_replace('/^(re:|fwd?:|reply:)\s*/i', '', $subject);
            
            $sql = "SELECT message_id FROM messages 
                    WHERE sender_email = :email 
                    AND (subject LIKE :subject OR :clean_subject LIKE CONCAT('%', subject, '%'))
                    ORDER BY received_at DESC 
                    LIMIT 1";
            
            $stmt = $this->messageModel->getDb()->prepare($sql);
            $stmt->bindParam(':email', $senderEmail);
            $subjectParam = "%{$cleanSubject}%";
            $stmt->bindParam(':subject', $subjectParam);
            $stmt->bindParam(':clean_subject', $cleanSubject);
            $stmt->execute();
            
            $result = $stmt->fetch();
            return $result ? $result['message_id'] : null;
        } catch (Exception $e) {
            return null;
        }
    }
    
    /**
     * Determine message category
     */
    private function determineCategory($subject, $body, $defaultCategory) {
        $text = strtolower($subject . ' ' . $body);
        
        if (strpos($text, 'quote') !== false || strpos($text, 'booking') !== false) {
            return 'quote';
        } elseif (strpos($text, 'contact') !== false) {
            return 'contact';
        }
        
        return $defaultCategory;
    }
    
    /**
     * Clean email body (remove signatures, quoted text, etc.)
     */
    private function cleanEmailBody($body) {
        // Remove HTML tags if present
        $body = strip_tags($body);
        
        // Remove common email signatures and quoted text
        $patterns = [
            '/^On .* wrote:.*$/ms',  // Remove "On ... wrote:" and everything after
            '/^From:.*$/ms',         // Remove forwarded email headers
            '/^Sent from my .*$/ms', // Remove mobile signatures
            '/^--.*$/ms',            // Remove signature lines
            '/^\s*>.*$/ms',          // Remove quoted lines starting with >
        ];
        
        foreach ($patterns as $pattern) {
            $body = preg_replace($pattern, '', $body);
        }
        
        // Clean up extra whitespace
        $body = preg_replace('/\n\s*\n\s*\n/', "\n\n", $body);
        
        return trim($body);
    }
    
    /**
     * Check if email is duplicate (same sender, subject, and within 5 minutes)
     */
    private function isDuplicateEmail($senderEmail, $subject, $receivedAt) {
        try {
            $sql = "SELECT COUNT(*) FROM messages
                    WHERE sender_email = :sender_email
                    AND subject = :subject
                    AND ABS(TIMESTAMPDIFF(MINUTE, received_at, :received_at)) <= 5";

            $stmt = $this->messageModel->getDb()->prepare($sql);
            $stmt->execute([
                ':sender_email' => $senderEmail,
                ':subject' => $subject,
                ':received_at' => $receivedAt
            ]);

            return $stmt->fetchColumn() > 0;
        } catch (Exception $e) {
            error_log("Duplicate check error: " . $e->getMessage());
            return false; // If check fails, allow the email to be processed
        }
    }

    /**
     * Check if database has enhanced columns
     */
    private function hasEnhancedColumns() {
        try {
            $stmt = $this->messageModel->getDb()->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
            $stmt->execute();
            return $stmt->fetch() !== false;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Check if email is from our own domain (outgoing emails)
     */
    private function isFromOurDomain($senderEmail) {
        $ourDomains = [
            'melevatours.co.ke',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];

        $senderEmail = strtolower(trim($senderEmail));

        // Check if sender email is exactly one of our email addresses
        if (in_array($senderEmail, $ourDomains)) {
            return true;
        }

        // Check if sender email ends with our domain
        if (str_ends_with($senderEmail, '@melevatours.co.ke')) {
            return true;
        }

        return false;
    }
}

// CLI usage
if (php_sapi_name() === 'cli') {
    echo "Email Fetcher - Starting...\n";
    
    try {
        $fetcher = new EmailFetcher();
        $result = $fetcher->fetchAllEmails();
        
        echo "\n=== SUMMARY ===\n";
        echo "Total emails fetched: {$result['total_fetched']}\n";
        
        if (!empty($result['errors'])) {
            echo "Errors encountered:\n";
            foreach ($result['errors'] as $error) {
                echo "- {$error}\n";
            }
        }
        
        echo "Done!\n";
        
    } catch (Exception $e) {
        echo "Fatal error: " . $e->getMessage() . "\n";
        exit(1);
    }
}

// Web interface
if (php_sapi_name() !== 'cli') {
    // Include security middleware for web access
    require_once 'includes/security_middleware.php';
    requireRole('admin');
    
    echo "<h1>Email Fetcher</h1>";
    
    if (isset($_POST['fetch_emails'])) {
        echo "<h2>Fetching Emails...</h2>";
        echo "<pre>";
        
        try {
            $fetcher = new EmailFetcher();
            $result = $fetcher->fetchAllEmails();
            
            echo "\n=== SUMMARY ===\n";
            echo "Total emails fetched: {$result['total_fetched']}\n";
            
            if (!empty($result['errors'])) {
                echo "Errors encountered:\n";
                foreach ($result['errors'] as $error) {
                    echo "- {$error}\n";
                }
            }
            
        } catch (Exception $e) {
            echo "Error: " . htmlspecialchars($e->getMessage()) . "\n";
        }
        
        echo "</pre>";
        echo "<p><a href='messages.php'>View Messages</a> | <a href='email-fetcher.php'>Fetch More</a></p>";
    } else {
        $fetcher = new EmailFetcher();
        
        echo "<p>This tool will fetch new emails from your email accounts and add them to the messages system.</p>";
        
        if (!$fetcher->checkIMAPSupport()) {
            echo "<div style='background: #fee; border: 1px solid #fcc; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Warning:</strong> IMAP extension is not installed. Please install php-imap extension first.";
            echo "</div>";
        } else {
            echo "<div style='background: #efe; border: 1px solid #cfc; padding: 10px; margin: 10px 0;'>";
            echo "<strong>Ready:</strong> IMAP extension is available.";
            echo "</div>";
        }
        
        echo "<form method='POST'>";
        echo csrf_field();
        echo "<button type='submit' name='fetch_emails' style='background: #f97316; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Fetch New Emails</button>";
        echo "</form>";
        
        echo "<h3>Configured Email Accounts:</h3>";
        echo "<ul>";
        $config = $fetcher->getConfig();
        foreach ($config['accounts'] as $account) {
            echo "<li>{$account['email']} (Category: {$account['category']})</li>";
        }
        echo "</ul>";
    }
}
?>
