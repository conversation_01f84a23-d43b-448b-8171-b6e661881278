<?php
/**
 * Payment System Migration Runner
 */

require_once 'config/config.php';

try {
    $db = Database::getInstance()->getConnection();
    echo "Database connection successful\n";
    
    $migrationFile = __DIR__ . '/migrations/enhance_payment_system_clean.sql';
    echo "Reading migration file: $migrationFile\n";

    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }

    $migration = file_get_contents($migrationFile);
    echo "Migration file size: " . strlen($migration) . " bytes\n";

    $statements = explode(';', $migration);
    echo "Found " . count($statements) . " statements\n";

    $executed = 0;
    $warnings = 0;

    foreach ($statements as $i => $statement) {
        $statement = trim($statement);

        if (!empty($statement) && !preg_match('/^--/', $statement)) {
            echo "Executing statement " . ($i + 1) . ": " . substr($statement, 0, 50) . "...\n";
            try {
                $db->exec($statement);
                $executed++;
                echo "✓ Executed successfully\n";
            } catch (Exception $e) {
                $warnings++;
                echo "⚠ Warning: " . $e->getMessage() . "\n";
                echo "   Statement: " . substr($statement, 0, 100) . "...\n";
            }
        }
    }
    
    echo "\n=== Migration Summary ===\n";
    echo "Executed: $executed statements\n";
    echo "Warnings: $warnings\n";
    echo "Migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
