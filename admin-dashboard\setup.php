<?php
/**
 * Database Setup Script for Meleva Admin Dashboard
 * Run this script to initialize the database and create default data
 */

// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';

try {
    // Connect to MySQL server (without database)
    $dsn = "mysql:host=" . DatabaseConfig::DB_HOST . ";charset=" . DatabaseConfig::DB_CHARSET;
    $pdo = new PDO($dsn, DatabaseConfig::DB_USER, DatabaseConfig::DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Meleva Admin Dashboard - Database Setup</h2>";
    echo "<div style='font-family: Arial, sans-serif; max-width: 800px; margin: 20px auto; padding: 20px;'>";
    
    // Read and execute SQL file
    $sqlFile = 'database_schema.sql';
    if (file_exists($sqlFile)) {
        $sql = file_get_contents($sqlFile);
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $sql)));
        
        echo "<h3>Executing Database Schema...</h3>";
        echo "<ul>";
        
        foreach ($statements as $statement) {
            if (!empty($statement)) {
                try {
                    $pdo->exec($statement);
                    
                    // Extract table name or operation for display
                    if (preg_match('/CREATE\s+DATABASE\s+(\w+)/i', $statement, $matches)) {
                        echo "<li>✅ Created database: " . $matches[1] . "</li>";
                    } elseif (preg_match('/CREATE\s+TABLE\s+(\w+)/i', $statement, $matches)) {
                        echo "<li>✅ Created table: " . $matches[1] . "</li>";
                    } elseif (preg_match('/INSERT\s+INTO\s+(\w+)/i', $statement, $matches)) {
                        echo "<li>✅ Inserted data into: " . $matches[1] . "</li>";
                    } elseif (preg_match('/ALTER\s+TABLE\s+(\w+)/i', $statement, $matches)) {
                        echo "<li>✅ Modified table: " . $matches[1] . "</li>";
                    } else {
                        echo "<li>✅ Executed SQL statement</li>";
                    }
                } catch (PDOException $e) {
                    echo "<li>❌ Error: " . $e->getMessage() . "</li>";
                }
            }
        }
        
        echo "</ul>";
        echo "<h3>✅ Database setup completed successfully!</h3>";
        
        // Display default login credentials
        echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>Default Admin Login Credentials:</h4>";
        echo "<p><strong>Username:</strong> admin</p>";
        echo "<p><strong>Password:</strong> admin123</p>";
        echo "<p><strong>Email:</strong> <EMAIL></p>";
        echo "</div>";
        
        echo "<div style='background: #f3e5f5; border: 1px solid #9c27b0; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>Next Steps:</h4>";
        echo "<ol>";
        echo "<li>Access the admin dashboard at: <a href='login.php'>login.php</a></li>";
        echo "<li>Login with the default credentials above</li>";
        echo "<li>Change the default password immediately</li>";
        echo "<li>Start adding your destinations, packages, and content</li>";
        echo "</ol>";
        echo "</div>";
        
        echo "<div style='background: #fff3e0; border: 1px solid #ff9800; padding: 15px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h4>Important Security Notes:</h4>";
        echo "<ul>";
        echo "<li>Change the default admin password immediately</li>";
        echo "<li>Update database credentials in config/config.php for production</li>";
        echo "<li>Ensure proper file permissions are set</li>";
        echo "<li>Consider enabling HTTPS for production use</li>";
        echo "</ul>";
        echo "</div>";
        
    } else {
        echo "<p style='color: red;'>❌ Error: database_schema.sql file not found!</p>";
    }
    
} catch (PDOException $e) {
    echo "<p style='color: red;'>❌ Database connection error: " . $e->getMessage() . "</p>";
    echo "<p>Please check your database configuration in config/config.php</p>";
}

echo "</div>";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meleva Admin - Database Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f97316, #ea580c);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 30px;
        }
        
        .success { color: #4caf50; }
        .error { color: #f44336; }
        .info { color: #2196f3; }
        
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #f97316, #ea580c);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px 5px;
            transition: opacity 0.3s;
        }
        
        .btn:hover {
            opacity: 0.9;
        }
        
        ul, ol {
            padding-left: 20px;
        }
        
        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏔️ Meleva Tours Admin Dashboard</h1>
            <p>Database Setup & Installation</p>
        </div>
        <div class="content">
            <!-- PHP output will be inserted here -->
            
            <div style="text-align: center; margin-top: 30px;">
                <a href="login.php" class="btn">Go to Admin Login</a>
                <a href="index.php" class="btn">Go to Dashboard</a>
            </div>
        </div>
    </div>
</body>
</html>

