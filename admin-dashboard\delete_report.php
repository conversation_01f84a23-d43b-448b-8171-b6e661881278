<?php
require_once 'config/config.php';
require_once 'classes/models.php';

// Require authentication
Auth::requireLogin();

$reportId = $_POST['report_id'] ?? null;
$action = $_POST['action'] ?? null;

if (!$reportId || $action !== 'delete') {
    header('Location: reports.php?error=Invalid request');
    exit;
}

try {
    // Get report details first
    $reportModel = new Report();
    $report = $reportModel->findById($reportId);
    
    if (!$report) {
        header('Location: reports.php?error=Report not found');
        exit;
    }
    
    // Delete the report
    $success = $reportModel->delete($reportId);
    
    if ($success) {
        header('Location: reports.php?success=Report deleted successfully');
    } else {
        header('Location: reports.php?error=Failed to delete report');
    }
    
} catch (Exception $e) {
    header('Location: reports.php?error=' . urlencode($e->getMessage()));
}

exit;
?>
