<?php
/**
 * Email Debug Tool - Diagnose IMAP connection issues
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';

// Include security middleware for web access
require_once 'includes/security_middleware.php';
requireRole('admin');

// Email configuration (matching your current settings)
$config = [
    'host' => 'mail.melevatours.co.ke',
    'port' => 993,
    'encryption' => 'ssl',
    'accounts' => [
        [
            'email' => '<EMAIL>',
            'password' => '1V^bAvDR!%)6,C&A',
            'category' => 'contact'
        ],
        [
            'email' => '<EMAIL>', 
            'password' => '1V^bAvDR!%)6,C&A',
            'category' => 'quote'
        ]
    ]
];

function runDiagnostics($config) {
    $results = [];
    
    // 1. Check PHP IMAP extension
    $results['imap_extension'] = [
        'test' => 'PHP IMAP Extension',
        'status' => extension_loaded('imap'),
        'message' => extension_loaded('imap') ? 'IMAP extension is loaded' : 'IMAP extension is NOT loaded',
        'solution' => extension_loaded('imap') ? '' : 'Install PHP IMAP extension and restart web server'
    ];
    
    // 2. Check if we can resolve the hostname
    $results['dns_resolution'] = [
        'test' => 'DNS Resolution',
        'status' => false,
        'message' => '',
        'solution' => ''
    ];
    
    $ip = gethostbyname($config['host']);
    if ($ip !== $config['host']) {
        $results['dns_resolution']['status'] = true;
        $results['dns_resolution']['message'] = "Host {$config['host']} resolves to {$ip}";
    } else {
        $results['dns_resolution']['message'] = "Cannot resolve hostname {$config['host']}";
        $results['dns_resolution']['solution'] = 'Check if hostname is correct. Try mail.melevatours.co.ke or melevatours.co.ke';
    }
    
    // 3. Check network connectivity
    $results['network_connectivity'] = [
        'test' => 'Network Connectivity',
        'status' => false,
        'message' => '',
        'solution' => ''
    ];
    
    $connection = @fsockopen($config['host'], $config['port'], $errno, $errstr, 10);
    if ($connection) {
        $results['network_connectivity']['status'] = true;
        $results['network_connectivity']['message'] = "Can connect to {$config['host']}:{$config['port']}";
        fclose($connection);
    } else {
        $results['network_connectivity']['message'] = "Cannot connect to {$config['host']}:{$config['port']} - {$errstr} ({$errno})";
        $results['network_connectivity']['solution'] = 'Check firewall, port number, or try different port (143 for non-SSL)';
    }
    
    // 4. Test IMAP connection for each account
    if (extension_loaded('imap')) {
        foreach ($config['accounts'] as $index => $account) {
            $accountName = $account['email'];
            $results["imap_auth_{$index}"] = [
                'test' => "IMAP Authentication - {$accountName}",
                'status' => false,
                'message' => '',
                'solution' => '',
                'details' => []
            ];
            
            try {
                $hostname = "{{$config['host']}:{$config['port']}/imap/{$config['encryption']}}INBOX";
                
                // Clear any previous IMAP errors
                imap_errors();
                imap_alerts();
                
                $inbox = @imap_open($hostname, $account['email'], $account['password']);
                
                if ($inbox) {
                    $mailboxInfo = imap_mailboxmsginfo($inbox);
                    $results["imap_auth_{$index}"]['status'] = true;
                    $results["imap_auth_{$index}"]['message'] = "Successfully connected to {$accountName}";
                    $results["imap_auth_{$index}"]['details'] = [
                        "Total messages: {$mailboxInfo->Nmsgs}",
                        "Unread messages: {$mailboxInfo->Unread}",
                        "Recent messages: {$mailboxInfo->Recent}",
                        "Mailbox size: " . number_format($mailboxInfo->Size) . " bytes"
                    ];
                    
                    // Test fetching a recent email
                    if ($mailboxInfo->Nmsgs > 0) {
                        $header = imap_headerinfo($inbox, $mailboxInfo->Nmsgs);
                        if ($header) {
                            $results["imap_auth_{$index}"]['details'][] = "Latest email from: " . $header->from[0]->mailbox . "@" . $header->from[0]->host;
                            $results["imap_auth_{$index}"]['details'][] = "Latest email subject: " . (isset($header->subject) ? $header->subject : 'No subject');
                        }
                    }
                    
                    imap_close($inbox);
                } else {
                    $errors = imap_errors();
                    $alerts = imap_alerts();
                    
                    $errorMsg = 'Connection failed';
                    if ($errors) {
                        $errorMsg .= ': ' . implode(', ', $errors);
                    }
                    if ($alerts) {
                        $errorMsg .= ' | Alerts: ' . implode(', ', $alerts);
                    }
                    
                    $results["imap_auth_{$index}"]['message'] = $errorMsg;
                    $results["imap_auth_{$index}"]['solution'] = 'Check email credentials, try different encryption (tls/none), or verify account exists';
                }
                
            } catch (Exception $e) {
                $results["imap_auth_{$index}"]['message'] = "Exception: " . $e->getMessage();
                $results["imap_auth_{$index}"]['solution'] = 'Check server settings and credentials';
            }
        }
    }
    
    // 5. Test alternative configurations
    $results['alternative_configs'] = [
        'test' => 'Alternative Configurations',
        'status' => false,
        'message' => 'Testing alternative server settings...',
        'solution' => '',
        'alternatives' => []
    ];
    
    $alternatives = [
        ['host' => 'melevatours.co.ke', 'port' => 993, 'encryption' => 'ssl'],
        ['host' => 'mail.melevatours.co.ke', 'port' => 143, 'encryption' => 'tls'],
        ['host' => 'mail.melevatours.co.ke', 'port' => 143, 'encryption' => 'none'],
        ['host' => 'imap.melevatours.co.ke', 'port' => 993, 'encryption' => 'ssl'],
    ];
    
    foreach ($alternatives as $alt) {
        $hostname = "{{$alt['host']}:{$alt['port']}/imap/{$alt['encryption']}}INBOX";
        $testResult = @imap_open($hostname, $config['accounts'][0]['email'], $config['accounts'][0]['password']);
        
        if ($testResult) {
            $results['alternative_configs']['alternatives'][] = [
                'config' => $alt,
                'status' => 'SUCCESS',
                'message' => 'This configuration works!'
            ];
            $results['alternative_configs']['status'] = true;
            imap_close($testResult);
        } else {
            $results['alternative_configs']['alternatives'][] = [
                'config' => $alt,
                'status' => 'FAILED',
                'message' => imap_last_error() ?: 'Connection failed'
            ];
        }
    }
    
    return $results;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Debug Tool - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gray-900">Email Debug Tool</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="email-setup-guide.php" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Setup Guide
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- Current Configuration -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Current Configuration</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-2">Server Settings</h3>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li><strong>Host:</strong> <?php echo htmlspecialchars($config['host']); ?></li>
                                <li><strong>Port:</strong> <?php echo $config['port']; ?></li>
                                <li><strong>Encryption:</strong> <?php echo strtoupper($config['encryption']); ?></li>
                            </ul>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-800 mb-2">Email Accounts</h3>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <?php foreach ($config['accounts'] as $account): ?>
                                    <li><strong><?php echo htmlspecialchars($account['email']); ?></strong> (<?php echo $account['category']; ?>)</li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Diagnostic Results -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">Diagnostic Results</h2>
                    <p class="text-sm text-gray-600">Running comprehensive tests to identify issues...</p>
                </div>
                <div class="p-6">
                    <?php 
                    $diagnostics = runDiagnostics($config);
                    $allPassed = true;
                    
                    foreach ($diagnostics as $key => $result): 
                        if (!$result['status']) $allPassed = false;
                    ?>
                        <div class="mb-6 p-4 border rounded-lg <?php echo $result['status'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; ?>">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-<?php echo $result['status'] ? 'check-circle text-green-600' : 'times-circle text-red-600'; ?> text-xl"></i>
                                </div>
                                <div class="ml-3 flex-1">
                                    <h3 class="font-semibold <?php echo $result['status'] ? 'text-green-800' : 'text-red-800'; ?>">
                                        <?php echo htmlspecialchars($result['test']); ?>
                                    </h3>
                                    <p class="<?php echo $result['status'] ? 'text-green-700' : 'text-red-700'; ?> mt-1">
                                        <?php echo htmlspecialchars($result['message']); ?>
                                    </p>
                                    
                                    <?php if (!empty($result['details'])): ?>
                                        <ul class="mt-2 text-sm <?php echo $result['status'] ? 'text-green-600' : 'text-red-600'; ?> list-disc list-inside">
                                            <?php foreach ($result['details'] as $detail): ?>
                                                <li><?php echo htmlspecialchars($detail); ?></li>
                                            <?php endforeach; ?>
                                        </ul>
                                    <?php endif; ?>
                                    
                                    <?php if (!$result['status'] && !empty($result['solution'])): ?>
                                        <div class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                                            <p class="text-yellow-800 text-sm">
                                                <strong>Solution:</strong> <?php echo htmlspecialchars($result['solution']); ?>
                                            </p>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($key === 'alternative_configs' && !empty($result['alternatives'])): ?>
                                        <div class="mt-3">
                                            <h4 class="font-medium text-gray-800 mb-2">Alternative Configurations Tested:</h4>
                                            <div class="space-y-2">
                                                <?php foreach ($result['alternatives'] as $alt): ?>
                                                    <div class="p-2 border rounded <?php echo $alt['status'] === 'SUCCESS' ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'; ?>">
                                                        <div class="flex items-center justify-between">
                                                            <span class="text-sm font-mono">
                                                                <?php echo htmlspecialchars($alt['config']['host']); ?>:<?php echo $alt['config']['port']; ?> (<?php echo strtoupper($alt['config']['encryption']); ?>)
                                                            </span>
                                                            <span class="text-xs px-2 py-1 rounded <?php echo $alt['status'] === 'SUCCESS' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                                                <?php echo $alt['status']; ?>
                                                            </span>
                                                        </div>
                                                        <?php if ($alt['status'] === 'SUCCESS'): ?>
                                                            <p class="text-xs text-green-600 mt-1">✓ Use this configuration in your email-fetcher.php</p>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    
                    <!-- Overall Status -->
                    <div class="mt-8 p-4 border-2 rounded-lg <?php echo $allPassed ? 'border-green-300 bg-green-50' : 'border-red-300 bg-red-50'; ?>">
                        <div class="flex items-center">
                            <i class="fas fa-<?php echo $allPassed ? 'check-circle text-green-600' : 'exclamation-triangle text-red-600'; ?> text-2xl mr-3"></i>
                            <div>
                                <h3 class="font-bold <?php echo $allPassed ? 'text-green-800' : 'text-red-800'; ?>">
                                    <?php echo $allPassed ? 'All Tests Passed!' : 'Issues Found'; ?>
                                </h3>
                                <p class="<?php echo $allPassed ? 'text-green-700' : 'text-red-700'; ?>">
                                    <?php echo $allPassed ? 'Your email configuration should work. Try fetching emails now.' : 'Please fix the issues above before attempting to fetch emails.'; ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Next Steps -->
                    <div class="mt-6 flex flex-wrap gap-4">
                        <a href="email-fetcher.php" 
                           class="bg-orange-500 text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition duration-300 <?php echo !$allPassed ? 'opacity-50 cursor-not-allowed' : ''; ?>">
                            <i class="fas fa-download mr-2"></i>Try Fetching Emails
                        </a>
                        <a href="email-config-wizard.php" 
                           class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 transition duration-300">
                            <i class="fas fa-cog mr-2"></i>Reconfigure Settings
                        </a>
                        <button onclick="location.reload()" 
                                class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition duration-300">
                            <i class="fas fa-redo mr-2"></i>Run Tests Again
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
