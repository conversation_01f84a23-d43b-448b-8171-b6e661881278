<?php
/**
 * Simple Category Fix
 * Quick fix for message categorization without security middleware
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Message Category Fix</h2>";
echo "<p>Fixing message categorization issues...</p>";

try {
    // Include required files
    require_once 'config/config.php';

    $database = Database::getInstance();
    $db = $database->getConnection();
    echo "<p>✅ Database connected successfully</p>";
    
    // Step 1: Check if message_category column exists
    echo "<h3>Step 1: Checking Database Structure</h3>";
    
    $stmt = $db->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
    $stmt->execute();
    $categoryColumnExists = $stmt->fetch();
    
    if (!$categoryColumnExists) {
        echo "<p>❌ message_category column does not exist</p>";
        echo "<p>Adding message_category column...</p>";
        
        try {
            $db->exec("ALTER TABLE messages ADD COLUMN message_category ENUM('contact', 'quote', 'general') DEFAULT 'general' AFTER message_type");
            echo "<p style='color: green;'>✅ message_category column added successfully</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add message_category column: " . htmlspecialchars($e->getMessage()) . "</p>";
            throw $e;
        }
    } else {
        echo "<p style='color: green;'>✅ message_category column exists</p>";
    }
    
    // Step 2: Check current message categories
    echo "<h3>Step 2: Current Message Distribution</h3>";
    
    $stmt = $db->prepare("SELECT message_category, COUNT(*) as count FROM messages GROUP BY message_category");
    $stmt->execute();
    $categoryCounts = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($categoryCounts as $count) {
        $category = $count['message_category'] ?: 'NULL';
        echo "<li><strong>{$category}:</strong> {$count['count']} messages</li>";
    }
    echo "</ul>";
    
    // Step 3: Update message categories based on subject
    echo "<h3>Step 3: Updating Message Categories</h3>";
    
    // Update contact messages
    $stmt = $db->prepare("UPDATE messages SET message_category = 'contact' WHERE (subject LIKE '%Contact Form%' OR subject LIKE '%contact%' OR subject LIKE '%Contact%' OR subject LIKE '%inquiry%' OR subject LIKE '%Inquiry%') AND (message_category = 'general' OR message_category IS NULL)");
    $stmt->execute();
    $contactUpdated = $stmt->rowCount();
    echo "<p style='color: green;'>✅ Updated {$contactUpdated} messages to 'contact' category</p>";
    
    // Update quote messages
    $stmt = $db->prepare("UPDATE messages SET message_category = 'quote' WHERE (subject LIKE '%Quote Request%' OR subject LIKE '%quote%' OR subject LIKE '%Quote%' OR subject LIKE '%booking%' OR subject LIKE '%Booking%' OR subject LIKE '%travel%' OR subject LIKE '%Travel%' OR subject LIKE '%tour%' OR subject LIKE '%Tour%') AND (message_category = 'general' OR message_category IS NULL)");
    $stmt->execute();
    $quoteUpdated = $stmt->rowCount();
    echo "<p style='color: green;'>✅ Updated {$quoteUpdated} messages to 'quote' category</p>";
    
    // Step 4: Show updated distribution
    echo "<h3>Step 4: Updated Message Distribution</h3>";
    
    $stmt = $db->prepare("SELECT message_category, COUNT(*) as count FROM messages GROUP BY message_category");
    $stmt->execute();
    $newCategoryCounts = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($newCategoryCounts as $count) {
        $category = $count['message_category'] ?: 'NULL';
        echo "<li><strong>{$category}:</strong> {$count['count']} messages</li>";
    }
    echo "</ul>";
    
    // Step 5: Show sample messages for verification
    echo "<h3>Step 5: Sample Messages by Category</h3>";
    
    $categories = ['contact', 'quote', 'general'];
    foreach ($categories as $cat) {
        echo "<h4>" . ucfirst($cat) . " Messages:</h4>";
        
        $stmt = $db->prepare("SELECT sender_name, subject FROM messages WHERE message_category = :category ORDER BY received_at DESC LIMIT 3");
        $stmt->bindParam(':category', $cat);
        $stmt->execute();
        $samples = $stmt->fetchAll();
        
        if ($samples) {
            echo "<ul>";
            foreach ($samples as $sample) {
                echo "<li><strong>From:</strong> " . htmlspecialchars($sample['sender_name']) . " | <strong>Subject:</strong> " . htmlspecialchars($sample['subject']) . "</li>";
            }
            echo "</ul>";
        } else {
            echo "<p><em>No messages in this category</em></p>";
        }
    }
    
    // Step 6: Test the Message model
    echo "<h3>Step 6: Testing Message Model</h3>";
    
    try {
        require_once 'classes/models.php';
        $messageModel = new Message();
        echo "<p style='color: green;'>✅ Message model loaded successfully</p>";
        
        // Test creating a message with category
        $testData = [
            'sender_name' => 'Test User',
            'sender_email' => '<EMAIL>',
            'subject' => 'Test Contact Message',
            'message_content' => 'This is a test message',
            'message_category' => 'contact'
        ];
        
        $messageId = $messageModel->create($testData);
        if ($messageId) {
            echo "<p style='color: green;'>✅ Test message created successfully with ID: {$messageId}</p>";
            
            // Clean up test message
            $deleteStmt = $db->prepare("DELETE FROM messages WHERE message_id = :id");
            $deleteStmt->bindParam(':id', $messageId);
            $deleteStmt->execute();
            echo "<p style='color: green;'>✅ Test message cleaned up</p>";
        } else {
            echo "<p style='color: red;'>❌ Failed to create test message</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Message model error: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
    echo "<hr>";
    echo "<h3 style='color: green;'>🎉 SUCCESS!</h3>";
    echo "<p><strong>Message categorization has been fixed!</strong></p>";
    echo "<ul>";
    echo "<li>✅ Database structure updated</li>";
    echo "<li>✅ {$contactUpdated} contact messages categorized</li>";
    echo "<li>✅ {$quoteUpdated} quote messages categorized</li>";
    echo "<li>✅ Message model working correctly</li>";
    echo "</ul>";
    
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li><a href='messages.php'>Go to Messages Admin Page</a> to see the categories</li>";
    echo "<li>Test the Contact and Quote filters</li>";
    echo "<li>Submit a test contact form to verify new messages get proper categories</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← Back to Messages</a> | <a href='email-setup-guide.php'>Email Setup Guide</a></p>";
