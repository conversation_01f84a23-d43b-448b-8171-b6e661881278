-- Migration: Add location column to destinations table
-- Run this script to add location field to existing destinations table

USE meleva_tours;

-- Add location column to destinations table
ALTER TABLE destinations 
ADD COLUMN location VARCHAR(100) AFTER name;

-- Update existing destinations with default location (optional)
-- You can modify this to set appropriate locations for existing destinations
UPDATE destinations SET location = 'Kenya' WHERE location IS NULL;

-- Verify the change
DESCRIBE destinations;
