<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coming Soon - Meleva Tours & Travel</title>
    <meta name="description" content="Something amazing is coming soon from Meleva Tours & Travel. Stay tuned for exciting new safari adventures and destinations.">
    <meta name="robots" content="noindex, nofollow">

    <!-- Favicon -->
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
    <link rel="icon" type="image/png" href="favicon.png">
    <link rel="apple-touch-icon" href="favicon.png">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body, html {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            scroll-behavior: smooth;
        }

        /* Coming Soon Page Specific Styles */
        .coming-soon-bg {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
            position: relative;
            overflow: hidden;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .coming-soon-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('images/hero-bg.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            opacity: 0.3;
            z-index: 1;
        }

        .coming-soon-content {
            position: relative;
            z-index: 2;
        }

        .safari-animation {
            animation: safari-move 4s ease-in-out infinite;
        }

        @keyframes safari-move {
            0%, 100% { transform: translateX(0) rotate(0deg); }
            25% { transform: translateX(10px) rotate(2deg); }
            50% { transform: translateX(0) rotate(0deg); }
            75% { transform: translateX(-10px) rotate(-2deg); }
        }

        .feature-preview {
            transition: all 0.3s ease;
        }

        .feature-preview:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .text-shadow {
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        @media (max-width: 768px) {
            .coming-soon-bg::before {
                background-attachment: scroll;
            }
        }
    </style>
</head>

<body>
    <!-- Coming Soon Section -->
    <section class="coming-soon-bg">
        <div class="coming-soon-content max-w-4xl mx-auto px-6 text-center">
            <!-- Logo -->
            <div class="mb-8">
                <img src="images/meleva-lg.png" alt="Meleva Tours & Travel Logo" class="w-24 h-24 mx-auto mb-6">
            </div>

            <!-- Main Heading -->
            <div class="mb-12">
                <div class="safari-animation text-white text-6xl md:text-7xl mb-8">
                    <i class="fas fa-binoculars"></i>
                </div>
                <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 text-shadow">
                    Meleva Tours & Travel
                </h1>
                <h2 class="text-3xl md:text-5xl font-bold mb-8 bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                    Coming Soon
                </h2>
                <p class="text-lg md:text-xl lg:text-2xl text-white opacity-90 text-shadow max-w-3xl mx-auto">
                    We're crafting an extraordinary safari experience that will take your breath away.
                    Get ready for the adventure of a lifetime in the heart of Africa!
                </p>
            </div>
        </div>
    </section>

    <script>
        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Add your initialization code here
        });
    </script>
</body>
</html>
