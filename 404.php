<?php
// Set HTTP 404 status code
http_response_code(404);

// Get the requested URL for display
$requestedUrl = $_SERVER['REQUEST_URI'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Meleva Tours & Travel</title>
    <meta name="description" content="The page you're looking for could not be found. Explore our amazing safari tours and destinations instead.">
    <meta name="robots" content="noindex, nofollow">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="style.css">
    
    <?php include 'header.php'; ?>
    
    <style>
        /* 404 Page Specific Styles */
        .error-bg {
            background: linear-gradient(135deg, #f97316 0%, #ea580c 50%, #dc2626 100%);
            position: relative;
            overflow: hidden;
        }
        
        .error-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: url('images/hero-bg.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            opacity: 0.2;
            z-index: 1;
        }
        
        .error-content {
            position: relative;
            z-index: 2;
        }
        
        .error-number {
            font-size: clamp(8rem, 15vw, 12rem);
            font-weight: 900;
            line-height: 0.8;
            background: linear-gradient(135deg, #ffffff, #fef3c7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }
        
        .safari-icon {
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .search-suggestions {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .suggestion-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .suggestion-card:hover {
            transform: translateY(-4px);
            border-color: #f97316;
            box-shadow: 0 10px 30px rgba(249, 115, 22, 0.2);
        }
        
        @media (max-width: 768px) {
            .error-bg::before {
                background-attachment: scroll;
            }
        }
    </style>
</head>

<body class="no-hero-section">
    <!-- 404 Error Section -->
    <section class="error-bg min-h-screen flex items-center justify-center py-20">
        <div class="error-content max-w-6xl mx-auto px-6 text-center">
            <!-- Error Number -->
            <div class="mb-8">
                <h1 class="error-number">404</h1>
                <div class="safari-icon text-white text-6xl mb-4">
                    <i class="fas fa-compass"></i>
                </div>
            </div>
            
            <!-- Error Message -->
            <div class="text-white mb-12">
                <h2 class="text-4xl md:text-5xl font-bold mb-4 text-shadow">
                    Oops! You've Wandered Off the Safari Trail
                </h2>
                <p class="text-xl md:text-2xl mb-6 text-shadow opacity-90">
                    The page you're looking for seems to have migrated to a different location.
                </p>
                <?php if (!empty($requestedUrl)): ?>
                    <p class="text-lg opacity-75 mb-8">
                        <i class="fas fa-link mr-2"></i>
                        Requested: <code class="bg-black bg-opacity-30 px-2 py-1 rounded"><?php echo htmlspecialchars($requestedUrl); ?></code>
                    </p>
                <?php endif; ?>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-16">
                <a href="index.php" 
                   class="bg-white text-orange-600 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-home mr-2"></i>
                    Return Home
                </a>
                <a href="tours.php" 
                   class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-orange-600 transition duration-300 transform hover:scale-105">
                    <i class="fas fa-binoculars mr-2"></i>
                    Explore Tours
                </a>
                <a href="contact.php" 
                   class="bg-transparent border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-orange-600 transition duration-300 transform hover:scale-105">
                    <i class="fas fa-envelope mr-2"></i>
                    Contact Us
                </a>
            </div>
        </div>
    </section>

    <!-- Search Suggestions Section -->
    <section class="search-suggestions py-16">
        <div class="max-w-7xl mx-auto px-6">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-800 mb-4">
                    <i class="fas fa-search text-orange-500 mr-3"></i>
                    Looking for Something Specific?
                </h3>
                <p class="text-lg text-gray-600">
                    Here are some popular destinations and experiences you might be interested in:
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Popular Destinations -->
                <div class="suggestion-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="text-orange-500 text-4xl mb-4">
                        <i class="fas fa-mountain"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800 mb-3">Popular Destinations</h4>
                    <p class="text-gray-600 mb-4">Discover our most sought-after safari destinations and wildlife experiences.</p>
                    <a href="tours.php#destinations" class="text-orange-500 font-semibold hover:text-orange-600 transition duration-300">
                        View Destinations <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>

                <!-- Tour Packages -->
                <div class="suggestion-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="text-orange-500 text-4xl mb-4">
                        <i class="fas fa-route"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800 mb-3">Tour Packages</h4>
                    <p class="text-gray-600 mb-4">Browse our carefully crafted safari packages for every type of adventurer.</p>
                    <a href="tours.php#packages" class="text-orange-500 font-semibold hover:text-orange-600 transition duration-300">
                        View Packages <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>

                <!-- Gallery -->
                <div class="suggestion-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="text-orange-500 text-4xl mb-4">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h4 class="text-xl font-bold text-gray-800 mb-3">Photo Gallery</h4>
                    <p class="text-gray-600 mb-4">Get inspired by stunning photos from our previous safari adventures.</p>
                    <a href="gallery.php" class="text-orange-500 font-semibold hover:text-orange-600 transition duration-300">
                        View Gallery <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <?php include 'footer.php'; ?>

    <script>
        // Add some interactive elements
        document.addEventListener('DOMContentLoaded', function() {
            // Add a subtle parallax effect to the error background
            window.addEventListener('scroll', function() {
                const scrolled = window.pageYOffset;
                const errorBg = document.querySelector('.error-bg::before');
                if (errorBg) {
                    errorBg.style.transform = `translateY(${scrolled * 0.5}px)`;
                }
            });

            // Track 404 errors (you can integrate with analytics)
            if (typeof gtag !== 'undefined') {
                gtag('event', 'page_not_found', {
                    'page_location': window.location.href,
                    'page_title': document.title
                });
            }
        });
    </script>
</body>
</html>
