<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/models.php';

/**
 * Image Model
 */
class Image extends BaseModel {
    protected $table = 'images';
    
    protected function getPrimaryKey() {
        return 'image_id';
    }
    
    public function findByType($type, $typeId) {
        $sql = "SELECT * FROM images WHERE {$type}_id = :type_id ORDER BY created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':type_id', $typeId);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function findAllWithDetails() {
    $query = "
        SELECT 
            i.image_id,
            i.url,
            i.alt_text,
            i.is_display_image,
            i.created_at,
            i.updated_at,
            d.name AS destination_name,
            tp.name AS tour_package_name,
            g.name AS gallery_name
        FROM images i
        LEFT JOIN destinations d ON i.destination_id = d.destination_id
        LEFT JOIN tour_packages tp ON i.tour_package_id = tp.tour_package_id
        LEFT JOIN gallery g ON i.gallery_id = g.gallery_id
        ORDER BY i.created_at DESC
    ";

    $stmt = $this->db->prepare($query); // Assuming $this->db is a PDO instance
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    public function create($data) {
        $sql = "INSERT INTO images (url, alt_text, is_display_image, destination_id, tour_package_id, gallery_id, image_category, uploaded_by_user_id)
                VALUES (:url, :alt_text, :is_display_image, :destination_id, :tour_package_id, :gallery_id, :image_category, :uploaded_by_user_id)";
        $stmt = $this->db->prepare($sql);

        $stmt->bindParam(':url', $data['url']);
        $stmt->bindParam(':alt_text', $data['alt_text']);
        $stmt->bindParam(':is_display_image', $data['is_display_image']);
        $stmt->bindParam(':destination_id', $data['destination_id']);
        $stmt->bindParam(':tour_package_id', $data['tour_package_id']);
        $stmt->bindParam(':gallery_id', $data['gallery_id']);
        $stmt->bindParam(':image_category', $data['image_category']);
        $stmt->bindParam(':uploaded_by_user_id', $data['uploaded_by_user_id']);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    public function update($id, $data) {
        // Build dynamic SQL based on provided data
        $fields = [];
        $params = [':id' => $id];

        if (isset($data['alt_text'])) {
            $fields[] = 'alt_text = :alt_text';
            $params[':alt_text'] = $data['alt_text'];
        }
        if (isset($data['is_display_image'])) {
            $fields[] = 'is_display_image = :is_display_image';
            $params[':is_display_image'] = $data['is_display_image'];
        }
        if (isset($data['image_category'])) {
            $fields[] = 'image_category = :image_category';
            $params[':image_category'] = $data['image_category'];
        }

        if (empty($fields)) {
            return true; // Nothing to update
        }

        $sql = "UPDATE images SET " . implode(', ', $fields) . " WHERE image_id = :id";
        $stmt = $this->db->prepare($sql);

        return $stmt->execute($params);
    }

    /**
     * Find images by destination ID
     */
    public function findByDestination($destinationId) {
        $sql = "SELECT * FROM images WHERE destination_id = :destination_id ORDER BY is_display_image DESC, image_id ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':destination_id', $destinationId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update images by destination ID
     */
    public function updateByDestination($destinationId, $data) {
        $setParts = [];
        $params = [':destination_id' => $destinationId];

        foreach ($data as $key => $value) {
            $paramName = ':' . $key;
            $setParts[] = "$key = $paramName";
            $params[$paramName] = $value;
        }

        $sql = "UPDATE images SET " . implode(', ', $setParts) . " WHERE destination_id = :destination_id";
        $stmt = $this->db->prepare($sql);

        return $stmt->execute($params);
    }

    /**
     * Get image statistics for a destination
     */
    public function getDestinationImageStats($destinationId) {
        $sql = "SELECT
                    COUNT(*) as total_images,
                    SUM(CASE WHEN is_display_image = 1 THEN 1 ELSE 0 END) as display_images,
                    MAX(image_id) as latest_image_id,
                    MIN(image_id) as oldest_image_id
                FROM images
                WHERE destination_id = :destination_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':destination_id', $destinationId, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Bulk delete images by destination
     */
    public function deleteByDestination($destinationId) {
        // First get all image URLs for file cleanup
        $images = $this->findByDestination($destinationId);

        // Delete from database
        $sql = "DELETE FROM images WHERE destination_id = :destination_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':destination_id', $destinationId, PDO::PARAM_INT);
        $success = $stmt->execute();

        if ($success) {
            // Clean up files
            foreach ($images as $image) {
                if (file_exists($image['url'])) {
                    unlink($image['url']);
                }
            }
        }

        return $success;
    }

    /**
     * Find images by tour package ID
     */
    public function findByTourPackage($tourPackageId) {
        $sql = "SELECT * FROM images WHERE tour_package_id = :tour_package_id ORDER BY is_display_image DESC, image_id ASC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tour_package_id', $tourPackageId, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Update images by tour package ID
     */
    public function updateByTourPackage($tourPackageId, $data) {
        $setParts = [];
        $params = [':tour_package_id' => $tourPackageId];

        foreach ($data as $key => $value) {
            $paramName = ':' . $key;
            $setParts[] = "$key = $paramName";
            $params[$paramName] = $value;
        }

        $sql = "UPDATE images SET " . implode(', ', $setParts) . " WHERE tour_package_id = :tour_package_id";
        $stmt = $this->db->prepare($sql);

        return $stmt->execute($params);
    }

    /**
     * Get image statistics for a tour package
     */
    public function getTourPackageImageStats($tourPackageId) {
        $sql = "SELECT
                    COUNT(*) as total_images,
                    SUM(CASE WHEN is_display_image = 1 THEN 1 ELSE 0 END) as display_images,
                    MAX(image_id) as latest_image_id,
                    MIN(image_id) as oldest_image_id
                FROM images
                WHERE tour_package_id = :tour_package_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tour_package_id', $tourPackageId, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Bulk delete images by tour package
     */
    public function deleteByTourPackage($tourPackageId) {
        // First get all image URLs for file cleanup
        $images = $this->findByTourPackage($tourPackageId);

        // Delete from database
        $sql = "DELETE FROM images WHERE tour_package_id = :tour_package_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tour_package_id', $tourPackageId, PDO::PARAM_INT);
        $success = $stmt->execute();

        if ($success) {
            // Clean up files
            foreach ($images as $image) {
                if (file_exists($image['url'])) {
                    unlink($image['url']);
                }
            }
        }

        return $success;
    }

    /**
     * Get last inserted ID
     */
    public function getLastInsertedId() {
        return $this->db->lastInsertId();
    }

    // ========== GALLERY-SPECIFIC METHODS (from root additional_models.php) ==========

    /**
     * Find all images with filters (gallery management)
     */
    public function findAllWithFilters($category = 'all', $searchTerm = '') {
        $sql = "SELECT i.*, u.username as uploaded_by
                FROM {$this->table} i
                LEFT JOIN users u ON i.uploaded_by_user_id = u.user_id
                WHERE 1=1";

        $params = [];

        if ($category !== 'all') {
            $sql .= " AND i.image_category = ?";
            $params[] = $category;
        }

        if (!empty($searchTerm)) {
            $sql .= " AND (i.alt_text LIKE ? OR i.url LIKE ?)";
            $params[] = "%{$searchTerm}%";
            $params[] = "%{$searchTerm}%";
        }

        $sql .= " ORDER BY i.created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Find gallery images with filters (main_gallery only)
     */
    public function findGalleryImages($searchTerm = '', $displayFilter = 'all') {
        $sql = "SELECT i.*, u.username as uploaded_by
                FROM {$this->table} i
                LEFT JOIN users u ON i.uploaded_by_user_id = u.user_id
                WHERE i.image_category = 'main_gallery'";

        $params = [];

        if (!empty($searchTerm)) {
            $sql .= " AND i.alt_text LIKE ?";
            $params[] = "%{$searchTerm}%";
        }

        if ($displayFilter === 'display') {
            $sql .= " AND i.is_display_image = 1";
        } elseif ($displayFilter === 'hidden') {
            $sql .= " AND i.is_display_image = 0";
        }

        $sql .= " ORDER BY i.is_display_image DESC, i.created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get display image count for a category
     */
    public function getDisplayImageCount($category) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE image_category = ? AND is_display_image = 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return !empty($result) ? intval($result['count']) : 0;
    }

    /**
     * Unset all display images for a category
     */
    public function unsetDisplayImages($category) {
        $sql = "UPDATE {$this->table} SET is_display_image = 0 WHERE image_category = ?";
        $stmt = $this->db->prepare($sql);
        return $stmt->execute([$category]);
    }

    /**
     * Get display image for a category
     */
    public function getDisplayImage($category) {
        $sql = "SELECT * FROM {$this->table}
                WHERE image_category = ? AND is_display_image = 1
                LIMIT 1";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return !empty($result) ? $result : null;
    }

    /**
     * Get display images for gallery (main_gallery category, max 20)
     */
    public function getGalleryDisplayImages() {
        $sql = "SELECT * FROM {$this->table}
                WHERE image_category = 'main_gallery' AND is_display_image = 1
                ORDER BY created_at DESC
                LIMIT 20";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Fix URLs that have incorrect path format
     */
    public function fixImageUrls() {
        $sql = "UPDATE {$this->table} SET url = REPLACE(url, '../uploads/', 'uploads/') WHERE url LIKE '../uploads/%'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Update main gallery URLs to new directory structure
     */
    public function updateMainGalleryUrls() {
        $sql = "UPDATE {$this->table} SET url = REPLACE(url, 'uploads/images/main-gallery/', 'admin-dashboard/upload/images/main-gallery/') WHERE image_category = 'main_gallery'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->rowCount();
    }

    /**
     * Get images by category
     */
    public function findByCategory($category) {
        $sql = "SELECT i.*, u.username as uploaded_by
                FROM {$this->table} i
                LEFT JOIN users u ON i.uploaded_by_user_id = u.user_id
                WHERE i.image_category = ?
                ORDER BY i.is_display_image DESC, i.created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$category]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get recent images
     */
    public function getRecentImages($limit = 10) {
        $sql = "SELECT i.*, u.username as uploaded_by
                FROM {$this->table} i
                LEFT JOIN users u ON i.uploaded_by_user_id = u.user_id
                ORDER BY i.created_at DESC
                LIMIT ?";

        $stmt = $this->db->prepare($sql);
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Get image statistics
     */
    public function getImageStats() {
        $sql = "SELECT
                    COUNT(*) as total_images,
                    COUNT(CASE WHEN image_category = 'main_gallery' THEN 1 END) as main_gallery_count,
                    COUNT(CASE WHEN image_category = 'destinations' THEN 1 END) as destinations_count,
                    COUNT(CASE WHEN image_category = 'tour_packages' THEN 1 END) as tour_packages_count,
                    COUNT(CASE WHEN is_display_image = 1 THEN 1 END) as display_images_count
                FROM {$this->table}";

        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return !empty($result) ? $result : [
            'total_images' => 0,
            'main_gallery_count' => 0,
            'destinations_count' => 0,
            'tour_packages_count' => 0,
            'display_images_count' => 0
        ];
    }
}

/**
 * Gallery Model
 */
class Gallery extends BaseModel {
    protected $table = 'gallery';
    
    protected function getPrimaryKey() {
        return 'gallery_id';
    }
    
    public function findAllWithImageCount() {
        $sql = "SELECT g.*, COUNT(i.image_id) as image_count 
                FROM gallery g 
                LEFT JOIN images i ON g.gallery_id = i.gallery_id 
                GROUP BY g.gallery_id 
                ORDER BY g.created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    public function create($data) {
        $sql = "INSERT INTO gallery (name, description) VALUES (:name, :description)";
        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        
        return $stmt->execute();
    }
    
    public function update($id, $data) {
        $sql = "UPDATE gallery SET name = :name, description = :description WHERE gallery_id = :id";
        $stmt = $this->db->prepare($sql);
        
        $stmt->bindParam(':name', $data['name']);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
}

/**
 * Message Model
 */
// class Message extends BaseModel {
//     protected $table = 'messages';
    
//     protected function getPrimaryKey() {
//         return 'message_id';
//     }
    
//     public function findAllUnread() {
//         $sql = "SELECT * FROM messages WHERE is_read = 0 ORDER BY received_at DESC";
//         $stmt = $this->db->prepare($sql);
//         $stmt->execute();
//         return $stmt->fetchAll();
//     }
    
//     public function markAsRead($id) {
//         $sql = "UPDATE messages SET is_read = 1 WHERE message_id = :id";
//         $stmt = $this->db->prepare($sql);
//         $stmt->bindParam(':id', $id);
//         return $stmt->execute();
//     }
    
//     public function reply($id, $replyContent) {
//         $sql = "UPDATE messages SET reply_content = :reply_content, replied_at = NOW() WHERE message_id = :id";
//         $stmt = $this->db->prepare($sql);
//         $stmt->bindParam(':reply_content', $replyContent);
//         $stmt->bindParam(':id', $id);
//         return $stmt->execute();
//     }
    
//     public function getStats() {
//         $sql = "SELECT 
//                     COUNT(*) as total,
//                     SUM(CASE WHEN is_read = 0 THEN 1 ELSE 0 END) as unread,
//                     SUM(CASE WHEN reply_content IS NOT NULL THEN 1 ELSE 0 END) as replied
//                 FROM messages";
//         $stmt = $this->db->prepare($sql);
//         $stmt->execute();
//         return $stmt->fetch();
//     }
// }
