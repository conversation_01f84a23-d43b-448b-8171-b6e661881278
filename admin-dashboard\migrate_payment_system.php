<?php
/**
 * Migration script to update payment system to simplified version
 * This script removes partial payment tracking and simplifies the quote status system
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'classes/models.php';

// Only allow execution from command line or admin users
if (php_sapi_name() !== 'cli') {
    // Include security middleware for web access
    require_once 'includes/security_middleware.php';
    
    // Require admin role
    requireRole('admin');
    
    echo "<h1>Payment System Migration</h1>";
    echo "<p>This script will update the payment system to the simplified version.</p>";
    echo "<p><strong>Warning:</strong> This will remove partial payment tracking. Make sure to backup your database first!</p>";
    
    if (!isset($_POST['confirm_migration'])) {
        echo '<form method="POST">';
        echo '<input type="hidden" name="confirm_migration" value="1">';
        echo csrf_field();
        echo '<button type="submit" onclick="return confirm(\'Are you sure you want to proceed? This cannot be undone!\')" style="background: #dc2626; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">Proceed with Migration</button>';
        echo '</form>';
        exit;
    }
    
    echo "<pre>";
}

try {
    $db = DatabaseConfig::getConnection();
    
    echo "Starting payment system migration...\n";
    
    // 1. Remove partial payment fields from quotes table if they exist
    echo "1. Checking quotes table structure...\n";
    
    $columnsToRemove = ['amount_paid', 'balance_remaining', 'payment_link_token', 'payment_link_expires'];
    
    foreach ($columnsToRemove as $column) {
        // Check if column exists
        $stmt = $db->prepare("SHOW COLUMNS FROM quotes LIKE ?");
        $stmt->execute([$column]);
        
        if ($stmt->fetch()) {
            echo "   Removing column: $column\n";
            $db->exec("ALTER TABLE quotes DROP COLUMN `$column`");
        } else {
            echo "   Column $column does not exist (already removed)\n";
        }
    }
    
    // 2. Update quote statuses to simplified system
    echo "2. Updating quote statuses...\n";
    
    // Convert 'partially_paid' to 'quoted' (admin will manually verify payments)
    $stmt = $db->prepare("UPDATE quotes SET quote_status = 'quoted' WHERE quote_status = 'partially_paid'");
    $stmt->execute();
    $affected = $stmt->rowCount();
    echo "   Updated $affected partially_paid quotes to quoted status\n";
    
    // 3. Update payment types to simplified system
    echo "3. Updating payment types...\n";
    
    // Convert all payment types to 'full' (no more partial/deposit tracking)
    $stmt = $db->prepare("UPDATE payments SET payment_type = 'full' WHERE payment_type IN ('partial', 'deposit', 'balance')");
    $stmt->execute();
    $affected = $stmt->rowCount();
    echo "   Updated $affected payment records to full payment type\n";
    
    // 4. Clean up any orphaned payment records
    echo "4. Cleaning up orphaned records...\n";
    
    // Remove payments without valid quote_id or booking_id
    $stmt = $db->prepare("DELETE FROM payments WHERE quote_id IS NULL AND booking_id IS NULL");
    $stmt->execute();
    $affected = $stmt->rowCount();
    echo "   Removed $affected orphaned payment records\n";
    
    // 5. Update quote statuses based on completed payments
    echo "5. Updating quote statuses based on payments...\n";
    
    // Mark quotes as paid if they have completed payments
    $stmt = $db->prepare("
        UPDATE quotes q 
        SET quote_status = 'paid' 
        WHERE q.quote_id IN (
            SELECT DISTINCT p.quote_id 
            FROM payments p 
            WHERE p.payment_status = 'completed' 
            AND p.quote_id IS NOT NULL
        ) 
        AND q.quote_status != 'paid'
    ");
    $stmt->execute();
    $affected = $stmt->rowCount();
    echo "   Updated $affected quotes to paid status based on completed payments\n";
    
    echo "\nMigration completed successfully!\n";
    echo "\nSummary of changes:\n";
    echo "- Removed partial payment tracking fields from quotes table\n";
    echo "- Simplified quote statuses (no more 'partially_paid')\n";
    echo "- Unified payment types to 'full'\n";
    echo "- Updated quote statuses based on actual payment completions\n";
    echo "\nThe payment system now works as follows:\n";
    echo "1. Customer requests quote\n";
    echo "2. Admin sends quote (with or without payment link)\n";
    echo "3. Customer pays any amount via payment link\n";
    echo "4. Admin manually verifies payment and marks as 'paid'\n";
    echo "5. No partial payment or balance tracking\n";
    
} catch (Exception $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
    echo "Migration failed. Please check the error and try again.\n";
    exit(1);
}

if (php_sapi_name() !== 'cli') {
    echo "</pre>";
    echo "<p><strong>Migration completed!</strong> The payment system has been updated to the simplified version.</p>";
    echo "<p><a href='quotes.php'>Go to Quotes Management</a> | <a href='payments.php'>Go to Payments Management</a></p>";
}
?>
