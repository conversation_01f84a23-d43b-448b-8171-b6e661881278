<?php
require_once 'config/config.php';
require_once 'classes/models.php';

// Require authentication
Auth::requireLogin();

$reportId = $_POST['report_id'] ?? null;

if (!$reportId) {
    die('Report ID is required');
}

// Get report details
$reportModel = new Report();
$report = $reportModel->findById($reportId);

if (!$report) {
    die('Report not found');
}

$reportData = json_decode($report['report_data'], true);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($report['report_name']); ?> - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="max-w-4xl mx-auto p-6">
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800"><?php echo htmlspecialchars($report['report_name']); ?></h1>
                    <p class="text-gray-600">Generated on <?php echo date('F j, Y g:i A', strtotime($report['generated_at'])); ?></p>
                </div>
                <button onclick="window.print()" class="bg-orange-600 hover:bg-orange-700 text-white px-4 py-2 rounded-lg">
                    <i class="fas fa-print mr-2"></i>Print Report
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
                <div class="bg-orange-50 p-4 rounded-lg">
                    <div class="text-sm text-orange-600 font-medium">Report Type</div>
                    <div class="text-lg font-bold text-gray-800"><?php echo ucfirst(str_replace('_', ' ', $reportData['type'] ?? 'Unknown')); ?></div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-sm text-blue-600 font-medium">Date Range</div>
                    <div class="text-lg font-bold text-gray-800"><?php echo $reportData['date_range'] ?? 'N/A'; ?> days</div>
                </div>
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-sm text-green-600 font-medium">Generated At</div>
                    <div class="text-lg font-bold text-gray-800"><?php echo date('M j, Y', strtotime($reportData['generated_at'] ?? $report['generated_at'])); ?></div>
                </div>
                <div class="bg-purple-50 p-4 rounded-lg">
                    <div class="text-sm text-purple-600 font-medium">Status</div>
                    <div class="text-lg font-bold text-gray-800">Complete</div>
                </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
                <h2 class="text-lg font-semibold text-gray-800 mb-4">Report Data</h2>
                <div class="overflow-x-auto">
                    <table class="w-full border-collapse">
                        <thead>
                            <tr class="bg-gray-200">
                                <th class="border border-gray-300 px-4 py-2 text-left">Metric</th>
                                <th class="border border-gray-300 px-4 py-2 text-left">Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (isset($reportData['data']) && is_array($reportData['data'])): ?>
                                <?php foreach ($reportData['data'] as $key => $value): ?>
                                    <tr>
                                        <td class="border border-gray-300 px-4 py-2 font-medium"><?php echo ucfirst(str_replace('_', ' ', $key)); ?></td>
                                        <td class="border border-gray-300 px-4 py-2">
                                            <?php 
                                            if (is_numeric($value)) {
                                                $displayValue = is_float($value) ? number_format($value, 2) : $value;
                                                if (strpos($key, 'amount') !== false || strpos($key, 'revenue') !== false) {
                                                    echo '$' . $displayValue;
                                                } else {
                                                    echo $displayValue;
                                                }
                                            } else {
                                                echo htmlspecialchars($value);
                                            }
                                            ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="2" class="border border-gray-300 px-4 py-2 text-center text-gray-500">No data available</td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="mt-6 text-center">
                <p class="text-sm text-gray-500">Report generated by Meleva Tours and Travel Admin Dashboard</p>
            </div>
        </div>
    </div>

    <style>
        @media print {
            body { background: white; }
            .no-print { display: none; }
        }
    </style>
</body>
</html>
