-- Migration: Enhance Payment System for Partial Payments and Quote Management
-- Date: 2025-07-26
-- Description: Add fields to support partial payments, quote versioning, and enhanced tracking

-- Add fields to quotes table for better payment tracking
ALTER TABLE quotes 
ADD COLUMN quote_version INT DEFAULT 1 AFTER quote_status,
ADD COLUMN total_paid DECIMAL(10, 2) DEFAULT 0.00 AFTER quoted_amount,
ADD COLUMN payment_status ENUM('unpaid', 'partial', 'paid') DEFAULT 'unpaid' AFTER total_paid,
ADD COLUMN last_payment_date TIMESTAMP NULL AFTER payment_status,
ADD COLUMN quote_details TEXT NULL AFTER special_requirements,
ADD INDEX idx_payment_status (payment_status),
ADD INDEX idx_quote_version (quote_version);

-- Update quote_status enum to include 'partially_paid'
ALTER TABLE quotes 
MODIFY COLUMN quote_status ENUM('pending', 'quoted', 'accepted', 'rejected', 'expired', 'paid', 'partially_paid') DEFAULT 'pending';

-- Add fields to payments table for better tracking
ALTER TABLE payments 
ADD COLUMN quote_version INT DEFAULT 1 AFTER quote_id,
ADD COLUMN remaining_balance DECIMAL(10, 2) DEFAULT 0.00 AFTER amount,
ADD COLUMN payment_notes TEXT NULL AFTER pesapal_response,
ADD COLUMN customer_name VARCHAR(100) NULL AFTER payment_notes,
ADD COLUMN customer_email VARCHAR(100) NULL AFTER customer_name,
ADD COLUMN customer_phone VARCHAR(20) NULL AFTER customer_email,
ADD INDEX idx_quote_version (quote_version),
ADD INDEX idx_customer_email (customer_email);

-- Create quote_history table for tracking quote changes
CREATE TABLE quote_history (
    history_id INT AUTO_INCREMENT PRIMARY KEY,
    quote_id INT NOT NULL,
    quote_version INT NOT NULL,
    previous_amount DECIMAL(10, 2) NULL,
    new_amount DECIMAL(10, 2) NOT NULL,
    change_reason TEXT NULL,
    changed_by_user_id INT NULL,
    changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE CASCADE,
    FOREIGN KEY (changed_by_user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    INDEX idx_quote_id (quote_id),
    INDEX idx_quote_version (quote_version),
    INDEX idx_changed_at (changed_at)
);

-- Create payment_links table for tracking payment links sent to customers
CREATE TABLE payment_links (
    link_id INT AUTO_INCREMENT PRIMARY KEY,
    quote_id INT NOT NULL,
    link_token VARCHAR(64) NOT NULL,
    amount_due DECIMAL(10, 2) NOT NULL,
    link_type ENUM('full', 'partial', 'balance') DEFAULT 'full',
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE CASCADE,
    UNIQUE KEY unique_token (link_token),
    INDEX idx_quote_id (quote_id),
    INDEX idx_expires_at (expires_at),
    INDEX idx_used_at (used_at)
);

-- Add email conversation linking to quotes
ALTER TABLE email_conversations 
ADD COLUMN quote_id INT NULL AFTER initial_message_id,
ADD COLUMN conversation_type ENUM('general', 'quote', 'booking', 'support') DEFAULT 'general' AFTER quote_id,
ADD FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE SET NULL,
ADD INDEX idx_quote_id (quote_id),
ADD INDEX idx_conversation_type (conversation_type);

-- Update messages table to better link with quotes
ALTER TABLE messages 
ADD COLUMN quote_id INT NULL AFTER conversation_id,
ADD COLUMN message_category ENUM('contact', 'quote', 'booking', 'payment', 'support') DEFAULT 'contact' AFTER message_type,
ADD FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE SET NULL,
ADD INDEX idx_quote_id (quote_id),
ADD INDEX idx_message_category (message_category);

-- Create payment_notifications table for tracking email notifications
CREATE TABLE payment_notifications (
    notification_id INT AUTO_INCREMENT PRIMARY KEY,
    payment_id INT NOT NULL,
    quote_id INT NOT NULL,
    notification_type ENUM('confirmation', 'partial_payment', 'balance_due', 'payment_received') NOT NULL,
    recipient_email VARCHAR(100) NOT NULL,
    email_subject VARCHAR(200) NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    email_status ENUM('sent', 'delivered', 'failed', 'bounced') DEFAULT 'sent',
    FOREIGN KEY (payment_id) REFERENCES payments(payment_id) ON DELETE CASCADE,
    FOREIGN KEY (quote_id) REFERENCES quotes(quote_id) ON DELETE CASCADE,
    INDEX idx_payment_id (payment_id),
    INDEX idx_quote_id (quote_id),
    INDEX idx_notification_type (notification_type),
    INDEX idx_sent_at (sent_at)
);

-- Update existing quotes to have proper payment status
UPDATE quotes 
SET payment_status = CASE 
    WHEN quote_status = 'paid' THEN 'paid'
    WHEN total_paid > 0 AND total_paid < quoted_amount THEN 'partial'
    ELSE 'unpaid'
END
WHERE quoted_amount IS NOT NULL;

-- Update existing payments to include customer information from quotes
UPDATE payments p
JOIN quotes q ON p.quote_id = q.quote_id
SET 
    p.customer_name = q.customer_name,
    p.customer_email = q.customer_email,
    p.customer_phone = q.customer_phone
WHERE p.quote_id IS NOT NULL AND p.customer_name IS NULL;

-- Calculate and update total_paid for existing quotes
UPDATE quotes q
SET total_paid = (
    SELECT COALESCE(SUM(p.amount), 0)
    FROM payments p
    WHERE p.quote_id = q.quote_id AND p.payment_status = 'completed'
)
WHERE q.quote_id IS NOT NULL;

-- Update payment_status based on calculated totals
UPDATE quotes 
SET payment_status = CASE 
    WHEN total_paid >= quoted_amount AND quoted_amount > 0 THEN 'paid'
    WHEN total_paid > 0 THEN 'partial'
    ELSE 'unpaid'
END
WHERE quoted_amount IS NOT NULL;

-- Update quote_status for partially paid quotes
UPDATE quotes 
SET quote_status = 'partially_paid'
WHERE payment_status = 'partial' AND quote_status = 'quoted';
