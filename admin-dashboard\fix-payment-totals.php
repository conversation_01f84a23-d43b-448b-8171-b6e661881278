<?php
/**
 * Fix Payment Totals Script
 * This script recalculates and updates the total_paid field for all quotes
 * based on their completed payments.
 */

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';

// Check if user is logged in (admin only)
session_start();
if (!isset($_SESSION['user_id'])) {
    die('Access denied. Admin login required.');
}

try {
    $quoteModel = new Quote();
    $paymentModel = new Payment();
    
    echo "<h2>Fixing Payment Totals for Quotes</h2>\n";
    echo "<pre>\n";
    
    // Get all quotes that have payments
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("
        SELECT DISTINCT q.quote_id, q.quote_reference, q.quoted_amount, q.total_paid, q.quote_status
        FROM quotes q
        INNER JOIN payments p ON q.quote_id = p.quote_id
        WHERE p.payment_status = 'completed'
        ORDER BY q.quote_id
    ");
    $stmt->execute();
    $quotesWithPayments = $stmt->fetchAll();
    
    echo "Found " . count($quotesWithPayments) . " quotes with completed payments.\n\n";
    
    $fixed = 0;
    $errors = 0;
    
    foreach ($quotesWithPayments as $quote) {
        echo "Processing Quote {$quote['quote_reference']} (ID: {$quote['quote_id']}):\n";
        echo "  Current total_paid: $" . number_format($quote['total_paid'], 2) . "\n";
        echo "  Quote amount: $" . number_format($quote['quoted_amount'], 2) . "\n";
        echo "  Current status: {$quote['quote_status']}\n";
        
        try {
            // Calculate actual total paid from payments
            $actualTotalPaid = $paymentModel->getTotalPaidForQuote($quote['quote_id']);
            echo "  Calculated total_paid: $" . number_format($actualTotalPaid, 2) . "\n";
            
            if (abs($actualTotalPaid - $quote['total_paid']) > 0.01) {
                echo "  → MISMATCH DETECTED! Updating...\n";
                
                // Update the payment status which will recalculate totals
                $result = $quoteModel->updatePaymentStatus($quote['quote_id']);
                
                if ($result) {
                    echo "  → FIXED! Payment totals updated.\n";
                    $fixed++;
                } else {
                    echo "  → ERROR: Failed to update payment totals.\n";
                    $errors++;
                }
            } else {
                echo "  → OK: Totals match.\n";
            }
            
        } catch (Exception $e) {
            echo "  → ERROR: " . $e->getMessage() . "\n";
            $errors++;
        }
        
        echo "\n";
    }
    
    echo "=== SUMMARY ===\n";
    echo "Quotes processed: " . count($quotesWithPayments) . "\n";
    echo "Quotes fixed: $fixed\n";
    echo "Errors: $errors\n";
    
    if ($fixed > 0) {
        echo "\n✅ Payment totals have been recalculated and updated!\n";
        echo "The quote details should now show correct payment information.\n";
    } else {
        echo "\n✅ All payment totals were already correct.\n";
    }
    
    echo "</pre>\n";
    
} catch (Exception $e) {
    echo "<pre>Error: " . $e->getMessage() . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
h2 { color: #333; }
</style>
