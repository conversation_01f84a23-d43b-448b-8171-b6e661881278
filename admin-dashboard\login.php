<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php'; // Include models.php

// Check if user is already logged in
Auth::startSession();
if (Auth::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$error = '';
$lockoutTime = 0;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate CSRF token
    if (!isset($_POST['csrf_token']) || !Utils::validateCSRFToken($_POST['csrf_token'])) {
        $error = 'Security token validation failed. Please try again.';
    } else {
        $username = Utils::sanitizeInput($_POST['username']);
        $password = $_POST['password'];

        if (!empty($username) && !empty($password)) {
            // Check if account is locked
            if (Auth::isAccountLocked($username)) {
                $lockoutTime = Auth::getLockoutTimeRemaining($username);
                $minutes = ceil($lockoutTime / 60);
                $error = "Account temporarily locked due to too many failed attempts. Try again in {$minutes} minute(s).";
            } else {
                // Rate limiting check
                if (!Utils::checkRateLimit('login_attempt', 10, 300)) {
                    $error = 'Too many login attempts. Please wait before trying again.';
                } else {
                    if (!class_exists('User')) {
                        die('User class not found. Check if models.php is included correctly.');
                    }

                    $userModel = new User();
                    $user = $userModel->authenticate($username, $password);

                    if ($user) {
                        // Record successful login
                        Auth::recordLoginAttempt($username, true);
                        Auth::login($user);

                        // Redirect to intended page or dashboard
                        $redirectUrl = $_SESSION['redirect_after_login'] ?? 'index.php';
                        unset($_SESSION['redirect_after_login']);

                        header('Location: ' . $redirectUrl);
                        exit;
                    } else {
                        // Record failed login attempt
                        Auth::recordLoginAttempt($username, false);
                        $error = 'Invalid username or password';

                        // Check if account is now locked
                        if (Auth::isAccountLocked($username)) {
                            $lockoutTime = Auth::getLockoutTimeRemaining($username);
                            $minutes = ceil($lockoutTime / 60);
                            $error = "Too many failed attempts. Account locked for {$minutes} minute(s).";
                        }
                    }
                }
            }
        } else {
            $error = 'Please fill in all fields';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meleva Admin - Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .safari-pattern {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen safari-pattern">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo and Title -->
            <div class="text-center">
                <div class="mx-auto w-20 h-20 lg:w-40 lg:h-40 flex items-center justify-center mb-4">
                    <img src="images/meleva-lg.png" alt="logo">
                </div>
                <!-- <h2 class="text-3xl font-bold text-gray-900">Meleva Admin</h2> -->
                <p class="mt-2 text-lg text-gray-600">Sign in to your admin dashboard</p>
            </div>
            
            <!-- Login Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <?php if ($error): ?>
                    <div class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            <span><?php echo htmlspecialchars($error); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="" class="space-y-6">
                    <!-- CSRF Token -->
                    <input type="hidden" name="csrf_token" value="<?php echo Utils::generateCSRFToken(); ?>">
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                            Username or Email
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400"></i>
                            </div>
                            <input 
                                type="text" 
                                id="username" 
                                name="username" 
                                required 
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Enter your username or email"
                                value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>"
                            >
                        </div>
                    </div>
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required 
                                class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Enter your password"
                            >
                            <button 
                                type="button" 
                                id="toggle-password"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input 
                                id="remember-me" 
                                name="remember-me" 
                                type="checkbox" 
                                class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                            >
                            <label for="remember-me" class="ml-2 block text-sm text-gray-700">
                                Remember me
                            </label>
                        </div>
                        
                        <div class="text-sm">
                            <a href="forgot-password.php" class="font-medium text-orange-600 hover:text-orange-500">
                                Forgot your password?
                            </a>
                        </div>
                    </div>
                    
                    <div>
                        <button 
                            type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                        >
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-sign-in-alt text-orange-300 group-hover:text-orange-200"></i>
                            </span>
                            Sign in to Dashboard
                        </button>
                    </div>
                </form>
                
                <!-- Default Credentials Info -->
                <!-- <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Default Login Credentials:</h4>
                    <div class="text-sm text-blue-700">
                        <p><strong>Username:</strong> admin</p>
                        <p><strong>Password:</strong> password</p>
                    </div>
                </div> -->
            </div>
            
            <!-- Footer -->
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    © 2025 Meleva Tours & Travel. All rights reserved.
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Toggle password visibility
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
        
        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Please fill in all fields');
            }
        });
    </script>
</body>
</html>

