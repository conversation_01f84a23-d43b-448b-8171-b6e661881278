<?php
/**
 * Session Refresh Endpoint
 * Used by JavaScript to refresh user session and prevent timeout
 */

define('ADMIN_ACCESS', true);

header('Content-Type: application/json');
require_once '../includes/security_middleware.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);

// Validate CSRF token
if (!isset($input['csrf_token']) || !Utils::validateCSRFToken($input['csrf_token'])) {
    http_response_code(403);
    echo json_encode(['error' => 'CSRF token validation failed']);
    exit;
}

// Check if user is still logged in
if (!Auth::isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'Not authenticated']);
    exit;
}

// Update last activity time
$_SESSION['last_activity'] = time();

// Log session refresh
Auth::logSecurityEvent('session_refresh', Auth::getCurrentUser()['username']);

// Return success response
echo json_encode([
    'success' => true,
    'message' => 'Session refreshed',
    'time_remaining' => DatabaseConfig::SESSION_TIMEOUT - (time() - $_SESSION['last_activity']),
    'new_csrf_token' => Utils::generateCSRFToken()
]);
