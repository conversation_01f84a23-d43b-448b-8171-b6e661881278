<?php
// Get payment history for a specific quote
require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/booking_models.php';

// Require authentication using the Auth class
Auth::requireLogin();

$quoteId = $_GET['quote_id'] ?? null;

if (!$quoteId) {
    echo '<div class="text-center text-red-600">Quote ID is required</div>';
    exit;
}

try {
    // Initialize models
    $quoteModel = new Quote();
    $paymentModel = new Payment();
    
    // Get quote details
    $quote = $quoteModel->findById($quoteId);
    
    if (!$quote) {
        echo '<div class="text-center text-red-600">Quote not found</div>';
        exit;
    }
    
    // Get all payments for this quote
    $payments = $paymentModel->getByQuoteId($quoteId);
    
    ?>
    
    <div class="space-y-4">
        <!-- Quote Summary -->
        <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="font-semibold text-gray-800 mb-2">Quote Summary</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <p><strong>Reference:</strong> <?php echo htmlspecialchars($quote['quote_reference']); ?></p>
                    <p><strong>Customer:</strong> <?php echo htmlspecialchars($quote['customer_name']); ?></p>
                    <p><strong>Status:</strong> 
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            <?php
                            switch($quote['quote_status']) {
                                case 'paid': echo 'bg-green-100 text-green-800'; break;
                                case 'partially_paid': echo 'bg-yellow-100 text-yellow-800'; break;
                                default: echo 'bg-gray-100 text-gray-800';
                            }
                            ?>">
                            <?php echo $quote['quote_status'] === 'partially_paid' ? 'Partially Paid' : ucfirst($quote['quote_status']); ?>
                        </span>
                    </p>
                </div>
                <div>
                    <p><strong>Quote Amount:</strong> $<?php echo number_format($quote['quoted_amount'], 2); ?></p>
                    <p><strong>Amount Paid:</strong> $<?php echo number_format($quote['amount_paid'] ?? 0, 2); ?></p>
                    <p><strong>Balance:</strong> 
                        <span class="<?php echo ($quote['balance_remaining'] ?? 0) > 0 ? 'text-orange-600' : 'text-green-600'; ?>">
                            $<?php echo number_format($quote['balance_remaining'] ?? 0, 2); ?>
                        </span>
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Payment History -->
        <div>
            <h4 class="font-semibold text-gray-800 mb-3">Payment History</h4>
            
            <?php if (empty($payments)): ?>
                <div class="text-center py-8 text-gray-500">
                    <i class="fas fa-credit-card text-3xl mb-2"></i>
                    <p>No payments found for this quote</p>
                </div>
            <?php else: ?>
                <div class="space-y-3">
                    <?php foreach ($payments as $payment): ?>
                        <div class="border rounded-lg p-4 <?php echo $payment['payment_status'] === 'completed' ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'; ?>">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <span class="font-medium text-gray-800">
                                            Payment #<?php echo $payment['payment_id']; ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            <?php 
                                            switch($payment['payment_status']) {
                                                case 'completed': echo 'bg-green-100 text-green-800'; break;
                                                case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                                case 'failed': echo 'bg-red-100 text-red-800'; break;
                                                default: echo 'bg-gray-100 text-gray-800';
                                            }
                                            ?>">
                                            <?php echo ucfirst($payment['payment_status']); ?>
                                        </span>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                                            <?php echo ucfirst($payment['payment_type']); ?>
                                        </span>
                                    </div>
                                    
                                    <div class="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                        <div>
                                            <p><strong>Amount:</strong> $<?php echo number_format($payment['amount'], 2); ?></p>
                                            <p><strong>Reference:</strong> <?php echo htmlspecialchars($payment['merchant_reference'] ?? 'N/A'); ?></p>
                                        </div>
                                        <div>
                                            <p><strong>Date:</strong>
                                                <?php
                                                if ($payment['payment_date']) {
                                                    $paymentDate = new DateTime($payment['payment_date'], new DateTimeZone('UTC'));
                                                    $paymentDate->setTimezone(new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                    echo $paymentDate->format('M j, Y g:i A');
                                                } else {
                                                    echo 'Pending';
                                                }
                                                ?>
                                            </p>
                                            <p><strong>Method:</strong> <?php echo htmlspecialchars($payment['payment_method'] ?? 'Online Payment'); ?></p>
                                        </div>
                                    </div>
                                    
                                    <?php if ($payment['pesapal_tracking_id']): ?>
                                        <div class="mt-2 text-xs text-gray-500">
                                            <p><strong>Tracking ID:</strong> <?php echo htmlspecialchars($payment['pesapal_tracking_id']); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="ml-4">
                                    <?php if ($payment['payment_status'] === 'completed'): ?>
                                        <i class="fas fa-check-circle text-green-600 text-xl"></i>
                                    <?php elseif ($payment['payment_status'] === 'pending'): ?>
                                        <i class="fas fa-clock text-yellow-600 text-xl"></i>
                                    <?php elseif ($payment['payment_status'] === 'failed'): ?>
                                        <i class="fas fa-times-circle text-red-600 text-xl"></i>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <!-- Payment Summary -->
                <div class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h5 class="font-semibold text-blue-800 mb-2">Payment Summary</h5>
                    <div class="grid grid-cols-3 gap-4 text-sm">
                        <div>
                            <p class="text-blue-600"><strong>Total Payments:</strong></p>
                            <p class="text-lg font-semibold"><?php echo count($payments); ?></p>
                        </div>
                        <div>
                            <p class="text-blue-600"><strong>Completed:</strong></p>
                            <p class="text-lg font-semibold text-green-600">
                                <?php echo count(array_filter($payments, function($p) { return $p['payment_status'] === 'completed'; })); ?>
                            </p>
                        </div>
                        <div>
                            <p class="text-blue-600"><strong>Pending/Failed:</strong></p>
                            <p class="text-lg font-semibold text-orange-600">
                                <?php echo count(array_filter($payments, function($p) { return $p['payment_status'] !== 'completed'; })); ?>
                            </p>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Actions -->
        <div class="flex justify-end space-x-2 pt-4 border-t">
            <?php if (($quote['balance_remaining'] ?? 0) > 0): ?>
                <a href="../payment.php?quote_ref=<?php echo urlencode($quote['quote_reference']); ?>" 
                   target="_blank"
                   class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                    <i class="fas fa-credit-card mr-1"></i>
                    Accept Additional Payment
                </a>
            <?php endif; ?>
            <button onclick="closePaymentHistoryModal()" 
                    class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition duration-300">
                Close
            </button>
        </div>
    </div>
    
    <?php
    
} catch (Exception $e) {
    error_log("Error fetching payment history: " . $e->getMessage());
    echo '<div class="text-center text-red-600">Error loading payment history: ' . htmlspecialchars($e->getMessage()) . '</div>';
}
?>
