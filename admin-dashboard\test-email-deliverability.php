<?php
/**
 * Test Email Deliverability and Spam Prevention
 * Check if emails are being marked as spam and how to fix it
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Email Deliverability Test</h2>";
echo "<p>Testing email configuration to prevent spam classification...</p>";

try {
    // Define admin access constant
    define('ADMIN_ACCESS', true);
    
    // Include required files
    require_once 'config/config.php';
    require_once 'classes/models.php';
    require_once 'classes/EmailService.php';
    
    echo "<p>✅ Files included successfully</p>";
    
    // Test authentication
    Auth::startSession();
    if (!Auth::isLoggedIn()) {
        echo "<p style='color: red;'>❌ Not logged in</p>";
        exit;
    }
    
    $currentUser = Auth::getCurrentUser();
    echo "<p>✅ Current user: " . htmlspecialchars($currentUser['username']) . "</p>";
    
    // Initialize EmailService
    $emailService = new EmailService();
    echo "<p>✅ EmailService initialized</p>";
    
    echo "<hr>";
    
    // Test SMTP Configuration
    echo "<h3>SMTP Configuration Analysis</h3>";
    
    $emailConfig = [
        'smtp_host' => 'melevatours.co.ke',
        'smtp_port' => 465,
        'smtp_username' => '<EMAIL>',
        'smtp_password' => '[HIDDEN]',
        'encryption' => 'SSL'
    ];
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Setting</th><th>Value</th><th>Status</th></tr>";
    
    foreach ($emailConfig as $key => $value) {
        $status = '✅ Good';
        $recommendation = '';
        
        if ($key === 'smtp_host' && $value === 'melevatours.co.ke') {
            $recommendation = 'Using your domain - good for reputation';
        } elseif ($key === 'smtp_port' && $value === 465) {
            $recommendation = 'SSL encryption - secure';
        } elseif ($key === 'smtp_username' && strpos($value, '@melevatours.co.ke') !== false) {
            $recommendation = 'Domain-based email - good';
        }
        
        echo "<tr>";
        echo "<td><strong>{$key}</strong></td>";
        echo "<td>" . htmlspecialchars($value) . "</td>";
        echo "<td>{$status}<br><small>{$recommendation}</small></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<hr>";
    
    // Analyze Spam Issues
    echo "<h3>🚨 Spam Classification Analysis</h3>";
    
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;'>";
    echo "<h4>⚠️ Your Email Was Marked as Spam Because:</h4>";
    echo "<ol>";
    echo "<li><strong>Outbound Spam Protection:</strong> relay.mailbaby.net classified it as rSPAM</li>";
    echo "<li><strong>Missing Authentication:</strong> Likely missing SPF, DKIM, or DMARC records</li>";
    echo "<li><strong>Content Patterns:</strong> Email content might trigger spam filters</li>";
    echo "<li><strong>Sender Reputation:</strong> New domain or IP reputation issues</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<hr>";
    
    // DNS and Authentication Check
    echo "<h3>DNS Authentication Records</h3>";
    
    echo "<p><strong>Required DNS Records for melevatours.co.ke:</strong></p>";
    
    echo "<div style='background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>1. SPF Record (TXT):</h4>";
    echo "<code>v=spf1 include:melevatours.co.ke ~all</code>";
    echo "<p><small>This tells receiving servers that melevatours.co.ke is authorized to send emails for your domain.</small></p>";
    
    echo "<h4>2. DKIM Record (TXT):</h4>";
    echo "<code>Contact your hosting provider to set up DKIM signing</code>";
    echo "<p><small>DKIM adds a digital signature to verify email authenticity.</small></p>";
    
    echo "<h4>3. DMARC Record (TXT):</h4>";
    echo "<code>v=DMARC1; p=quarantine; rua=mailto:<EMAIL></code>";
    echo "<p><small>DMARC tells receiving servers what to do with emails that fail SPF/DKIM checks.</small></p>";
    echo "</div>";
    
    echo "<hr>";
    
    // Content Analysis
    echo "<h3>Email Content Improvements</h3>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Improvements Already Applied:</h4>";
    echo "<ul>";
    echo "<li>✅ Added professional email headers (X-Mailer, Organization, etc.)</li>";
    echo "<li>✅ Set normal priority and importance levels</li>";
    echo "<li>✅ Added business legitimacy headers</li>";
    echo "<li>✅ Improved email title and meta tags</li>";
    echo "<li>✅ Added List-Unsubscribe header</li>";
    echo "<li>✅ Set proper character encoding (UTF-8)</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<hr>";
    
    // Immediate Actions
    echo "<h3>🎯 Immediate Actions Required</h3>";
    
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
    echo "<h4>🔧 Contact Your Hosting Provider (URGENT):</h4>";
    echo "<ol>";
    echo "<li><strong>Set up SPF record:</strong> Add the SPF TXT record to your DNS</li>";
    echo "<li><strong>Enable DKIM signing:</strong> Ask them to enable DKIM for melevatours.co.ke</li>";
    echo "<li><strong>Configure DMARC:</strong> Add DMARC policy to your DNS</li>";
    echo "<li><strong>Check IP reputation:</strong> Ensure your server IP isn't blacklisted</li>";
    echo "<li><strong>Request reverse DNS:</strong> Set up PTR record for your IP</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<hr>";
    
    // Test Email Headers
    echo "<h3>Test Email Headers</h3>";
    
    echo "<p><strong>Headers that will be added to prevent spam:</strong></p>";
    echo "<ul>";
    echo "<li><strong>X-Mailer:</strong> Meleva Tours Customer Service v1.0</li>";
    echo "<li><strong>X-Priority:</strong> 3 (Normal)</li>";
    echo "<li><strong>Organization:</strong> Meleva Tours and Travel</li>";
    echo "<li><strong>X-Business-Type:</strong> Travel and Tourism</li>";
    echo "<li><strong>X-Content-Type:</strong> Customer Service Reply</li>";
    echo "<li><strong>X-Message-Category:</strong> Transactional</li>";
    echo "<li><strong>List-Unsubscribe:</strong> &lt;mailto:<EMAIL>&gt;</li>";
    echo "</ul>";
    
    echo "<hr>";
    
    // Alternative Solutions
    echo "<h3>🔄 Alternative Solutions</h3>";
    
    echo "<div style='background: #e2e3e5; border: 1px solid #d6d8db; padding: 15px; border-radius: 5px;'>";
    echo "<h4>If DNS Changes Don't Work:</h4>";
    echo "<ol>";
    echo "<li><strong>Use a transactional email service:</strong>";
    echo "<ul>";
    echo "<li>SendGrid (recommended for business)</li>";
    echo "<li>Mailgun</li>";
    echo "<li>Amazon SES</li>";
    echo "<li>Postmark</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Warm up your domain:</strong> Start with low volume, gradually increase</li>";
    echo "<li><strong>Monitor reputation:</strong> Use tools like MXToolbox to check blacklists</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<hr>";
    
    // Summary
    echo "<h3>📋 Summary</h3>";
    
    echo "<p><strong>The spam issue is likely due to missing email authentication records, not your code.</strong></p>";
    
    echo "<div style='background: #cce5ff; border: 1px solid #99d6ff; padding: 15px; border-radius: 5px;'>";
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li>✅ <strong>Code improvements applied</strong> - Better headers and content</li>";
    echo "<li>🔧 <strong>Contact hosting provider</strong> - Set up SPF, DKIM, DMARC</li>";
    echo "<li>📧 <strong>Test email delivery</strong> - Send test emails after DNS changes</li>";
    echo "<li>📊 <strong>Monitor results</strong> - Check if emails still go to spam</li>";
    echo "<li>🔄 <strong>Consider alternatives</strong> - If issues persist, use transactional email service</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← Back to Messages</a> | <a href='../contact.php'>Test Contact Form</a></p>";
?>
