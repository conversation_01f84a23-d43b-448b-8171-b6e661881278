<?php
// Simple test script to check if messages.php will work
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Messages System</h2>";

try {
    // Test basic includes
    echo "1. Testing includes...<br>";
    require_once 'config/config.php';
    echo "   ✓ Config loaded<br>";
    
    require_once 'classes/models.php';
    echo "   ✓ Models loaded<br>";
    
    require_once 'classes/EmailService.php';
    echo "   ✓ EmailService loaded<br>";
    
    // Test database connection
    echo "2. Testing database connection...<br>";
    $db = DatabaseConfig::getConnection();
    echo "   ✓ Database connected<br>";
    
    // Test Message model
    echo "3. Testing Message model...<br>";
    $messageModel = new Message();
    echo "   ✓ Message model created<br>";
    
    // Test basic query
    echo "4. Testing basic message query...<br>";
    $sql = "SELECT COUNT(*) as count FROM messages";
    $stmt = $messageModel->getDb()->prepare($sql);
    $stmt->execute();
    $count = $stmt->fetch();
    echo "   ✓ Found " . $count['count'] . " messages<br>";
    
    // Test message creation
    echo "5. Testing message creation...<br>";
    $testData = [
        'sender_name' => 'Test User',
        'sender_email' => '<EMAIL>',
        'subject' => 'Test Message',
        'message_content' => 'This is a test message'
    ];
    
    $messageId = $messageModel->create($testData);
    if ($messageId) {
        echo "   ✓ Test message created with ID: $messageId<br>";
        
        // Clean up test message
        $deleteStmt = $messageModel->getDb()->prepare("DELETE FROM messages WHERE message_id = :id");
        $deleteStmt->bindParam(':id', $messageId);
        $deleteStmt->execute();
        echo "   ✓ Test message cleaned up<br>";
    } else {
        echo "   ✗ Failed to create test message<br>";
    }
    
    // Test column existence
    echo "6. Checking table structure...<br>";
    $stmt = $db->prepare("DESCRIBE messages");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $hasCategory = false;
    $hasParentId = false;
    $hasIsReply = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'message_category') $hasCategory = true;
        if ($column['Field'] === 'parent_message_id') $hasParentId = true;
        if ($column['Field'] === 'is_reply') $hasIsReply = true;
    }
    
    echo "   message_category: " . ($hasCategory ? "✓ EXISTS" : "✗ MISSING") . "<br>";
    echo "   parent_message_id: " . ($hasParentId ? "✓ EXISTS" : "✗ MISSING") . "<br>";
    echo "   is_reply: " . ($hasIsReply ? "✓ EXISTS" : "✗ MISSING") . "<br>";
    
    if (!$hasCategory || !$hasParentId || !$hasIsReply) {
        echo "<br><strong>Note:</strong> Some enhanced columns are missing. Run <a href='setup-message-categories.php'>setup-message-categories.php</a> to add them.<br>";
    }
    
    echo "<br><strong>✅ All tests passed! Messages page should work now.</strong><br>";
    echo "<br><a href='messages.php'>Go to Messages Page</a><br>";
    
} catch (Exception $e) {
    echo "<br><strong>❌ Error:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "<strong>Stack trace:</strong><br><pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}
?>
