<?php
/**
 * Pesapal API Configuration
 *
 * DEMO CREDENTIALS: Using Pesapal demo credentials for testing
 * Get live credentials from: https://developer.pesapal.com/
 */

// Pesapal API Credentials - KENYAN MERCHANT DEMO
// Official Kenyan Merchant Demo Credentials
define('PESAPAL_CONSUMER_KEY', 'qkio1BGGYAXTu2JOfm7XSXNruoZsrqEW');
define('PESAPAL_CONSUMER_SECRET', 'osGQ364R49cXKeOYSpaOnT++rHs=');

// Environment Configuration
define('PESAPAL_ENVIRONMENT', 'sandbox'); // 'sandbox' for testing, 'live' for production

// Pesapal URLs based on environment
if (PESAPAL_ENVIRONMENT === 'live') {
    // Production URLs
    define('PESAPAL_BASE_URL', 'https://pay.pesapal.com/v3/api/');
    define('PESAPAL_IFRAME_URL', 'https://pay.pesapal.com/v3/api/Transactions/SubmitOrderRequest');
} else {
    // Sandbox URLs
    define('PESAPAL_BASE_URL', 'https://cybqa.pesapal.com/pesapalv3/api/');
    define('PESAPAL_IFRAME_URL', 'https://cybqa.pesapal.com/pesapalv3/api/Transactions/SubmitOrderRequest');
}

// Callback URLs - Smart detection for localhost vs live server
function getPesapalBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';

    // Check if we're on localhost (development) or live server
    if (strpos($host, 'localhost') !== false || strpos($host, '127.0.0.1') !== false) {
        // Localhost - include /meleva path
        return $protocol . '://' . $host . '/meleva';
    } else {
        // Live server - no /meleva path needed
        return $protocol . '://' . $host;
    }
}

$pesapalBaseUrl = getPesapalBaseUrl();

define('PESAPAL_CALLBACK_URL', $pesapalBaseUrl . '/payment-callback.php');
define('PESAPAL_IPN_URL', $pesapalBaseUrl . '/payment-ipn.php');

// Validation function to check if credentials are configured
function isPesapalConfigured() {
    return PESAPAL_CONSUMER_KEY !== 'YOUR_PESAPAL_CONSUMER_KEY' && 
           PESAPAL_CONSUMER_SECRET !== 'YOUR_PESAPAL_CONSUMER_SECRET' &&
           !empty(PESAPAL_CONSUMER_KEY) && 
           !empty(PESAPAL_CONSUMER_SECRET);
}
