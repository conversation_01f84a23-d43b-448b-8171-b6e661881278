-- Add message category field to messages table
-- This will help separate contact form messages from quote-related messages

USE jaslanen_meleva_tours;

-- Add message_category field to messages table
ALTER TABLE messages 
ADD COLUMN message_category ENUM('contact', 'quote', 'general') DEFAULT 'general' AFTER message_type;

-- Update existing messages based on subject patterns
UPDATE messages 
SET message_category = 'contact' 
WHERE subject LIKE '%Contact Form%' 
   OR subject LIKE '%contact%' 
   OR subject LIKE '%Contact%';

UPDATE messages 
SET message_category = 'quote' 
WHERE subject LIKE '%Quote Request%' 
   OR subject LIKE '%quote%' 
   OR subject LIKE '%Quote%'
   OR subject LIKE '%booking%'
   OR subject LIKE '%Booking%';

-- Add index for better performance
ALTER TABLE messages 
ADD INDEX idx_message_category (message_category);

-- Update the messages table to better handle email replies
-- Add fields to track email threading and replies
ALTER TABLE messages 
ADD COLUMN parent_message_id INT NULL AFTER original_message_id,
ADD COLUMN is_reply BO<PERSON>EAN DEFAULT FALSE AFTER parent_message_id,
ADD FOREIGN KEY (parent_message_id) REFERENCES messages(message_id) ON DELETE SET NULL;

-- Add index for parent message tracking
ALTER TABLE messages 
ADD INDEX idx_parent_message_id (parent_message_id);

-- Create a view for better message management
CREATE OR REPLACE VIEW message_threads AS
SELECT 
    m.*,
    CASE 
        WHEN m.parent_message_id IS NULL THEN m.message_id
        ELSE COALESCE(pm.parent_message_id, m.parent_message_id)
    END as thread_root_id,
    (SELECT COUNT(*) FROM messages m2 WHERE m2.parent_message_id = m.message_id OR m2.message_id = m.message_id) as thread_count
FROM messages m
LEFT JOIN messages pm ON m.parent_message_id = pm.message_id
ORDER BY m.received_at DESC;
