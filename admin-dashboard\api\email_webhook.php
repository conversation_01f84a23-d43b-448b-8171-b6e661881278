<?php
/**
 * Email Webhook Handler
 * Handles incoming emails and conversation tracking
 * This endpoint can be used with email services like SendGrid, Mailgun, etc.
 */

// Start output buffering to prevent header issues
ob_start();

require_once '../config/config.php';
require_once '../classes/models.php';

header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

try {
    // Get the raw POST data
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    // Handle different email service formats
    $emailData = parseEmailWebhook($data);
    
    if ($emailData) {
        $messageModel = new Message();
        
        // Check if this is a reply to an existing conversation
        $conversationId = extractConversationId($emailData);
        
        // Prepare message data
        $messageData = [
            'conversation_id' => $conversationId,
            'email_message_id' => $emailData['message_id'],
            'in_reply_to' => $emailData['in_reply_to'] ?? null,
            'email_thread_id' => $emailData['thread_id'] ?? null,
            'message_type' => 'incoming',
            'sender_name' => $emailData['sender_name'],
            'sender_email' => $emailData['sender_email'],
            'subject' => $emailData['subject'],
            'message_content' => $emailData['content']
        ];
        
        // Create the message
        $messageId = $messageModel->create($messageData);
        
        if ($messageId) {
            // Send notification to admin (optional)
            notifyAdminOfNewMessage($messageData);
            
            echo json_encode([
                'success' => true,
                'message_id' => $messageId,
                'conversation_id' => $conversationId
            ]);
        } else {
            throw new Exception('Failed to save message');
        }
    } else {
        throw new Exception('Invalid email data format');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Parse email webhook data from different providers
 */
function parseEmailWebhook($data) {
    // SendGrid format
    if (isset($data['from']) && isset($data['subject'])) {
        return [
            'message_id' => $data['headers']['Message-ID'] ?? generateMessageId(),
            'in_reply_to' => $data['headers']['In-Reply-To'] ?? null,
            'thread_id' => $data['headers']['Thread-ID'] ?? null,
            'sender_email' => extractEmail($data['from']),
            'sender_name' => extractName($data['from']),
            'subject' => $data['subject'],
            'content' => $data['text'] ?? $data['html'] ?? ''
        ];
    }
    
    // Mailgun format
    if (isset($data['sender']) && isset($data['Subject'])) {
        return [
            'message_id' => $data['Message-Id'] ?? generateMessageId(),
            'in_reply_to' => $data['In-Reply-To'] ?? null,
            'thread_id' => $data['Thread-Index'] ?? null,
            'sender_email' => $data['sender'],
            'sender_name' => $data['From'] ?? $data['sender'],
            'subject' => $data['Subject'],
            'content' => $data['body-plain'] ?? $data['body-html'] ?? ''
        ];
    }
    
    // Generic format
    if (isset($data['email']) && isset($data['message'])) {
        return [
            'message_id' => $data['message_id'] ?? generateMessageId(),
            'in_reply_to' => $data['in_reply_to'] ?? null,
            'thread_id' => $data['thread_id'] ?? null,
            'sender_email' => $data['email'],
            'sender_name' => $data['name'] ?? $data['email'],
            'subject' => $data['subject'] ?? 'No Subject',
            'content' => $data['message']
        ];
    }
    
    return null;
}

/**
 * Extract conversation ID from email headers or generate new one
 */
function extractConversationId($emailData) {
    // Check for existing conversation ID in headers
    if (isset($emailData['headers']['X-Conversation-ID'])) {
        return $emailData['headers']['X-Conversation-ID'];
    }
    
    // Check if this is a reply by looking at In-Reply-To
    if (!empty($emailData['in_reply_to'])) {
        $messageModel = new Message();
        $db = $messageModel->getDb();
        
        $sql = "SELECT conversation_id FROM messages WHERE email_message_id = :message_id LIMIT 1";
        $stmt = $db->prepare($sql);
        $stmt->execute([':message_id' => $emailData['in_reply_to']]);
        $result = $stmt->fetch();
        
        if ($result) {
            return $result['conversation_id'];
        }
    }
    
    // Generate new conversation ID
    return 'CONV_' . strtoupper(substr(md5($emailData['sender_email'] . $emailData['subject']), 0, 12));
}

/**
 * Extract email address from "Name <<EMAIL>>" format
 */
function extractEmail($emailString) {
    if (preg_match('/<([^>]+)>/', $emailString, $matches)) {
        return $matches[1];
    }
    return $emailString;
}

/**
 * Extract name from "Name <<EMAIL>>" format
 */
function extractName($emailString) {
    if (preg_match('/^([^<]+)</', $emailString, $matches)) {
        return trim($matches[1]);
    }
    return extractEmail($emailString);
}

/**
 * Generate unique message ID
 */
function generateMessageId() {
    return '<' . uniqid() . '@webhook.melevatours.co.ke>';
}

/**
 * Notify admin of new message (optional)
 */
function notifyAdminOfNewMessage($messageData) {
    // You can implement admin notification here
    // For example, send email to admin or create notification
}
