-- Email Conversation Tracking Migration
-- Run this to add email tracking capabilities to the messages system

-- Add email tracking fields to messages table
ALTER TABLE messages 
ADD COLUMN conversation_id VARCHAR(50) NULL AFTER message_id,
ADD COLUMN email_message_id VARCHAR(255) NULL AFTER conversation_id,
ADD COLUMN in_reply_to VARC<PERSON><PERSON>(255) NULL AFTER email_message_id,
ADD COLUMN email_thread_id VARCHAR(255) NULL AFTER in_reply_to,
ADD COLUMN message_type ENUM('incoming', 'outgoing') DEFAULT 'incoming' AFTER email_thread_id,
ADD COLUMN email_status ENUM('sent', 'delivered', 'read', 'failed') NULL AFTER message_type,
ADD COLUMN original_message_id INT NULL AFTER email_status,
ADD INDEX idx_conversation_id (conversation_id),
ADD INDEX idx_email_message_id (email_message_id),
ADD INDEX idx_email_thread_id (email_thread_id),
ADD FOREIGN KEY (original_message_id) REFERENCES messages(message_id) ON DELETE SET NULL;

-- Create email_conversations table for better conversation management
CREATE TABLE email_conversations (
    conversation_id VARCHAR(50) PRIMARY KEY,
    initial_message_id INT NOT NULL,
    sender_email VARCHAR(100) NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    subject VARCHAR(200) NOT NULL,
    status ENUM('open', 'closed', 'pending') DEFAULT 'open',
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (initial_message_id) REFERENCES messages(message_id) ON DELETE CASCADE,
    INDEX idx_sender_email (sender_email),
    INDEX idx_status (status),
    INDEX idx_last_activity (last_activity)
);

-- Create email_tracking table for detailed email delivery tracking
CREATE TABLE email_tracking (
    tracking_id INT AUTO_INCREMENT PRIMARY KEY,
    message_id INT NOT NULL,
    email_message_id VARCHAR(255) NOT NULL,
    email_provider VARCHAR(50) NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    delivered_at TIMESTAMP NULL,
    opened_at TIMESTAMP NULL,
    clicked_at TIMESTAMP NULL,
    bounced_at TIMESTAMP NULL,
    status ENUM('queued', 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'failed') DEFAULT 'queued',
    error_message TEXT NULL,
    FOREIGN KEY (message_id) REFERENCES messages(message_id) ON DELETE CASCADE,
    INDEX idx_email_message_id (email_message_id),
    INDEX idx_status (status)
);

-- Update existing messages to have conversation IDs
UPDATE messages 
SET conversation_id = CONCAT('CONV_', LPAD(message_id, 8, '0'))
WHERE conversation_id IS NULL;

-- Insert existing messages into conversations table
INSERT INTO email_conversations (conversation_id, initial_message_id, sender_email, sender_name, subject, status, last_activity, created_at)
SELECT 
    conversation_id,
    message_id,
    sender_email,
    sender_name,
    subject,
    CASE 
        WHEN reply_content IS NOT NULL THEN 'closed'
        WHEN is_read = 1 THEN 'pending'
        ELSE 'open'
    END as status,
    COALESCE(replied_at, received_at) as last_activity,
    received_at as created_at
FROM messages 
WHERE conversation_id IS NOT NULL
ON DUPLICATE KEY UPDATE
    last_activity = VALUES(last_activity),
    status = VALUES(status);
