<?php
/**
 * Email Reply Handler
 * This script processes incoming email replies and adds them to the messages system
 * 
 * This can be called via:
 * 1. Email forwarding rules (pipe emails to this script)
 * 2. IMAP polling (check for new emails periodically)
 * 3. Webhook from email service provider
 */

require_once 'config/config.php';
require_once 'classes/models.php';

class EmailReplyHandler {
    private $messageModel;
    
    public function __construct() {
        $this->messageModel = new Message();
    }
    
    /**
     * Process an incoming email reply
     */
    public function processEmailReply($emailData) {
        try {
            // Extract email information
            $senderEmail = $this->extractEmail($emailData['from']);
            $senderName = $this->extractName($emailData['from']);
            $subject = $emailData['subject'];
            $content = $this->cleanEmailContent($emailData['body']);
            
            // Determine if this is a reply to an existing conversation
            $parentMessageId = $this->findParentMessage($senderEmail, $subject);
            $messageCategory = $this->determineCategory($subject, $content);
            
            // Create conversation ID if it's a new conversation
            $conversationId = $parentMessageId ? 
                $this->getConversationId($parentMessageId) : 
                $this->generateConversationId();
            
            // Save the reply to database
            $messageData = [
                'sender_name' => $senderName,
                'sender_email' => $senderEmail,
                'subject' => $subject,
                'message_content' => $content,
                'message_category' => $messageCategory,
                'conversation_id' => $conversationId,
                'parent_message_id' => $parentMessageId,
                'is_reply' => $parentMessageId ? true : false,
                'message_type' => 'incoming'
            ];
            
            $messageId = $this->messageModel->create($messageData);
            
            if ($messageId) {
                error_log("Email reply processed successfully: Message ID {$messageId}");
                return $messageId;
            } else {
                error_log("Failed to save email reply to database");
                return false;
            }
            
        } catch (Exception $e) {
            error_log("Error processing email reply: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Find parent message based on sender email and subject
     */
    private function findParentMessage($senderEmail, $subject) {
        // Look for recent messages from the same sender
        $sql = "SELECT message_id, conversation_id FROM messages 
                WHERE sender_email = :email 
                AND (subject LIKE :subject OR :subject LIKE CONCAT('%', subject, '%'))
                ORDER BY received_at DESC 
                LIMIT 1";
        
        $stmt = $this->messageModel->getDb()->prepare($sql);
        $stmt->bindParam(':email', $senderEmail);
        $stmt->bindParam(':subject', $subject);
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result ? $result['message_id'] : null;
    }
    
    /**
     * Get conversation ID for a message
     */
    private function getConversationId($messageId) {
        $sql = "SELECT conversation_id FROM messages WHERE message_id = :id";
        $stmt = $this->messageModel->getDb()->prepare($sql);
        $stmt->bindParam(':id', $messageId);
        $stmt->execute();
        
        $result = $stmt->fetch();
        return $result ? $result['conversation_id'] : $this->generateConversationId();
    }
    
    /**
     * Generate a unique conversation ID
     */
    private function generateConversationId() {
        return 'CONV_' . date('Ymd') . '_' . uniqid();
    }
    
    /**
     * Determine message category based on subject and content
     */
    private function determineCategory($subject, $content) {
        $text = strtolower($subject . ' ' . $content);
        
        if (strpos($text, 'quote') !== false || strpos($text, 'booking') !== false) {
            return 'quote';
        } elseif (strpos($text, 'contact') !== false) {
            return 'contact';
        }
        
        return 'general';
    }
    
    /**
     * Extract email address from "Name <<EMAIL>>" format
     */
    private function extractEmail($fromField) {
        if (preg_match('/<([^>]+)>/', $fromField, $matches)) {
            return $matches[1];
        }
        return $fromField;
    }
    
    /**
     * Extract name from "Name <<EMAIL>>" format
     */
    private function extractName($fromField) {
        if (preg_match('/^([^<]+)</', $fromField, $matches)) {
            return trim($matches[1]);
        }
        // If no name found, use email
        return $this->extractEmail($fromField);
    }
    
    /**
     * Clean email content (remove signatures, quoted text, etc.)
     */
    private function cleanEmailContent($content) {
        // Remove common email signatures and quoted text
        $patterns = [
            '/^On .* wrote:.*$/ms',  // Remove "On ... wrote:" and everything after
            '/^From:.*$/ms',         // Remove forwarded email headers
            '/^Sent from my .*$/ms', // Remove mobile signatures
            '/^--.*$/ms',            // Remove signature lines
        ];
        
        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }
        
        return trim($content);
    }
}

// Example usage for webhook or direct processing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email_data'])) {
    $handler = new EmailReplyHandler();
    $result = $handler->processEmailReply($_POST['email_data']);
    
    header('Content-Type: application/json');
    echo json_encode(['success' => $result !== false, 'message_id' => $result]);
    exit;
}

// Example usage for IMAP processing (commented out - requires IMAP extension)
/*
function processIMAPReplies() {
    $handler = new EmailReplyHandler();
    
    // IMAP configuration
    $hostname = '{melevatours.co.ke:993/imap/ssl}INBOX';
    $username = '<EMAIL>';
    $password = 'your_password';
    
    $inbox = imap_open($hostname, $username, $password) or die('Cannot connect to email: ' . imap_last_error());
    
    // Get unread emails
    $emails = imap_search($inbox, 'UNSEEN');
    
    if ($emails) {
        foreach ($emails as $emailNumber) {
            $header = imap_headerinfo($inbox, $emailNumber);
            $body = imap_body($inbox, $emailNumber);
            
            $emailData = [
                'from' => $header->from[0]->mailbox . '@' . $header->from[0]->host,
                'subject' => $header->subject,
                'body' => $body
            ];
            
            $handler->processEmailReply($emailData);
            
            // Mark as read
            imap_setflag_full($inbox, $emailNumber, "\\Seen");
        }
    }
    
    imap_close($inbox);
}
*/

// CLI usage example
if (php_sapi_name() === 'cli' && isset($argv[1]) && $argv[1] === 'process-imap') {
    echo "IMAP processing would run here (requires IMAP extension and configuration)\n";
    // processIMAPReplies();
}
?>
