<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database configuration and models with error handling
try {
    require_once 'admin-dashboard/config/config.php';
    require_once 'admin-dashboard/config/pesapal_config.php';
    require_once 'admin-dashboard/classes/models.php';
    require_once 'admin-dashboard/classes/booking_models.php';
    require_once 'admin-dashboard/classes/PesapalService.php';
    require_once 'admin-dashboard/classes/EmailService.php';
} catch (Exception $e) {
    die("Error loading required files: " . $e->getMessage());
}

// Initialize models and services with error handling
try {
    $quoteModel = new Quote();
    $paymentModel = new Payment();
    $pesapalService = new PesapalService();
    $emailService = new EmailService();
} catch (Exception $e) {
    die("Error initializing services: " . $e->getMessage());
}

// Get quote reference from URL
$quoteReference = $_GET['quote_ref'] ?? '';
$quote = null;
$message = '';
$messageType = '';
$pesapalUrl = '';
$totalPaid = 0;
$remainingBalance = 0;

if (!$quoteReference) {
    $message = 'Quote reference is required.';
    $messageType = 'error';
} else {
    // Get quote details
    $quote = $quoteModel->findByReference($quoteReference);
    
    if (!$quote) {
        $message = 'Quote not found. Please check your quote reference.';
        $messageType = 'error';
    } elseif ($quote['quote_status'] !== 'quoted') {
        $message = 'This quote is not ready for payment. Please contact us for assistance.';
        $messageType = 'error';
    } else {
        // Check if already paid
        $existingPayments = $paymentModel->getByQuoteId($quote['quote_id']);
        $hasPaidPayment = false;
        foreach ($existingPayments as $payment) {
            if ($payment['payment_status'] === 'completed') {
                $hasPaidPayment = true;
                break;
            }
        }

        if ($hasPaidPayment) {
            $message = 'This quote has already been paid. Thank you!';
            $messageType = 'success';
        } else {
            // Check if payment is being processed
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['proceed_payment'])) {
                try {
                    // Get and validate payment amount
                    $paymentAmount = floatval($_POST['payment_amount'] ?? 0);

                    if ($paymentAmount <= 0) {
                        throw new Exception('Please enter a valid payment amount.');
                    }

                    // No upper limit restriction - admin will verify the amount

                // Set longer execution time for payment processing
                set_time_limit(90);

                // Check if Pesapal is configured
                if (!isPesapalConfigured()) {
                    throw new Exception('Payment gateway is not configured. Please contact support.');
                }

                // Get Pesapal auth token
                $authToken = $pesapalService->getAuthToken();
                if (!$authToken) {
                    throw new Exception('Failed to authenticate with payment gateway. Please try again later.');
                }

                // Register IPN if not already done
                $ipnResponse = $pesapalService->registerIPN($authToken, PESAPAL_IPN_URL);

                $ipnId = null;
                if ($ipnResponse && isset($ipnResponse['ipn_id'])) {
                    $ipnId = $ipnResponse['ipn_id'];
                } elseif ($ipnResponse && isset($ipnResponse['id'])) {
                    $ipnId = $ipnResponse['id'];
                }

                if (!$ipnId) {
                    throw new Exception('Failed to register payment notification. Please contact support.');
                }

                // Generate unique merchant reference
                $merchantReference = $pesapalService->generateMerchantReference($quote['quote_reference'], 'PAYMENT');

                // Create payment record (simplified - no partial payment tracking)
                $paymentData = [
                    'quote_id' => $quote['quote_id'],
                    'amount' => $paymentAmount,
                    'payment_type' => 'full',
                    'merchant_reference' => $merchantReference
                ];

                $paymentId = $paymentModel->createFromQuote($paymentData);
                if (!$paymentId) {
                    throw new Exception('Failed to create payment record.');
                }

                // Parse customer name
                $customerName = $pesapalService->parseCustomerName($quote['customer_name']);

                // Prepare order data for Pesapal
                $orderData = [
                    'merchant_reference' => $merchantReference,
                    'amount' => $pesapalService->formatAmount($paymentAmount),
                    'currency' => 'USD',
                    'description' => "Payment for travel quote {$quote['quote_reference']} ({$paymentType} payment)",
                    'customer_email' => $quote['customer_email'],
                    'customer_phone' => $quote['customer_phone'],
                    'country_code' => 'KE',
                    'first_name' => $customerName['first_name'],
                    'last_name' => $customerName['last_name'],
                    'address' => $quote['customer_country'] ?? '',
                    'city' => '',
                    'state' => '',
                    'postal_code' => '',
                    'zip_code' => '',
                    'notification_id' => $ipnId
                ];

                // Submit order to Pesapal
                $orderResponse = $pesapalService->submitOrderRequest($authToken, $orderData);

                if ($orderResponse && isset($orderResponse['order_tracking_id']) && isset($orderResponse['redirect_url'])) {
                    // Update payment with Pesapal details
                    $pesapalData = [
                        'tracking_id' => $orderResponse['order_tracking_id'],
                        'merchant_reference' => $merchantReference,
                        'order_response' => $orderResponse
                    ];

                    $paymentModel->updatePesapalDetails($paymentId, $pesapalData);

                    // Redirect to Pesapal
                    $pesapalUrl = $orderResponse['redirect_url'];
                    header("Location: " . $pesapalUrl);
                    exit;
                } else {
                    throw new Exception('Failed to initialize payment. Please try again.');
                }

                } catch (Exception $e) {
                    error_log("Payment processing error: " . $e->getMessage());
                    $message = $e->getMessage();
                    $messageType = 'error';
                }
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Secure Payment | Meleva Tours & Travel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        body {
            padding-top: 50px;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #7c2d12, #92400e);
        }
        .payment-card {
            background: #FFE5B4;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            border: 1px solid #FFE5B4;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="gradient-bg py-16 px-4">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-3xl md:text-5xl font-bold mb-6">
                Secure Payment
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8"></div>
            <p class="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
                Complete your booking with our secure payment system
            </p>
        </div>
    </section>

    <!-- Payment Section -->
    <section class="py-16 px-4">
        <div class="max-w-4xl mx-auto">
            <?php if ($message): ?>
                <div class="mb-8 p-4 rounded-lg <?php
                    echo $messageType === 'success' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                ?>">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>



            <?php if ($quote && $quote['quote_status'] === 'quoted' && !$hasPaidPayment): ?>
                <div class="payment-card">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Payment Summary</h2>
                        <p class="text-gray-600">Quote Reference: <span class="font-semibold"><?php echo Utils::displayText($quote['quote_reference']); ?></span></p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Quote Details -->
                        <div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-4">
                                <i class="fas fa-file-invoice text-orange-500 mr-2"></i>
                                Quote Details
                            </h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Customer:</span>
                                    <span class="font-medium"><?php echo Utils::displayText($quote['customer_name']); ?></span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Travel Date:</span>
                                    <span class="font-medium">
                                        <?php echo $quote['travel_date'] ? date('F j, Y', strtotime($quote['travel_date'])) : 'To be determined'; ?>
                                    </span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Travelers:</span>
                                    <span class="font-medium">
                                        <?php echo $quote['number_of_adults']; ?> Adult(s)
                                        <?php if ($quote['number_of_children'] > 0): ?>
                                            , <?php echo $quote['number_of_children']; ?> Child(ren)
                                        <?php endif; ?>
                                    </span>
                                </div>

                            </div>
                        </div>

                        <!-- Payment Information -->
                        <div>
                            <form method="POST" class="space-y-4">
                                <h3 class="text-xl font-semibold text-gray-900 mb-4">
                                    <i class="fas fa-credit-card text-orange-500 mr-2"></i>
                                    Payment Information
                                </h3>

                                <div class="bg-white p-6 rounded-lg border-2 border-orange-200 mb-6">
                                <div class="text-center mb-4">
                                    <p class="text-gray-600 mb-2">Quote Amount</p>
                                    <p class="text-2xl font-bold text-gray-800">
                                        $<?php echo number_format($quote['quoted_amount'], 2); ?>
                                    </p>
                                    <p class="text-sm text-gray-500 mt-2">USD (United States Dollar)</p>
                                </div>

                                <div class="border-t pt-4">
                                    <label for="payment_amount" class="block text-sm font-medium text-gray-700 mb-2">
                                        Amount to Pay
                                    </label>
                                    <div class="relative">
                                        <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">$</span>
                                        <input type="number"
                                               id="payment_amount"
                                               name="payment_amount"
                                               step="0.01"
                                               min="1"
                                               value="<?php echo number_format($quote['quoted_amount'], 2); ?>"
                                               class="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-lg font-semibold text-center"
                                               required>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-2 text-center">
                                        You can pay any amount. The quoted amount is pre-filled for your convenience.
                                    </p>
                                </div>
                            </div>

                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                                <h4 class="font-semibold text-blue-800 mb-2">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    Secure Payment Methods
                                </h4>
                                <ul class="text-sm text-blue-700 space-y-1">
                                    <li><i class="fas fa-check mr-2"></i>Mobile Money (M-Pesa, Airtel Money)</li>
                                    <li><i class="fas fa-check mr-2"></i>Credit/Debit Cards (Visa, Mastercard)</li>
                                    <li><i class="fas fa-check mr-2"></i>Bank Transfers</li>
                                    <li><i class="fas fa-check mr-2"></i>Digital Wallets</li>
                                </ul>
                            </div>

                                <button type="submit" name="proceed_payment"
                                        class="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-8 py-4 rounded-lg font-bold text-lg transition duration-300 transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-lock mr-3"></i>
                                    Proceed to Secure Payment
                                </button>
                                <p class="text-xs text-gray-500 text-center">
                                    Powered by Pesapal - Your payment information is encrypted and secure
                                </p>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>

    <!-- Include Footer -->
    <?php include 'footer.php'; ?>
</body>
</html>
