<?php
require_once __DIR__ . '/../config/pesapal_config.php';

/**
 * Pesapal API Integration Service
 * Handles communication with Pesapal payment gateway
 */
class PesapalService {

    private $consumerKey;
    private $consumerSecret;
    private $baseUrl;
    private $iframeUrl;
    private $callbackUrl;
    private $ipnUrl;

    public function __construct() {
        // Load configuration from config file
        $this->consumerKey = PESAPAL_CONSUMER_KEY;
        $this->consumerSecret = PESAPAL_CONSUMER_SECRET;
        $this->baseUrl = PESAPAL_BASE_URL;
        $this->iframeUrl = PESAPAL_IFRAME_URL;
        $this->callbackUrl = PESAPAL_CALLBACK_URL;
        $this->ipnUrl = PESAPAL_IPN_URL;

        // Validate configuration
        if (!isPesapalConfigured()) {
            error_log('Pesapal API credentials not configured. Please update admin-dashboard/config/pesapal_config.php');
        }
    }

    /**
     * Get callback URL
     */
    public function getCallbackUrl() {
        return $this->callbackUrl;
    }
    
    /**
     * Get OAuth token from Pesapal
     */
    public function getAuthToken() {
        $url = $this->baseUrl . 'Auth/RequestToken';
        
        $data = [
            'consumer_key' => $this->consumerKey,
            'consumer_secret' => $this->consumerSecret
        ];
        
        $response = $this->makeRequest($url, 'POST', $data);
        
        if ($response && isset($response['token'])) {
            return $response['token'];
        }
        
        return false;
    }
    
    /**
     * Register IPN (Instant Payment Notification) URL
     */
    public function registerIPN($token, $ipnUrl) {
        $url = $this->baseUrl . 'URLSetup/RegisterIPN';
        
        $data = [
            'url' => $ipnUrl,
            'ipn_notification_type' => 'GET'
        ];
        
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ];
        
        return $this->makeRequest($url, 'POST', $data, $headers);
    }
    
    /**
     * Submit order request to Pesapal
     */
    public function submitOrderRequest($token, $orderData) {
        $url = $this->baseUrl . 'Transactions/SubmitOrderRequest';
        
        $data = [
            'id' => $orderData['merchant_reference'] ?? $orderData['id'] ?? '',
            'currency' => $orderData['currency'] ?? 'USD',
            'amount' => $orderData['amount'],
            'description' => $orderData['description'],
            'callback_url' => $this->callbackUrl,
            'notification_id' => $orderData['notification_id'] ?? '',
            'billing_address' => [
                'email_address' => $orderData['customer_email'] ?? $orderData['billing_address']['email_address'] ?? '',
                'phone_number' => $orderData['customer_phone'] ?? $orderData['billing_address']['phone_number'] ?? '',
                'country_code' => $orderData['country_code'] ?? $orderData['billing_address']['country_code'] ?? 'US',
                'first_name' => $orderData['first_name'] ?? $orderData['billing_address']['first_name'] ?? 'Customer',
                'last_name' => $orderData['last_name'] ?? $orderData['billing_address']['last_name'] ?? 'Name',
                'line_1' => $orderData['address'] ?? '',
                'line_2' => '',
                'city' => $orderData['city'] ?? '',
                'state' => $orderData['state'] ?? '',
                'postal_code' => $orderData['postal_code'] ?? '',
                'zip_code' => $orderData['zip_code'] ?? ''
            ]
        ];
        
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ];
        
        return $this->makeRequest($url, 'POST', $data, $headers);
    }
    
    /**
     * Get transaction status from Pesapal
     */
    public function getTransactionStatus($token, $orderTrackingId) {
        $url = $this->baseUrl . 'Transactions/GetTransactionStatus?orderTrackingId=' . $orderTrackingId;
        
        $headers = [
            'Authorization: Bearer ' . $token,
            'Content-Type: application/json'
        ];
        
        return $this->makeRequest($url, 'GET', null, $headers);
    }
    
    /**
     * Generate payment URL for iframe
     */
    public function generatePaymentUrl($orderTrackingId) {
        return $this->iframeUrl . '?OrderTrackingId=' . $orderTrackingId;
    }
    
    /**
     * Validate IPN (Instant Payment Notification)
     */
    public function validateIPN($token, $orderTrackingId, $orderMerchantReference) {
        // Get transaction status to validate
        $status = $this->getTransactionStatus($token, $orderTrackingId);
        
        if ($status && isset($status['order_tracking_id']) && $status['order_tracking_id'] === $orderTrackingId) {
            return $status;
        }
        
        return false;
    }
    
    /**
     * Make HTTP request to Pesapal API
     */
    private function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
        $curl = curl_init();
        
        $defaultHeaders = [
            'Accept: application/json',
            'Content-Type: application/json'
        ];
        
        $headers = array_merge($defaultHeaders, $headers);
        
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 90, // 1 minute 30 seconds
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_SSL_VERIFYPEER => false, // Set to true in production
            CURLOPT_SSL_VERIFYHOST => false  // Set to true in production
        ]);
        
        if ($data && ($method === 'POST' || $method === 'PUT')) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        
        curl_close($curl);
        
        if ($error) {
            error_log("Pesapal API Error: " . $error);
            return false;
        }
        
        if ($httpCode >= 400) {
            error_log("Pesapal API HTTP Error: " . $httpCode . " - " . $response);
            return false;
        }
        
        $decodedResponse = json_decode($response, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Pesapal API JSON Error: " . json_last_error_msg());
            return false;
        }
        
        return $decodedResponse;
    }
    
    /**
     * Format amount for Pesapal (ensure 2 decimal places)
     */
    public function formatAmount($amount) {
        return number_format((float)$amount, 2, '.', '');
    }
    
    /**
     * Generate merchant reference
     */
    public function generateMerchantReference($bookingReference, $paymentType) {
        return $bookingReference . '_' . strtoupper($paymentType) . '_' . time();
    }
    
    /**
     * Parse customer name into first and last name
     */
    public function parseCustomerName($fullName) {
        $nameParts = explode(' ', trim($fullName), 2);
        return [
            'first_name' => $nameParts[0] ?? '',
            'last_name' => $nameParts[1] ?? ''
        ];
    }
    
    /**
     * Validate payment amount
     */
    public function validateAmount($amount) {
        $amount = (float)$amount;
        return $amount > 0 && $amount <= 999999.99; // Pesapal limits
    }
    
    /**
     * Get supported currencies
     */
    public function getSupportedCurrencies() {
        return ['USD', 'KES', 'EUR', 'GBP'];
    }
    
    /**
     * Check if currency is supported
     */
    public function isCurrencySupported($currency) {
        return in_array(strtoupper($currency), $this->getSupportedCurrencies());
    }
}
