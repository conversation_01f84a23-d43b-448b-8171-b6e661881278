<?php
/**
 * Test Fixed Reply Functionality
 * Verify that the reply issues are resolved
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Test Fixed Reply Functionality</h2>";
echo "<p>Testing the fixes for reply HTTP 500 error...</p>";

try {
    // Define admin access constant
    define('ADMIN_ACCESS', true);
    
    // Include required files
    require_once 'config/config.php';
    require_once 'classes/models.php';
    require_once 'classes/EmailService.php';
    
    echo "<p>✅ Files included successfully</p>";
    
    // Test authentication
    Auth::startSession();
    if (!Auth::isLoggedIn()) {
        echo "<p style='color: red;'>❌ Not logged in</p>";
        echo "<p><a href='login.php'>Please log in first</a></p>";
        exit;
    }
    
    $currentUser = Auth::getCurrentUser();
    echo "<p>✅ User logged in: " . htmlspecialchars($currentUser['username']) . "</p>";
    
    // Initialize models
    $messageModel = new Message();
    $emailService = new EmailService();
    echo "<p>✅ Models initialized</p>";
    
    echo "<hr>";
    
    // Create test message
    echo "<h3>Creating Test Message</h3>";
    
    $testMessageData = [
        'sender_name' => 'Test Customer Fixed',
        'sender_email' => '<EMAIL>',
        'subject' => 'Contact Form - Fixed Reply Test',
        'message_content' => 'This is a test message to verify the reply fixes work correctly.',
        'message_category' => 'contact'
    ];
    
    $testMessageId = $messageModel->create($testMessageData);
    
    if ($testMessageId) {
        echo "<p style='color: green;'>✅ Test message created with ID: {$testMessageId}</p>";
        
        // Test the FIXED reply process
        echo "<h3>Testing Fixed Reply Process</h3>";
        
        $replyContent = "Thank you for your message. This reply should work without HTTP 500 errors!";
        
        // Step 1: Save reply to database
        echo "<p>Step 1: Saving reply to database...</p>";
        $replyResult = $messageModel->reply($testMessageId, $replyContent);
        
        if ($replyResult) {
            echo "<p style='color: green;'>✅ Reply saved successfully</p>";
            
            // Step 2: Mark as read
            echo "<p>Step 2: Marking as read...</p>";
            $markReadResult = $messageModel->markAsRead($testMessageId);
            
            if ($markReadResult) {
                echo "<p style='color: green;'>✅ Marked as read successfully</p>";
                
                // Step 3: Test FIXED email reply (with proper message_content)
                echo "<p>Step 3: Testing FIXED email reply...</p>";
                
                // Get full message details (FIXED VERSION)
                $fullOriginalMessage = $messageModel->findById($testMessageId);
                $originalMessage = [
                    'message_id' => $testMessageId,
                    'sender_name' => $fullOriginalMessage['sender_name'],
                    'sender_email' => $fullOriginalMessage['sender_email'],
                    'subject' => $fullOriginalMessage['subject'],
                    'message_content' => $fullOriginalMessage['message_content']
                ];
                
                echo "<p><strong>Fixed original message data:</strong></p>";
                echo "<ul>";
                echo "<li>Message ID: " . htmlspecialchars($originalMessage['message_id']) . "</li>";
                echo "<li>Sender: " . htmlspecialchars($originalMessage['sender_name']) . "</li>";
                echo "<li>Email: " . htmlspecialchars($originalMessage['sender_email']) . "</li>";
                echo "<li>Subject: " . htmlspecialchars($originalMessage['subject']) . "</li>";
                echo "<li>Content: " . htmlspecialchars(substr($originalMessage['message_content'], 0, 100)) . "...</li>";
                echo "</ul>";
                
                try {
                    // Test email sending (this should now work without errors)
                    $emailSent = $emailService->sendReplyEmail($originalMessage, $replyContent, $currentUser);
                    
                    if ($emailSent) {
                        echo "<p style='color: green;'>🎉 SUCCESS! Email reply sent without errors!</p>";
                    } else {
                        echo "<p style='color: orange;'>⚠️ Email sending returned false (but no exception thrown)</p>";
                        echo "<p>This might be due to SMTP configuration, but the HTTP 500 error should be fixed</p>";
                    }
                    
                } catch (Exception $emailError) {
                    echo "<p style='color: red;'>❌ Email error: " . htmlspecialchars($emailError->getMessage()) . "</p>";
                    
                    // Check if it's the same database error
                    if (strpos($emailError->getMessage(), 'initial_message_id') !== false) {
                        echo "<p style='color: red;'>⚠️ Still having initial_message_id issue - needs further investigation</p>";
                    } else {
                        echo "<p style='color: orange;'>This might be an SMTP configuration issue, not the HTTP 500 error</p>";
                    }
                }
                
            } else {
                echo "<p style='color: red;'>❌ Failed to mark as read</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Failed to save reply</p>";
        }
        
        // Clean up
        $deleteStmt = $messageModel->getDb()->prepare("DELETE FROM messages WHERE message_id = :id");
        $deleteStmt->bindParam(':id', $testMessageId);
        $deleteStmt->execute();
        echo "<p>✅ Test message cleaned up</p>";
        
    } else {
        echo "<p style='color: red;'>❌ Failed to create test message</p>";
    }
    
    echo "<hr>";
    
    // Test timezone display
    echo "<h3>Testing Timezone Display</h3>";
    
    $currentTime = new DateTime('now', new DateTimeZone(DatabaseConfig::TIMEZONE));
    echo "<p><strong>Current time in " . DatabaseConfig::TIMEZONE . ":</strong> " . $currentTime->format('Y-m-d H:i:s T') . "</p>";
    
    $utcTime = new DateTime('now', new DateTimeZone('UTC'));
    echo "<p><strong>Current time in UTC:</strong> " . $utcTime->format('Y-m-d H:i:s T') . "</p>";
    
    echo "<p><strong>Database timezone setting:</strong> +03:00 (Africa/Nairobi)</p>";
    
    echo "<hr>";
    
    // Summary
    echo "<h3>🎯 Fix Summary</h3>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
    echo "<h4>✅ Issues Fixed:</h4>";
    echo "<ul>";
    echo "<li>✅ <strong>Missing message_content:</strong> Now fetching full message details from database</li>";
    echo "<li>✅ <strong>Database constraint violation:</strong> Fixed initial_message_id null issue</li>";
    echo "<li>✅ <strong>Unauthorized email:</strong> Removed <EMAIL></li>";
    echo "<li>✅ <strong>Time references:</strong> Removed all duration promises from templates</li>";
    echo "</ul>";
    
    echo "<h4>🚀 What Should Now Work:</h4>";
    echo "<ul>";
    echo "<li>📧 Reply functionality without HTTP 500 errors</li>";
    echo "<li>⏰ Proper timezone display (Africa/Nairobi UTC+3)</li>";
    echo "<li>📨 Only authorized emails: info, no_reply, admin, booking</li>";
    echo "<li>💬 Clean messages without time commitments</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← Test Reply in Messages Admin</a> | <a href='../contact.php'>Test Contact Form</a></p>";
?>
