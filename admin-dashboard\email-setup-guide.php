<?php
/**
 * Email Setup Guide - Instructions for setting up email fetching
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';

// Include security middleware for web access
require_once 'includes/security_middleware.php';
requireRole('admin');

// Check system capabilities
$imapAvailable = extension_loaded('imap');
$curlAvailable = extension_loaded('curl');
$phpVersion = phpversion();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Setup Guide - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gray-900">Email Setup Guide</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="messages.php" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Messages
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- System Status -->
            <div class="bg-white shadow rounded-lg mb-8">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h2 class="text-lg font-medium text-gray-900">System Status</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="<?php echo $imapAvailable ? 'text-green-600' : 'text-red-600'; ?> text-3xl mb-2">
                                <i class="fas fa-<?php echo $imapAvailable ? 'check-circle' : 'times-circle'; ?>"></i>
                            </div>
                            <h3 class="font-semibold">IMAP Extension</h3>
                            <p class="text-sm text-gray-600">
                                <?php echo $imapAvailable ? 'Available' : 'Not Available'; ?>
                            </p>
                        </div>
                        
                        <div class="text-center">
                            <div class="<?php echo $curlAvailable ? 'text-green-600' : 'text-red-600'; ?> text-3xl mb-2">
                                <i class="fas fa-<?php echo $curlAvailable ? 'check-circle' : 'times-circle'; ?>"></i>
                            </div>
                            <h3 class="font-semibold">cURL Extension</h3>
                            <p class="text-sm text-gray-600">
                                <?php echo $curlAvailable ? 'Available' : 'Not Available'; ?>
                            </p>
                        </div>
                        
                        <div class="text-center">
                            <div class="text-blue-600 text-3xl mb-2">
                                <i class="fas fa-info-circle"></i>
                            </div>
                            <h3 class="font-semibold">PHP Version</h3>
                            <p class="text-sm text-gray-600"><?php echo $phpVersion; ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Available Methods -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Method 1: Manual Entry -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-hand-paper text-blue-600 mr-2"></i>
                            Method 1: Manual Entry
                        </h3>
                        <span class="inline-block bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Recommended</span>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">
                            Manually add customer email replies to your system. Perfect for occasional use.
                        </p>
                        
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-sm">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>Works immediately</span>
                            </div>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>No technical setup required</span>
                            </div>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-check text-green-600 mr-2"></i>
                                <span>Full control over what gets added</span>
                            </div>
                        </div>
                        
                        <a href="simple-email-checker.php" 
                           class="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-300 text-center block">
                            <i class="fas fa-plus mr-2"></i>Add Email Replies
                        </a>
                    </div>
                </div>

                <!-- Method 2: IMAP Fetching -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-download text-orange-600 mr-2"></i>
                            Method 2: IMAP Fetching
                        </h3>
                        <span class="inline-block bg-<?php echo $imapAvailable ? 'green' : 'red'; ?>-100 text-<?php echo $imapAvailable ? 'green' : 'red'; ?>-800 text-xs px-2 py-1 rounded-full">
                            <?php echo $imapAvailable ? 'Available' : 'Requires Setup'; ?>
                        </span>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">
                            Automatically fetch emails from your email server using IMAP protocol.
                        </p>
                        
                        <?php if ($imapAvailable): ?>
                            <div class="space-y-3 mb-6">
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-check text-green-600 mr-2"></i>
                                    <span>Automatic email fetching</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-check text-green-600 mr-2"></i>
                                    <span>Processes multiple accounts</span>
                                </div>
                                <div class="flex items-center text-sm">
                                    <i class="fas fa-check text-green-600 mr-2"></i>
                                    <span>Can be scheduled with cron</span>
                                </div>
                            </div>
                            
                            <div class="space-y-2">
                                <a href="email-config-wizard.php"
                                   class="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition duration-300 text-center block">
                                    <i class="fas fa-cog mr-2"></i>Configure IMAP Settings
                                </a>
                                <a href="test-imap-connection.php"
                                   class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition duration-300 text-center block">
                                    <i class="fas fa-plug mr-2"></i>Test Connection
                                </a>
                                <a href="email-fetcher.php"
                                   class="w-full bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition duration-300 text-center block">
                                    <i class="fas fa-download mr-2"></i>Fetch Emails Now
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                <p class="text-yellow-800 text-sm">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    IMAP extension is not installed. Contact your hosting provider to enable it.
                                </p>
                            </div>
                            
                            <div class="space-y-2 text-sm text-gray-600">
                                <p><strong>For XAMPP/Local:</strong></p>
                                <code class="bg-gray-100 px-2 py-1 rounded">
                                    Uncomment ;extension=imap in php.ini
                                </code>
                                
                                <p class="mt-3"><strong>For Ubuntu/Linux:</strong></p>
                                <code class="bg-gray-100 px-2 py-1 rounded">
                                    sudo apt-get install php-imap
                                </code>
                                
                                <p class="mt-3"><strong>For CPanel/Shared Hosting:</strong></p>
                                <p>Contact your hosting provider to enable IMAP extension</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Method 3: Email Forwarding -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-share text-purple-600 mr-2"></i>
                            Method 3: Email Forwarding
                        </h3>
                        <span class="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Advanced</span>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">
                            Set up email forwarding rules to automatically process incoming emails.
                        </p>
                        
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-sm">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <span>Requires email server configuration</span>
                            </div>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <span>Real-time processing</span>
                            </div>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <span>Most efficient method</span>
                            </div>
                        </div>
                        
                        <button class="w-full bg-gray-400 text-white px-4 py-2 rounded-lg cursor-not-allowed" disabled>
                            <i class="fas fa-tools mr-2"></i>Requires Server Setup
                        </button>
                    </div>
                </div>

                <!-- Method 4: Webhook Integration -->
                <div class="bg-white shadow rounded-lg">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-webhook text-indigo-600 mr-2"></i>
                            Method 4: Webhook Integration
                        </h3>
                        <span class="inline-block bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Advanced</span>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-600 mb-4">
                            Use webhooks from email service providers like SendGrid, Mailgun, etc.
                        </p>
                        
                        <div class="space-y-3 mb-6">
                            <div class="flex items-center text-sm">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <span>Requires third-party service</span>
                            </div>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <span>Real-time notifications</span>
                            </div>
                            <div class="flex items-center text-sm">
                                <i class="fas fa-cog text-gray-400 mr-2"></i>
                                <span>Highly reliable</span>
                            </div>
                        </div>
                        
                        <button class="w-full bg-gray-400 text-white px-4 py-2 rounded-lg cursor-not-allowed" disabled>
                            <i class="fas fa-cloud mr-2"></i>Requires Service Setup
                        </button>
                    </div>
                </div>
            </div>

            <!-- Quick Start Guide -->
            <div class="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-green-900 mb-4">
                    <i class="fas fa-rocket mr-2"></i>Quick Start Recommendation
                </h3>
                
                <div class="space-y-4 text-green-800">
                    <div class="flex items-start">
                        <div class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">1</div>
                        <div>
                            <strong>Start with Manual Entry:</strong> Use the <a href="simple-email-checker.php" class="underline">Simple Email Checker</a> to immediately start capturing customer replies.
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">2</div>
                        <div>
                            <strong>Enable IMAP (Optional):</strong> If you want automatic fetching, install the IMAP extension and use the <a href="email-fetcher.php" class="underline">Email Fetcher</a>.
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-green-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 mt-0.5">3</div>
                        <div>
                            <strong>Set Up Automation:</strong> Once comfortable, consider setting up email forwarding or webhooks for real-time processing.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
