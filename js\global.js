/**
 * <PERSON>eva Tours Global JavaScript
 * Common functionality used across all pages
 */

class MelevaGlobal {
    constructor() {
        // Navigation scroll properties
        this.lastScrollTop = 0;
        this.scrollThreshold = 100;
        this.isNavVisible = true;
        this.mainNav = null;
        this.goToTopBtn = null;
        this.hasHeroSection = false;

        // Image optimization properties
        this.lazyImages = [];
        this.imageObserver = null;
        this.webpSupported = false;

        this.init();
    }

    /**
     * Initialize all global functionality
     */
    init() {
        document.addEventListener('DOMContentLoaded', () => {
            this.checkWebPSupport();
            this.initializeNavigation();
            this.createGoToTopButton();
            this.bindScrollEvents();
            this.bindClickEvents();
            this.initializeSmoothScrolling();
            this.initializeFooter();
            this.initializeImageOptimization();
        });
    }

    /**
     * Initialize navigation with hide-on-scroll functionality
     */
    initializeNavigation() {
        this.mainNav = document.querySelector('#main-nav');

        if (!this.mainNav) {
            console.error('Main navigation not found!');
            return;
        }

        // Check if page has hero section
        this.hasHeroSection = this.mainNav.getAttribute('data-has-hero') === 'true';

        // Set initial state
        if (this.hasHeroSection) {
            this.mainNav.classList.add('transparent-nav');
            this.mainNav.classList.remove('solid-nav');
        } else {
            this.mainNav.classList.add('solid-nav');
            this.mainNav.classList.remove('transparent-nav');
        }

        // Ensure fixed positioning
        this.mainNav.classList.add('fixed-nav');

        // Set initial visibility state
        this.mainNav.style.transform = 'translateY(0)';
        this.isNavVisible = true;
    }

    /**
     * Create go-to-top button
     */
    createGoToTopButton() {
        this.goToTopBtn = document.createElement('button');
        this.goToTopBtn.id = 'go-to-top';
        this.goToTopBtn.innerHTML = '<i class="fas fa-chevron-up"></i>';
        this.goToTopBtn.className = 'fixed bottom-6 right-6 w-12 h-12 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full shadow-lg hover:shadow-xl transform hover:scale-110 transition-all duration-300 z-40 opacity-0 invisible';
        this.goToTopBtn.setAttribute('aria-label', 'Go to top');
        this.goToTopBtn.title = 'Go to top';

        this.goToTopBtn.addEventListener('mouseenter', () => {
            this.goToTopBtn.style.background = 'linear-gradient(135deg, #ea580c, #dc2626)';
        });

        this.goToTopBtn.addEventListener('mouseleave', () => {
            this.goToTopBtn.style.background = 'linear-gradient(135deg, #f97316, #ea580c)';
        });

        document.body.appendChild(this.goToTopBtn);
    }

    /**
     * Bind scroll events
     */
    bindScrollEvents() {
        let ticking = false;

        window.addEventListener('scroll', () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.handleScroll();
                    ticking = false;
                });
                ticking = true;
            }
        });
    }

    /**
     * Handle scroll behavior
     */
    handleScroll() {
        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

        this.handleNavigationScroll(currentScrollTop);
        this.handleGoToTopButton(currentScrollTop);

        this.lastScrollTop = currentScrollTop;
    }

    /**
     * Handle navigation scroll behavior
     */
// In your MelevaGlobal class, update the handleNavigationScroll method:

handleNavigationScroll(currentScrollTop) {
    if (!this.mainNav) return;

    const scrollingDown = currentScrollTop > this.lastScrollTop;
    const scrollDelta = Math.abs(currentScrollTop - this.lastScrollTop);

    // Handle transparency state for hero pages
    if (this.hasHeroSection) {
        if (currentScrollTop > this.scrollThreshold) {
            this.mainNav.classList.add('scrolled');
        } else {
            this.mainNav.classList.remove('scrolled');
        }
    }

    // Handle show/hide logic
    if (currentScrollTop <= 0 && this.hasHeroSection) {
        // Hide navigation when at the very top and has hero section
        this.mainNav.classList.add('hidden');
    } else if (currentScrollTop <= this.scrollThreshold) {
        // Always show navigation when near top (but not at the very top with hero section)
        this.mainNav.classList.remove('hidden');
    } else if (scrollDelta > 5) {
        if (scrollingDown && currentScrollTop > 100) {
            // Hide when scrolling down past threshold
            this.mainNav.classList.add('hidden');
        } else {
            // Show when scrolling up
            this.mainNav.classList.remove('hidden');
        }
    }

    this.lastScrollTop = currentScrollTop;
}

    /**
     * Make navigation transparent (for hero sections)
     */
    makeNavigationTransparent() {
        if (!this.mainNav || !this.hasHeroSection) return;

        this.mainNav.classList.add('transparent-nav');
        this.mainNav.classList.remove('solid-nav');
    }

    /**
     * Make navigation solid white
     */
    makeNavigationSolid() {
        if (!this.mainNav) return;

        this.mainNav.classList.add('solid-nav');
        this.mainNav.classList.remove('transparent-nav');
    }

    /**
     * Show navigation
     */
    showNavigation() {
        if (this.mainNav && !this.isNavVisible) {
            this.mainNav.style.transform = 'translateY(0)';
            this.isNavVisible = true;
        }
    }

    /**
     * Hide navigation
     */
    hideNavigation() {
        if (this.mainNav && this.isNavVisible) {
            this.mainNav.style.transform = 'translateY(-100%)';
            this.isNavVisible = false;
        }
    }

    /**
     * Handle go-to-top button visibility
     */
    handleGoToTopButton(currentScrollTop) {
        if (!this.goToTopBtn) return;

        if (currentScrollTop > 300) {
            this.goToTopBtn.style.opacity = '1';
            this.goToTopBtn.style.visibility = 'visible';
        } else {
            this.goToTopBtn.style.opacity = '0';
            this.goToTopBtn.style.visibility = 'hidden';
        }
    }

    /**
     * Bind click events
     */
    bindClickEvents() {
        if (this.goToTopBtn) {
            this.goToTopBtn.addEventListener('click', () => {
                this.scrollToTop();
            });
        }
    }

    /**
     * Smooth scroll to top
     */
    scrollToTop() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    initializeSmoothScrolling() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('a[href^="#"]')) {
                e.preventDefault();
                const targetId = e.target.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const offsetTop = targetElement.offsetTop - 80;
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            }
        });
    }

    /**
     * Initialize footer functionality
     */
    initializeFooter() {
        const yearElement = document.getElementById('current-year');
        if (yearElement) {
            yearElement.textContent = new Date().getFullYear();
        }
    }

    /**
     * Check WebP support
     */
    checkWebPSupport() {
        const webP = new Image();
        webP.onload = webP.onerror = () => {
            this.webpSupported = (webP.height === 2);
        };
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
    }

    /**
     * Initialize image optimization features
     */
    initializeImageOptimization() {
        this.setupLazyLoading();
        this.optimizeExistingImages();
        this.preloadCriticalImages();
    }

    /**
     * Setup lazy loading for images
     */
    setupLazyLoading() {
        // Find all images with data-src attribute (lazy loading)
        this.lazyImages = document.querySelectorAll('img[data-src]');

        if ('IntersectionObserver' in window) {
            this.imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        observer.unobserve(img);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            this.lazyImages.forEach(img => {
                this.imageObserver.observe(img);
            });
        } else {
            // Fallback for browsers without IntersectionObserver
            this.lazyImages.forEach(img => {
                this.loadImage(img);
            });
        }
    }

    /**
     * Load individual image with optimization
     */
    loadImage(img) {
        const src = img.dataset.src;
        const srcset = img.dataset.srcset;

        if (!src) return;

        // Create a new image to test loading
        const imageLoader = new Image();

        imageLoader.onload = () => {
            // Image loaded successfully, update the actual img element
            img.src = src;
            if (srcset) {
                img.srcset = srcset;
            }

            // Add loaded class for fade-in effect
            img.classList.add('image-loaded');

            // Remove data attributes to prevent reprocessing
            delete img.dataset.src;
            delete img.dataset.srcset;
        };

        imageLoader.onerror = () => {
            // Handle image load error
            img.classList.add('image-error');
            console.warn('Failed to load image:', src);
        };

        // Start loading
        imageLoader.src = src;
    }

    /**
     * Optimize existing images that are already loaded
     */
    optimizeExistingImages() {
        const images = document.querySelectorAll('img:not([data-src])');

        images.forEach(img => {
            // Add loading optimization
            if (!img.loading) {
                img.loading = 'lazy';
            }

            // Add decode hint for better performance
            img.decoding = 'async';

            // Add error handling
            img.onerror = () => {
                img.classList.add('image-error');
                console.warn('Failed to load image:', img.src);
            };
        });
    }

    /**
     * Preload critical images (above the fold)
     */
    preloadCriticalImages() {
        const criticalImages = document.querySelectorAll('.critical-image, .hero-image, .logo');

        criticalImages.forEach(img => {
            if (img.dataset.src) {
                // For lazy loaded critical images, load them immediately
                this.loadImage(img);
            } else if (img.src) {
                // For already loaded images, ensure they're prioritized
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = img.src;
                document.head.appendChild(link);
            }
        });
    }

    /**
     * Generate responsive image srcset
     */
    generateSrcSet(basePath, sizes = [400, 800, 1200, 1600]) {
        return sizes.map(size => {
            const path = this.getOptimizedImagePath(basePath, size);
            return `${path} ${size}w`;
        }).join(', ');
    }

    /**
     * Get optimized image path with size and format
     */
    getOptimizedImagePath(originalPath, width = null, format = null) {
        if (!originalPath) return originalPath;

        const pathParts = originalPath.split('.');
        const extension = pathParts.pop();
        const basePath = pathParts.join('.');

        // Use WebP if supported and not explicitly requesting another format
        const targetFormat = format || (this.webpSupported ? 'webp' : extension);

        // Add width parameter if specified
        const widthSuffix = width ? `_${width}w` : '';

        return `${basePath}${widthSuffix}.${targetFormat}`;
    }

    /**
     * Create responsive image element
     */
    createResponsiveImage(src, alt, className = '', sizes = '100vw') {
        const img = document.createElement('img');

        // Set up lazy loading
        img.dataset.src = src;
        img.dataset.srcset = this.generateSrcSet(src);
        img.alt = alt;
        img.className = className;
        img.sizes = sizes;
        img.loading = 'lazy';
        img.decoding = 'async';

        // Add placeholder
        img.src = 'data:image/svg+xml;base64,' + btoa(`
            <svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
                <rect width="100%" height="100%" fill="#f3f4f6"/>
                <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#9ca3af">Loading...</text>
            </svg>
        `);

        return img;
    }
}

// Image optimization utilities
class ImageOptimizer {
    /**
     * Compress image file on client side
     */
    static compressImage(file, maxWidth = 1200, quality = 0.8) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Calculate new dimensions
                let { width, height } = img;

                if (width > maxWidth) {
                    height = (height * maxWidth) / width;
                    width = maxWidth;
                }

                canvas.width = width;
                canvas.height = height;

                // Draw and compress
                ctx.drawImage(img, 0, 0, width, height);

                canvas.toBlob(resolve, 'image/jpeg', quality);
            };

            img.src = URL.createObjectURL(file);
        });
    }

    /**
     * Generate image thumbnail
     */
    static generateThumbnail(file, size = 150) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                canvas.width = size;
                canvas.height = size;

                // Calculate crop dimensions for square thumbnail
                const minDimension = Math.min(img.width, img.height);
                const x = (img.width - minDimension) / 2;
                const y = (img.height - minDimension) / 2;

                ctx.drawImage(img, x, y, minDimension, minDimension, 0, 0, size, size);

                canvas.toBlob(resolve, 'image/jpeg', 0.8);
            };

            img.src = URL.createObjectURL(file);
        });
    }
}

// Initialize global functionality
const melevaGlobal = new MelevaGlobal();

// Export for manual use
window.MelevaGlobal = MelevaGlobal;
window.melevaGlobal = melevaGlobal;