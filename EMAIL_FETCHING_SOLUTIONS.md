# Email Fetching Solutions - Meleva Tours

## Overview
This document provides comprehensive solutions for capturing customer email replies that are sent directly to your email addresses (<EMAIL>, <EMAIL>) and adding them to your admin messages system.

## The Problem
When customers reply to your quote emails or contact confirmations, their responses go to your email inbox but don't automatically appear in your admin messages system. This means you might miss important customer communications.

## Available Solutions

### 🟢 **Solution 1: Manual Entry (Immediate Use)**
**File**: `admin-dashboard/simple-email-checker.php`

**Best for**: Immediate use, occasional replies, full control

**How it works**:
1. Check your email inbox (<EMAIL>, <EMAIL>)
2. Copy customer reply details into the web form
3. System adds the reply to your messages with proper categorization

**Advantages**:
- ✅ Works immediately, no setup required
- ✅ Full control over what gets added
- ✅ No technical dependencies
- ✅ Perfect for low-volume businesses

**Steps to use**:
1. Go to `admin-dashboard/simple-email-checker.php`
2. Fill in customer details from their email
3. Select category (Contact/Quote)
4. Paste email content
5. Click "Add Email Reply"

---

### 🟡 **Solution 2: IMAP Automatic Fetching (Recommended)**
**File**: `admin-dashboard/email-fetcher.php`

**Best for**: Automatic processing, high email volume, hands-off operation

**Requirements**:
- PHP IMAP extension installed
- Email server IMAP access

**How it works**:
1. Connects to your email server via IMAP
2. Fetches unread emails from configured accounts
3. Processes and categorizes emails automatically
4. Adds them to your messages system
5. Marks emails as read

**Setup Instructions**:

#### Step 1: Check IMAP Extension
```bash
# Check if IMAP is installed
php -m | grep imap
```

#### Step 2: Install IMAP (if needed)
```bash
# Ubuntu/Debian
sudo apt-get install php-imap

# CentOS/RHEL
sudo yum install php-imap

# XAMPP - Edit php.ini and uncomment:
extension=imap
```

#### Step 3: Configure Email Accounts
Edit `admin-dashboard/email-fetcher.php` and update the config:
```php
'accounts' => [
    [
        'email' => '<EMAIL>',
        'password' => 'your_password',
        'category' => 'contact'
    ],
    [
        'email' => '<EMAIL>', 
        'password' => 'your_password',
        'category' => 'quote'
    ]
]
```

#### Step 4: Test Manual Fetch
```bash
cd admin-dashboard
php email-fetcher.php
```

#### Step 5: Set Up Automatic Fetching
```bash
# Make setup script executable
chmod +x setup-email-cron.sh

# Run setup script
./setup-email-cron.sh
```

**Advantages**:
- ✅ Fully automatic
- ✅ Processes multiple email accounts
- ✅ Handles email threading
- ✅ Can be scheduled with cron jobs
- ✅ Cleans email content (removes signatures, quoted text)

---

### 🔵 **Solution 3: Email Forwarding (Advanced)**
**Best for**: Real-time processing, server administrators

**How it works**:
1. Set up email forwarding rules on your email server
2. Forward incoming emails to a processing script
3. Script processes emails in real-time

**Setup Requirements**:
- Email server access (cPanel, Plesk, or direct server access)
- Ability to set up email forwarding rules
- Web server that can receive POST requests

**Configuration Example**:
1. Set up forwarding rule: `<EMAIL>` → `https://yourdomain.com/admin-dashboard/email-reply-handler.php`
2. Configure the handler script to process forwarded emails
3. Test with a sample email

---

### 🟣 **Solution 4: Webhook Integration (Enterprise)**
**Best for**: High-volume businesses, integration with email service providers

**Supported Services**:
- SendGrid
- Mailgun
- Amazon SES
- Postmark

**How it works**:
1. Configure your email service to send webhooks
2. Set up webhook endpoint to receive notifications
3. Process incoming email data automatically

---

## Quick Start Guide

### For Immediate Use (5 minutes):
1. Go to `admin-dashboard/simple-email-checker.php`
2. Start manually adding customer replies
3. Use this while setting up automatic solutions

### For Automatic Processing (30 minutes):
1. Check if IMAP is installed: `php -m | grep imap`
2. If not installed, install it (see instructions above)
3. Configure `admin-dashboard/email-fetcher.php` with your email credentials
4. Test: `php admin-dashboard/email-fetcher.php`
5. Set up cron job: `./admin-dashboard/setup-email-cron.sh`

### For Advanced Users:
1. Set up email forwarding rules
2. Configure webhook endpoints
3. Implement custom processing logic

## File Structure

```
admin-dashboard/
├── simple-email-checker.php      # Manual email entry interface
├── email-fetcher.php             # IMAP automatic fetching
├── email-reply-handler.php       # Generic email processing
├── email-setup-guide.php         # Setup instructions and status
├── setup-email-cron.sh          # Cron job setup script
└── EMAIL_FETCHING_SOLUTIONS.md   # This documentation
```

## Configuration

### Email Server Settings
```php
'host' => 'melevatours.co.ke',
'port' => 993,
'encryption' => 'ssl',
```

### Account Configuration
```php
'accounts' => [
    [
        'email' => '<EMAIL>',
        'password' => 'your_password',
        'category' => 'contact'
    ],
    [
        'email' => '<EMAIL>',
        'password' => 'your_password',
        'category' => 'quote'
    ]
]
```

## Cron Job Examples

```bash
# Every 5 minutes (high frequency)
*/5 * * * * cd /path/to/admin-dashboard && php email-fetcher.php

# Every 15 minutes (recommended)
*/15 * * * * cd /path/to/admin-dashboard && php email-fetcher.php

# Every hour (low frequency)
0 * * * * cd /path/to/admin-dashboard && php email-fetcher.php
```

## Troubleshooting

### IMAP Connection Issues
1. **Check credentials**: Verify email and password
2. **Check server settings**: Confirm host, port, and encryption
3. **Check firewall**: Ensure port 993 (IMAP SSL) is open
4. **Check email server**: Verify IMAP is enabled

### Permission Issues
```bash
# Fix file permissions
chmod 755 admin-dashboard/email-fetcher.php
chmod +x admin-dashboard/setup-email-cron.sh

# Fix log file permissions
sudo touch /var/log/meleva-email-fetcher.log
sudo chmod 666 /var/log/meleva-email-fetcher.log
```

### Database Issues
1. **Run database migration**: `admin-dashboard/setup-message-categories.php`
2. **Check table structure**: `admin-dashboard/check-messages-table.php`
3. **Test message creation**: `admin-dashboard/test-messages.php`

## Security Considerations

### Email Credentials
- Store passwords securely
- Use environment variables for production
- Consider using app-specific passwords

### Access Control
- Restrict access to email fetching scripts
- Use HTTPS for web interfaces
- Implement proper authentication

### Data Validation
- Validate email addresses
- Sanitize email content
- Prevent SQL injection

## Monitoring and Logs

### Log Files
- **Cron logs**: `/var/log/meleva-email-fetcher.log`
- **PHP errors**: Check PHP error log
- **Web server logs**: Check Apache/Nginx logs

### Monitoring Commands
```bash
# View recent email fetcher logs
tail -f /var/log/meleva-email-fetcher.log

# Check cron job status
crontab -l

# Test email fetcher manually
php admin-dashboard/email-fetcher.php
```

## Performance Optimization

### For High Volume
- Increase PHP memory limit
- Optimize database queries
- Use email filtering rules
- Implement batch processing

### For Low Volume
- Reduce cron frequency
- Use manual processing
- Implement on-demand fetching

## Integration with Existing System

### Message Categories
- **Contact**: General inquiries, support requests
- **Quote**: Booking discussions, quote responses
- **General**: Uncategorized messages

### Reply Threading
- Links customer replies to original messages
- Maintains conversation history
- Enables better customer service

### Status Tracking
- Tracks read/unread status
- Monitors reply status
- Provides admin notifications

## Next Steps

1. **Choose your solution** based on your needs and technical capabilities
2. **Start with manual entry** for immediate results
3. **Implement automatic fetching** for long-term efficiency
4. **Monitor and optimize** based on your email volume
5. **Consider advanced solutions** as your business grows

The email fetching system ensures no customer communication is lost and provides a seamless experience for both customers and administrators.
