<?php
/**
 * Test All Message Creation Methods
 * Test both contact form and email fetcher message creation
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing All Message Creation Methods</h2>";
echo "<p>Testing contact form, email fetcher, and admin compatibility...</p>";

try {
    // Include required files
    require_once 'config/config.php';
    require_once 'classes/models.php';
    
    echo "<p>✅ Files included successfully</p>";
    
    // Test database connection
    $database = Database::getInstance();
    $db = $database->getConnection();
    echo "<p>✅ Database connected successfully</p>";
    
    // Test Message model
    $messageModel = new Message();
    echo "<p>✅ Message model created successfully</p>";
    
    echo "<hr>";
    
    // Test 1: Contact Form Message (Simple)
    echo "<h3>Test 1: Contact Form Message</h3>";
    
    $contactData = [
        'sender_name' => 'John Doe',
        'sender_email' => '<EMAIL>',
        'subject' => 'Contact Form - Website Inquiry',
        'message_content' => 'Hello, I would like to know more about your tour packages.',
        'message_category' => 'contact'
    ];
    
    echo "<p><strong>Contact form data:</strong></p>";
    echo "<pre>" . print_r($contactData, true) . "</pre>";
    
    $contactMessageId = $messageModel->create($contactData);
    
    if ($contactMessageId) {
        echo "<p style='color: green;'>✅ Contact form message created successfully! ID: {$contactMessageId}</p>";
    } else {
        echo "<p style='color: red;'>❌ Contact form message creation failed</p>";
    }
    
    echo "<hr>";
    
    // Test 2: Email Fetcher Message (Complex)
    echo "<h3>Test 2: Email Fetcher Message</h3>";
    
    $emailData = [
        'sender_name' => 'Jane Smith',
        'sender_email' => '<EMAIL>',
        'subject' => 'Re: Your Travel Quote - QT2024001',
        'message_content' => 'Thank you for the quote. I would like to proceed with the booking.',
        'message_type' => 'incoming',
        'message_category' => 'quote',
        'conversation_id' => 'CONV_20240724_001',
        'email_message_id' => '<<EMAIL>>',
        'in_reply_to' => '<<EMAIL>>',
        'email_thread_id' => 'thread_789',
        'email_status' => 'delivered',
        'original_message_id' => null
    ];
    
    echo "<p><strong>Email fetcher data:</strong></p>";
    echo "<pre>" . print_r($emailData, true) . "</pre>";
    
    $emailMessageId = $messageModel->create($emailData);
    
    if ($emailMessageId) {
        echo "<p style='color: green;'>✅ Email fetcher message created successfully! ID: {$emailMessageId}</p>";
    } else {
        echo "<p style='color: red;'>❌ Email fetcher message creation failed</p>";
    }
    
    echo "<hr>";
    
    // Test 3: Basic Message (Minimal data)
    echo "<h3>Test 3: Basic Message (Minimal Data)</h3>";
    
    $basicData = [
        'sender_name' => 'Test User',
        'sender_email' => '<EMAIL>',
        'subject' => 'Basic Test',
        'message_content' => 'This is a basic test message.'
    ];
    
    echo "<p><strong>Basic message data:</strong></p>";
    echo "<pre>" . print_r($basicData, true) . "</pre>";
    
    $basicMessageId = $messageModel->create($basicData);
    
    if ($basicMessageId) {
        echo "<p style='color: green;'>✅ Basic message created successfully! ID: {$basicMessageId}</p>";
    } else {
        echo "<p style='color: red;'>❌ Basic message creation failed</p>";
    }
    
    echo "<hr>";
    
    // Verify all messages were saved correctly
    echo "<h3>Verification: Check Saved Messages</h3>";
    
    $messageIds = array_filter([$contactMessageId, $emailMessageId, $basicMessageId]);
    
    if (!empty($messageIds)) {
        $placeholders = implode(',', array_fill(0, count($messageIds), '?'));
        $stmt = $db->prepare("SELECT message_id, sender_name, subject, message_category, message_type, conversation_id FROM messages WHERE message_id IN ($placeholders) ORDER BY message_id DESC");
        $stmt->execute($messageIds);
        $savedMessages = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr><th>ID</th><th>Sender</th><th>Subject</th><th>Category</th><th>Type</th><th>Conversation ID</th></tr>";
        
        foreach ($savedMessages as $msg) {
            echo "<tr>";
            echo "<td>{$msg['message_id']}</td>";
            echo "<td>" . htmlspecialchars($msg['sender_name']) . "</td>";
            echo "<td>" . htmlspecialchars($msg['subject']) . "</td>";
            echo "<td><strong>{$msg['message_category']}</strong></td>";
            echo "<td>{$msg['message_type']}</td>";
            echo "<td>" . htmlspecialchars($msg['conversation_id'] ?: 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Clean up test messages
        $stmt = $db->prepare("DELETE FROM messages WHERE message_id IN ($placeholders)");
        $stmt->execute($messageIds);
        echo "<p>✅ Test messages cleaned up</p>";
    }
    
    echo "<hr>";
    
    // Summary
    echo "<h3>🎉 Test Results Summary</h3>";
    
    $successCount = 0;
    if ($contactMessageId) $successCount++;
    if ($emailMessageId) $successCount++;
    if ($basicMessageId) $successCount++;
    
    if ($successCount === 3) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ ALL TESTS PASSED! (3/3)</h4>";
        echo "<ul>";
        echo "<li>✅ Contact form messages work perfectly</li>";
        echo "<li>✅ Email fetcher messages work perfectly</li>";
        echo "<li>✅ Basic messages work perfectly</li>";
        echo "<li>✅ All database fields are properly handled</li>";
        echo "<li>✅ Message categorization works correctly</li>";
        echo "</ul>";
        echo "<p><strong>Your system is ready for:</strong></p>";
        echo "<ul>";
        echo "<li>📝 Contact form submissions</li>";
        echo "<li>📧 Automatic email reply fetching</li>";
        echo "<li>🏷️ Proper message categorization</li>";
        echo "<li>🔗 Email threading and conversation tracking</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px;'>";
        echo "<h4>⚠️ SOME TESTS FAILED ({$successCount}/3)</h4>";
        echo "<p>Check the individual test results above for details.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<h3 style='color: red;'>❌ Fatal Error</h3>";
    echo "<p style='color: red;'><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>Stack trace:</strong></p>";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
}

echo "<hr>";
echo "<p><a href='messages.php'>← View Messages Admin</a> | <a href='../contact.php'>Test Contact Form</a> | <a href='email-fetcher.php'>Test Email Fetcher</a></p>";
?>
