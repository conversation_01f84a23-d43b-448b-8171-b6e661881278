<?php
/**
 * Simple Email Checker - Alternative method without IMAP
 * This creates a simple interface for manually adding email replies
 */

// Define admin access constant
define('ADMIN_ACCESS', true);

// Include required files
require_once 'config/config.php';
require_once 'classes/models.php';

// Include security middleware for web access
require_once 'includes/security_middleware.php';
requireRole('admin');

$messageModel = new Message();
$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_email_reply'])) {
    try {
        $senderName = trim($_POST['sender_name']);
        $senderEmail = trim($_POST['sender_email']);
        $subject = trim($_POST['subject']);
        $messageContent = trim($_POST['message_content']);
        $category = $_POST['category'] ?? 'general';
        
        if (empty($senderName) || empty($senderEmail) || empty($subject) || empty($messageContent)) {
            throw new Exception('All fields are required.');
        }
        
        if (!filter_var($senderEmail, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please enter a valid email address.');
        }
        
        // Prepare message data
        $messageData = [
            'sender_name' => $senderName,
            'sender_email' => $senderEmail,
            'subject' => $subject,
            'message_content' => $messageContent,
            'message_type' => 'incoming'
        ];
        
        // Add category if database supports it
        try {
            $checkColumn = $messageModel->getDb()->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
            $checkColumn->execute();
            if ($checkColumn->fetch()) {
                $messageData['message_category'] = $category;
                $messageData['is_reply'] = true;
            }
        } catch (Exception $e) {
            // Column doesn't exist, continue without it
        }
        
        $messageId = $messageModel->create($messageData);
        
        if ($messageId) {
            $message = 'Email reply added successfully to the messages system.';
            $messageType = 'success';
        } else {
            $message = 'Failed to add email reply. Please try again.';
            $messageType = 'error';
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Reply Manager - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-gray-900">Email Reply Manager</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <a href="messages.php" class="text-gray-600 hover:text-gray-900">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Messages
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
            
            <!-- Message Display -->
            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $messageType === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
                    <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- Instructions -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                <h2 class="text-lg font-semibold text-blue-900 mb-3">
                    <i class="fas fa-info-circle mr-2"></i>How to Add Email Replies
                </h2>
                <div class="text-blue-800 space-y-2">
                    <p><strong>Step 1:</strong> Check your email accounts (<EMAIL>, <EMAIL>)</p>
                    <p><strong>Step 2:</strong> Copy the customer's reply details into the form below</p>
                    <p><strong>Step 3:</strong> Select the appropriate category (Contact or Quote)</p>
                    <p><strong>Step 4:</strong> Click "Add Email Reply" to save it to your messages system</p>
                </div>
            </div>

            <!-- Email Reply Form -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Add Email Reply</h3>
                    <p class="text-sm text-gray-600">Manually add customer email replies to your messages system</p>
                </div>
                
                <form method="POST" class="p-6 space-y-6">
                    <?php echo csrf_field(); ?>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="sender_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="sender_name" 
                                   name="sender_name" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                   placeholder="John Doe">
                        </div>
                        
                        <div>
                            <label for="sender_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Customer Email <span class="text-red-500">*</span>
                            </label>
                            <input type="email" 
                                   id="sender_email" 
                                   name="sender_email" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="md:col-span-2">
                            <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                                Email Subject <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="subject" 
                                   name="subject" 
                                   required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                   placeholder="Re: Your Travel Quote - QT2024001">
                        </div>
                        
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                                Category
                            </label>
                            <select id="category" 
                                    name="category"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                                <option value="quote">Quote/Booking</option>
                                <option value="contact">Contact/General</option>
                                <option value="general">General</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="message_content" class="block text-sm font-medium text-gray-700 mb-2">
                            Email Content <span class="text-red-500">*</span>
                        </label>
                        <textarea id="message_content" 
                                  name="message_content" 
                                  rows="8" 
                                  required
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
                                  placeholder="Paste the customer's email content here..."></textarea>
                        <p class="text-xs text-gray-500 mt-1">Copy and paste the customer's email reply content</p>
                    </div>
                    
                    <div class="flex justify-end space-x-4">
                        <a href="messages.php" 
                           class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition duration-300">
                            Cancel
                        </a>
                        <button type="submit" 
                                name="add_email_reply"
                                class="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition duration-300">
                            <i class="fas fa-plus mr-2"></i>Add Email Reply
                        </button>
                    </div>
                </form>
            </div>

            <!-- Automatic Email Fetching Info -->
            <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-yellow-900 mb-3">
                    <i class="fas fa-robot mr-2"></i>Want Automatic Email Fetching?
                </h3>
                <div class="text-yellow-800 space-y-2">
                    <p>For automatic email fetching, you can:</p>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li><strong>Use IMAP Fetcher:</strong> <a href="email-fetcher.php" class="underline">email-fetcher.php</a> (requires IMAP extension)</li>
                        <li><strong>Set up Email Forwarding:</strong> Forward emails to a processing script</li>
                        <li><strong>Use Webhooks:</strong> If your email provider supports them</li>
                    </ul>
                    <p class="text-sm">This manual method is perfect for occasional use or when automatic methods aren't available.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
