<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/models.php';

/**
 * Quote Model for handling quote requests
 */
class Quote extends BaseModel {
    protected $table = 'quotes';
    
    protected function getPrimaryKey() {
        return 'quote_id';
    }
    
    /**
     * Generate unique quote reference
     */
    private function generateQuoteReference() {
        do {
            $reference = 'QT' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $exists = $this->findByReference($reference);
        } while ($exists);
        
        return $reference;
    }
    
    /**
     * Find quote by reference
     */
    public function findByReference($reference) {
        $sql = "SELECT * FROM {$this->table} WHERE quote_reference = :reference";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':reference', $reference);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Find quote by ID
     */
    public function findById($quoteId) {
        $sql = "SELECT * FROM {$this->table} WHERE quote_id = :quote_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Create new quote
     */
    public function create($data) {
        $sql = "INSERT INTO quotes (quote_reference, customer_name, customer_email, customer_phone,
                customer_country, travel_date, number_of_adults, number_of_children,
                special_requirements, quote_status)
                VALUES (:quote_reference, :customer_name, :customer_email, :customer_phone,
                :customer_country, :travel_date, :number_of_adults, :number_of_children,
                :special_requirements, :quote_status)";

        $stmt = $this->db->prepare($sql);

        // Extract values to variables for bindParam
        $quoteReference = $data['quote_reference'];
        $customerName = $data['customer_name'];
        $customerEmail = $data['customer_email'];
        $customerPhone = $data['customer_phone'];
        $customerCountry = $data['customer_country'] ?? '';
        $travelDate = $data['travel_date'];
        $numberOfAdults = $data['number_of_adults'];
        $numberOfChildren = $data['number_of_children'];
        $specialRequirements = $data['special_requirements'] ?? '';
        $quoteStatus = $data['quote_status'] ?? 'pending';

        $stmt->bindParam(':quote_reference', $quoteReference);
        $stmt->bindParam(':customer_name', $customerName);
        $stmt->bindParam(':customer_email', $customerEmail);
        $stmt->bindParam(':customer_phone', $customerPhone);
        $stmt->bindParam(':customer_country', $customerCountry);
        $stmt->bindParam(':travel_date', $travelDate);
        $stmt->bindParam(':number_of_adults', $numberOfAdults);
        $stmt->bindParam(':number_of_children', $numberOfChildren);
        $stmt->bindParam(':special_requirements', $specialRequirements);
        $stmt->bindParam(':quote_status', $quoteStatus);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    /**
     * Create new quote with packages
     */
    public function createQuoteWithPackages($quoteData, $packageIds = []) {
        try {
            $this->db->beginTransaction();
            
            // Generate quote reference
            $quoteData['quote_reference'] = $this->generateQuoteReference();
            
            // Create quote
            $quoteId = $this->create($quoteData);
            
            if ($quoteId && !empty($packageIds)) {
                // Add packages to quote
                $this->addPackagesToQuote($quoteId, $packageIds);
            }
            
            $this->db->commit();
            return $quoteId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Add packages to quote
     */
    public function addPackagesToQuote($quoteId, $packageIds) {
        $sql = "INSERT INTO quote_packages (quote_id, tour_package_id) VALUES (:quote_id, :package_id)";
        $stmt = $this->db->prepare($sql);
        
        foreach ($packageIds as $packageId) {
            $stmt->bindParam(':quote_id', $quoteId);
            $stmt->bindParam(':package_id', $packageId);
            $stmt->execute();
        }
    }
    
    /**
     * Get quote with packages
     */
    public function findWithPackages($quoteId) {
        $sql = "SELECT q.*, 
                       GROUP_CONCAT(tp.name SEPARATOR ', ') as package_names,
                       GROUP_CONCAT(tp.tour_package_id) as package_ids
                FROM quotes q
                LEFT JOIN quote_packages qp ON q.quote_id = qp.quote_id
                LEFT JOIN tour_packages tp ON qp.tour_package_id = tp.tour_package_id
                WHERE q.quote_id = :quote_id
                GROUP BY q.quote_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * Update quote status and amount
     */
    public function updateQuoteStatus($quoteId, $status, $amount = null, $expiresAt = null) {
        $sql = "UPDATE quotes SET quote_status = :status";
        $params = [':status' => $status, ':quote_id' => $quoteId];

        if ($amount !== null) {
            $sql .= ", quoted_amount = :amount, quoted_at = NOW()";
            $params[':amount'] = $amount;
        }

        if ($expiresAt !== null) {
            $sql .= ", expires_at = :expires_at";
            $params[':expires_at'] = $expiresAt;
        }

        $sql .= " WHERE quote_id = :quote_id";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Update quote amount and create version history
     */
    public function updateQuoteAmount($quoteId, $newAmount, $reason = null, $userId = null) {
        try {
            $this->db->beginTransaction();

            // Get current quote details
            $currentQuote = $this->findById($quoteId);
            if (!$currentQuote) {
                throw new Exception("Quote not found");
            }

            $previousAmount = $currentQuote['quoted_amount'];
            $newVersion = ($currentQuote['quote_version'] ?? 1) + 1;

            // Update quote with new amount and version
            $sql = "UPDATE quotes SET
                        quoted_amount = :amount,
                        quote_version = :version,
                        quoted_at = NOW()
                    WHERE quote_id = :quote_id";

            $stmt = $this->db->prepare($sql);
            $stmt->bindParam(':amount', $newAmount);
            $stmt->bindParam(':version', $newVersion);
            $stmt->bindParam(':quote_id', $quoteId);
            $stmt->execute();

            // Create history record
            $historySql = "INSERT INTO quote_history
                          (quote_id, quote_version, previous_amount, new_amount, change_reason, changed_by_user_id)
                          VALUES (:quote_id, :version, :previous_amount, :new_amount, :reason, :user_id)";

            $historyStmt = $this->db->prepare($historySql);
            $historyStmt->bindParam(':quote_id', $quoteId);
            $historyStmt->bindParam(':version', $newVersion);
            $historyStmt->bindParam(':previous_amount', $previousAmount);
            $historyStmt->bindParam(':new_amount', $newAmount);
            $historyStmt->bindParam(':reason', $reason);
            $historyStmt->bindParam(':user_id', $userId);
            $historyStmt->execute();

            // Recalculate payment status
            $this->updatePaymentStatus($quoteId);

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Update payment status based on payments
     */
    public function updatePaymentStatus($quoteId) {
        // Get quote details
        $quote = $this->findById($quoteId);
        if (!$quote || !$quote['quoted_amount']) {
            return false;
        }

        // Calculate total paid
        $paymentModel = new Payment();
        $totalPaid = $paymentModel->getTotalPaidForQuote($quoteId);

        // Determine payment status
        $paymentStatus = 'unpaid';
        $quoteStatus = $quote['quote_status'];

        if ($totalPaid >= $quote['quoted_amount']) {
            $paymentStatus = 'paid';
            if ($quoteStatus === 'quoted' || $quoteStatus === 'accepted' || $quoteStatus === 'partially_paid') {
                $quoteStatus = 'paid';
            }
        } elseif ($totalPaid > 0) {
            $paymentStatus = 'partial';
            if ($quoteStatus === 'quoted' || $quoteStatus === 'accepted') {
                $quoteStatus = 'partially_paid';
            }
        }

        // Update quote
        $sql = "UPDATE quotes SET
                    total_paid = :total_paid,
                    payment_status = :payment_status,
                    quote_status = :quote_status,
                    last_payment_date = (
                        SELECT MAX(payment_date)
                        FROM payments
                        WHERE quote_id = :quote_id AND payment_status = 'completed'
                    )
                WHERE quote_id = :quote_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':total_paid', $totalPaid);
        $stmt->bindParam(':payment_status', $paymentStatus);
        $stmt->bindParam(':quote_status', $quoteStatus);
        $stmt->bindParam(':quote_id', $quoteId);

        return $stmt->execute();
    }

    /**
     * Get quote with payment summary
     */
    public function getQuoteWithPaymentSummary($quoteId) {
        $sql = "SELECT q.*,
                       COALESCE(q.total_paid, 0) as total_paid,
                       (q.quoted_amount - COALESCE(q.total_paid, 0)) as balance_remaining,
                       COUNT(p.payment_id) as payment_count,
                       MAX(p.payment_date) as last_payment_date
                FROM quotes q
                LEFT JOIN payments p ON q.quote_id = p.quote_id AND p.payment_status = 'completed'
                WHERE q.quote_id = :quote_id
                GROUP BY q.quote_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Get quote history
     */
    public function getQuoteHistory($quoteId) {
        $sql = "SELECT qh.*, u.username as changed_by_username
                FROM quote_history qh
                LEFT JOIN users u ON qh.changed_by_user_id = u.user_id
                WHERE qh.quote_id = :quote_id
                ORDER BY qh.changed_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Find quotes by customer email
     */
    public function findByEmail($email) {
        $sql = "SELECT * FROM quotes WHERE customer_email = :email ORDER BY created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':email', $email);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Get all quotes with pagination
     */
    public function findAllWithDetails($limit = 20, $offset = 0, $filters = []) {
        $sql = "SELECT q.*,
                       GROUP_CONCAT(tp.name SEPARATOR ', ') as package_names
                FROM quotes q
                LEFT JOIN quote_packages qp ON q.quote_id = qp.quote_id
                LEFT JOIN tour_packages tp ON qp.tour_package_id = tp.tour_package_id";

        $whereConditions = [];
        $params = [];

        // Status filter
        if (!empty($filters['status'])) {
            $whereConditions[] = "q.quote_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Search filter (customer name or quote reference)
        if (!empty($filters['search'])) {
            $whereConditions[] = "(q.customer_name LIKE :search1 OR q.quote_reference LIKE :search2)";
            $params[':search1'] = '%' . $filters['search'] . '%';
            $params[':search2'] = '%' . $filters['search'] . '%';
        }

        // Date from filter
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(q.created_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        // Date to filter
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(q.created_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        // Add WHERE clause if there are conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $sql .= " GROUP BY q.quote_id ORDER BY q.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);

        // Add pagination parameters to the params array
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;

        // Bind all parameters at once
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Count quotes with filters
     */
    public function countWithFilters($filters = []) {
        $sql = "SELECT COUNT(DISTINCT q.quote_id) as total
                FROM quotes q
                LEFT JOIN quote_packages qp ON q.quote_id = qp.quote_id
                LEFT JOIN tour_packages tp ON qp.tour_package_id = tp.tour_package_id";

        $whereConditions = [];
        $params = [];

        // Status filter
        if (!empty($filters['status'])) {
            $whereConditions[] = "q.quote_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Search filter (customer name or quote reference)
        if (!empty($filters['search'])) {
            $whereConditions[] = "(q.customer_name LIKE :search1 OR q.quote_reference LIKE :search2)";
            $params[':search1'] = '%' . $filters['search'] . '%';
            $params[':search2'] = '%' . $filters['search'] . '%';
        }

        // Date from filter
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(q.created_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        // Date to filter
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(q.created_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        // Add WHERE clause if there are conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $stmt = $this->db->prepare($sql);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    }

    /**
     * Count total quotes
     */
    public function count($status = null) {
        $sql = "SELECT COUNT(*) as total FROM quotes";
        if ($status) {
            $sql .= " WHERE quote_status = :status";
        }

        $stmt = $this->db->prepare($sql);
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    /**
     * Find all quotes
     */
    public function findAll($limit = null, $offset = 0) {
        $sql = "SELECT * FROM quotes ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
        }

        $stmt = $this->db->prepare($sql);
        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Get recent quotes for testing
     */
    public function getRecentQuotes($limit = 10) {
        $sql = "SELECT * FROM quotes ORDER BY created_at DESC LIMIT :limit";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->execute();
        return $stmt->fetchAll();
    }

}

/**
 * Booking Model for handling confirmed bookings
 */
class Booking extends BaseModel {
    protected $table = 'bookings';
    
    protected function getPrimaryKey() {
        return 'booking_id';
    }
    
    /**
     * Generate unique booking reference
     */
    private function generateBookingReference() {
        do {
            $reference = 'BK' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $exists = $this->findByReference($reference);
        } while ($exists);
        
        return $reference;
    }
    
    /**
     * Find booking by reference
     */
    public function findByReference($reference) {
        $sql = "SELECT * FROM {$this->table} WHERE booking_reference = :reference";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':reference', $reference);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Find booking by quote ID
     */
    public function findByQuoteId($quoteId) {
        $sql = "SELECT * FROM {$this->table} WHERE quote_id = :quote_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Get booking with payment summary by quote ID
     */
    public function getBookingWithPaymentSummary($quoteId) {
        $sql = "SELECT
                    b.*,
                    COALESCE(SUM(CASE WHEN p.payment_status = 'completed' THEN p.amount ELSE 0 END), 0) as total_paid,
                    COALESCE(SUM(CASE WHEN p.payment_status IN ('pending', 'processing') THEN p.amount ELSE 0 END), 0) as pending_amount,
                    COUNT(p.payment_id) as payment_count
                FROM {$this->table} b
                LEFT JOIN payments p ON b.booking_id = p.booking_id
                WHERE b.quote_id = :quote_id
                GROUP BY b.booking_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Create new booking
     */
    public function create($data) {
        $sql = "INSERT INTO bookings (booking_reference, quote_id, customer_name, customer_email,
                customer_phone, customer_country, travel_date, number_of_adults, number_of_children,
                total_amount, deposit_amount, balance_amount, special_requirements, booking_status, payment_status)
                VALUES (:booking_reference, :quote_id, :customer_name, :customer_email,
                :customer_phone, :customer_country, :travel_date, :number_of_adults, :number_of_children,
                :total_amount, :deposit_amount, :balance_amount, :special_requirements, :booking_status, :payment_status)";

        $stmt = $this->db->prepare($sql);

        // Extract values to variables for bindParam
        $bookingReference = $data['booking_reference'];
        $quoteId = $data['quote_id'];
        $customerName = $data['customer_name'];
        $customerEmail = $data['customer_email'];
        $customerPhone = $data['customer_phone'];
        $customerCountry = $data['customer_country'];
        $travelDate = $data['travel_date'];
        $numberOfAdults = $data['number_of_adults'];
        $numberOfChildren = $data['number_of_children'];
        $totalAmount = $data['total_amount'];
        $depositAmount = $data['deposit_amount'];
        $balanceAmount = $data['balance_amount'];
        $specialRequirements = $data['special_requirements'];
        $bookingStatus = $data['booking_status'] ?? 'pending';
        $paymentStatus = $data['payment_status'] ?? 'pending';

        $stmt->bindParam(':booking_reference', $bookingReference);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->bindParam(':customer_name', $customerName);
        $stmt->bindParam(':customer_email', $customerEmail);
        $stmt->bindParam(':customer_phone', $customerPhone);
        $stmt->bindParam(':customer_country', $customerCountry);
        $stmt->bindParam(':travel_date', $travelDate);
        $stmt->bindParam(':number_of_adults', $numberOfAdults);
        $stmt->bindParam(':number_of_children', $numberOfChildren);
        $stmt->bindParam(':total_amount', $totalAmount);
        $stmt->bindParam(':deposit_amount', $depositAmount);
        $stmt->bindParam(':balance_amount', $balanceAmount);
        $stmt->bindParam(':special_requirements', $specialRequirements);
        $stmt->bindParam(':booking_status', $bookingStatus);
        $stmt->bindParam(':payment_status', $paymentStatus);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }
    
    /**
     * Create booking from quote
     */
    public function createFromQuote($quoteId, $additionalData = []) {
        try {
            $this->db->beginTransaction();
            
            // Get quote details
            $quoteModel = new Quote();
            $quote = $quoteModel->findWithPackages($quoteId);
            
            if (!$quote || !in_array($quote['quote_status'], ['quoted', 'accepted'])) {
                throw new Exception('Quote not found or not ready for booking');
            }
            
            // Prepare booking data
            $bookingData = array_merge([
                'booking_reference' => $this->generateBookingReference(),
                'quote_id' => $quoteId,
                'customer_name' => $quote['customer_name'],
                'customer_email' => $quote['customer_email'],
                'customer_phone' => $quote['customer_phone'],
                'customer_country' => $quote['customer_country'],
                'travel_date' => $quote['travel_date'],
                'number_of_adults' => $quote['number_of_adults'],
                'number_of_children' => $quote['number_of_children'],
                'total_amount' => $quote['quoted_amount'],
                'deposit_amount' => $quote['quoted_amount'] * 0.3, // 30% deposit
                'special_requirements' => $quote['special_requirements']
            ], $additionalData);
            
            $bookingData['balance_amount'] = $bookingData['total_amount'] - $bookingData['deposit_amount'];
            
            // Create booking
            $bookingId = $this->create($bookingData);
            
            if ($bookingId && !empty($quote['package_ids'])) {
                // Add packages to booking
                $packageIds = explode(',', $quote['package_ids']);
                $this->addPackagesToBooking($bookingId, $packageIds, $quote['quoted_amount']);
            }
            
            $this->db->commit();
            return $bookingId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Create direct booking (without quote)
     */
    public function createDirectBooking($bookingData, $packageIds = []) {
        try {
            $this->db->beginTransaction();
            
            // Generate booking reference
            $bookingData['booking_reference'] = $this->generateBookingReference();
            
            // Calculate deposit (30% of total)
            $bookingData['deposit_amount'] = $bookingData['total_amount'] * 0.3;
            $bookingData['balance_amount'] = $bookingData['total_amount'] - $bookingData['deposit_amount'];
            
            // Create booking
            $bookingId = $this->create($bookingData);
            
            if ($bookingId && !empty($packageIds)) {
                // Add packages to booking
                $this->addPackagesToBooking($bookingId, $packageIds, $bookingData['total_amount']);
            }
            
            $this->db->commit();
            return $bookingId;
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Add packages to booking
     */
    public function addPackagesToBooking($bookingId, $packageIds, $totalAmount) {
        $sql = "INSERT INTO booking_packages (booking_id, tour_package_id, package_price) 
                VALUES (:booking_id, :package_id, :price)";
        $stmt = $this->db->prepare($sql);
        
        $pricePerPackage = $totalAmount / count($packageIds);
        
        foreach ($packageIds as $packageId) {
            $stmt->bindParam(':booking_id', $bookingId);
            $stmt->bindParam(':package_id', $packageId);
            $stmt->bindParam(':price', $pricePerPackage);
            $stmt->execute();
        }
    }
    
    /**
     * Get booking with packages and payments
     */
    public function findWithDetails($bookingId) {
        $sql = "SELECT b.*, 
                       GROUP_CONCAT(tp.name SEPARATOR ', ') as package_names,
                       GROUP_CONCAT(tp.tour_package_id) as package_ids,
                       SUM(CASE WHEN p.payment_status = 'completed' THEN p.amount ELSE 0 END) as paid_amount
                FROM bookings b
                LEFT JOIN booking_packages bp ON b.booking_id = bp.booking_id
                LEFT JOIN tour_packages tp ON bp.tour_package_id = tp.tour_package_id
                LEFT JOIN payments p ON b.booking_id = p.booking_id
                WHERE b.booking_id = :booking_id
                GROUP BY b.booking_id";
        
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':booking_id', $bookingId);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * Update booking status
     */
    public function updateStatus($bookingId, $bookingStatus = null, $paymentStatus = null) {
        $sql = "UPDATE bookings SET ";
        $params = [':booking_id' => $bookingId];
        $updates = [];
        
        if ($bookingStatus !== null) {
            $updates[] = "booking_status = :booking_status";
            $params[':booking_status'] = $bookingStatus;
        }
        
        if ($paymentStatus !== null) {
            $updates[] = "payment_status = :payment_status";
            $params[':payment_status'] = $paymentStatus;
        }
        
        if (empty($updates)) {
            return false;
        }
        
        $sql .= implode(', ', $updates) . " WHERE booking_id = :booking_id";
        
        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }
    
    /**
     * Get all bookings with details and filtering
     */
    public function findAllWithDetails($limit = 20, $offset = 0, $filters = []) {
        $sql = "SELECT b.*,
                       GROUP_CONCAT(tp.name SEPARATOR ', ') as package_names,
                       SUM(CASE WHEN p.payment_status = 'completed' THEN p.amount ELSE 0 END) as paid_amount
                FROM bookings b
                LEFT JOIN booking_packages bp ON b.booking_id = bp.booking_id
                LEFT JOIN tour_packages tp ON bp.tour_package_id = tp.tour_package_id
                LEFT JOIN payments p ON b.booking_id = p.booking_id";

        $whereConditions = [];
        $params = [];

        // Status filter
        if (!empty($filters['status'])) {
            $whereConditions[] = "b.booking_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Search filter (customer name or booking reference)
        if (!empty($filters['search'])) {
            $whereConditions[] = "(b.customer_name LIKE :search1 OR b.booking_reference LIKE :search2)";
            $params[':search1'] = '%' . $filters['search'] . '%';
            $params[':search2'] = '%' . $filters['search'] . '%';
        }

        // Date from filter
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(b.created_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        // Date to filter
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(b.created_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        // Add WHERE clause if there are conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $sql .= " GROUP BY b.booking_id ORDER BY b.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);

        // Add pagination parameters to the params array
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;

        // Bind all parameters at once
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Count bookings with filters
     */
    public function countWithFilters($filters = []) {
        $sql = "SELECT COUNT(DISTINCT b.booking_id) as total
                FROM bookings b
                LEFT JOIN booking_packages bp ON b.booking_id = bp.booking_id
                LEFT JOIN tour_packages tp ON bp.tour_package_id = tp.tour_package_id
                LEFT JOIN payments p ON b.booking_id = p.booking_id";

        $whereConditions = [];
        $params = [];

        // Status filter
        if (!empty($filters['status'])) {
            $whereConditions[] = "b.booking_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Search filter (customer name or booking reference)
        if (!empty($filters['search'])) {
            $whereConditions[] = "(b.customer_name LIKE :search1 OR b.booking_reference LIKE :search2)";
            $params[':search1'] = '%' . $filters['search'] . '%';
            $params[':search2'] = '%' . $filters['search'] . '%';
        }

        // Date from filter
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(b.created_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        // Date to filter
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(b.created_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        // Add WHERE clause if there are conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $stmt = $this->db->prepare($sql);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    }

    /**
     * Count total bookings
     */
    public function count($status = null) {
        $sql = "SELECT COUNT(*) as total FROM bookings";
        if ($status) {
            $sql .= " WHERE booking_status = :status";
        }

        $stmt = $this->db->prepare($sql);
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    /**
     * Find all bookings
     */
    public function findAll($limit = null, $offset = 0) {
        $sql = "SELECT * FROM bookings ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
        }

        $stmt = $this->db->prepare($sql);
        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }
}

/**
 * Payment Model for handling payment transactions
 */
class Payment extends BaseModel {
    protected $table = 'payments';

    protected function getPrimaryKey() {
        return 'payment_id';
    }

    /**
     * Generate unique payment reference
     */
    private function generatePaymentReference() {
        do {
            $reference = 'PAY' . date('Y') . str_pad(rand(1, 9999), 4, '0', STR_PAD_LEFT);
            $exists = $this->findByReference($reference);
        } while ($exists);

        return $reference;
    }

    /**
     * Find payment by reference
     */
    public function findByReference($reference) {
        $sql = "SELECT * FROM {$this->table} WHERE payment_reference = :reference";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':reference', $reference);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Get total paid amount for a quote
     */
    public function getTotalPaidForQuote($quoteId) {
        $sql = "SELECT COALESCE(SUM(amount), 0) as total_paid
                FROM {$this->table}
                WHERE quote_id = :quote_id AND payment_status = 'completed'";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId, PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch();
        return floatval($result['total_paid'] ?? 0);
    }

    /**
     * Update payment with Pesapal tracking ID
     */
    public function updatePesapalTrackingId($paymentId, $trackingId) {
        $sql = "UPDATE {$this->table} SET pesapal_tracking_id = :tracking_id WHERE payment_id = :payment_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tracking_id', $trackingId);
        $stmt->bindParam(':payment_id', $paymentId, PDO::PARAM_INT);
        return $stmt->execute();
    }

    /**
     * Find payment by Pesapal tracking ID
     */
    public function findByPesapalTrackingId($trackingId) {
        $sql = "SELECT * FROM {$this->table} WHERE pesapal_tracking_id = :tracking_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tracking_id', $trackingId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Create new payment
     */
    public function create($data) {
        $sql = "INSERT INTO payments (payment_reference, booking_id, payment_type, amount,
                currency, payment_status)
                VALUES (:payment_reference, :booking_id, :payment_type, :amount,
                :currency, :payment_status)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':payment_reference', $data['payment_reference']);
        $stmt->bindParam(':booking_id', $data['booking_id']);
        $stmt->bindParam(':payment_type', $data['payment_type']);
        $stmt->bindParam(':amount', $data['amount']);

        $currency = $data['currency'] ?? 'USD';
        $paymentStatus = $data['payment_status'] ?? 'pending';
        $stmt->bindParam(':currency', $currency);
        $stmt->bindParam(':payment_status', $paymentStatus);

        if ($stmt->execute()) {
            return $this->db->lastInsertId();
        }
        return false;
    }

    /**
     * Create new payment record
     */
    public function createPayment($bookingId, $amount, $paymentType, $currency = 'USD') {
        $paymentData = [
            'payment_reference' => $this->generatePaymentReference(),
            'booking_id' => $bookingId,
            'payment_type' => $paymentType,
            'amount' => $amount,
            'currency' => $currency,
            'payment_status' => 'pending'
        ];

        return $this->create($paymentData);
    }

    /**
     * Create payment from quote (enhanced system)
     */
    public function createFromQuote($data) {
        // Get quote details for customer information
        $quoteModel = new Quote();
        $quote = $quoteModel->findById($data['quote_id']);

        if (!$quote) {
            throw new Exception("Quote not found");
        }

        // Calculate remaining balance
        $totalPaid = $this->getTotalPaidForQuote($data['quote_id']);
        $remainingBalance = max(0, $quote['quoted_amount'] - $totalPaid - $data['amount']);

        $sql = "INSERT INTO payments (
                    payment_reference, quote_id, quote_version, payment_type, amount,
                    remaining_balance, currency, payment_status, pesapal_merchant_reference,
                    customer_name, customer_email, customer_phone
                ) VALUES (
                    :payment_reference, :quote_id, :quote_version, :payment_type, :amount,
                    :remaining_balance, :currency, :payment_status, :merchant_reference,
                    :customer_name, :customer_email, :customer_phone
                )";

        $stmt = $this->db->prepare($sql);

        $paymentReference = $this->generatePaymentReference();
        $currency = $data['currency'] ?? 'USD';
        $paymentStatus = $data['payment_status'] ?? 'pending';
        $quoteVersion = $quote['quote_version'] ?? 1;

        $stmt->bindParam(':payment_reference', $paymentReference);
        $stmt->bindParam(':quote_id', $data['quote_id']);
        $stmt->bindParam(':quote_version', $quoteVersion);
        $stmt->bindParam(':payment_type', $data['payment_type']);
        $stmt->bindParam(':amount', $data['amount']);
        $stmt->bindParam(':remaining_balance', $remainingBalance);
        $stmt->bindParam(':currency', $currency);
        $stmt->bindParam(':payment_status', $paymentStatus);
        $stmt->bindParam(':merchant_reference', $data['merchant_reference']);
        $stmt->bindParam(':customer_name', $quote['customer_name']);
        $stmt->bindParam(':customer_email', $quote['customer_email']);
        $stmt->bindParam(':customer_phone', $quote['customer_phone']);

        if ($stmt->execute()) {
            $paymentId = $this->db->lastInsertId();

            // Update quote payment status
            $quoteModel->updatePaymentStatus($data['quote_id']);

            return $paymentId;
        }
        return false;
    }

    /**
     * Create partial payment
     */
    public function createPartialPayment($quoteId, $amount, $merchantReference) {
        return $this->createFromQuote([
            'quote_id' => $quoteId,
            'payment_type' => 'partial',
            'amount' => $amount,
            'merchant_reference' => $merchantReference
        ]);
    }

    /**
     * Create balance payment
     */
    public function createBalancePayment($quoteId, $merchantReference) {
        $quoteModel = new Quote();
        $quote = $quoteModel->getQuoteWithPaymentSummary($quoteId);

        if (!$quote || $quote['balance_remaining'] <= 0) {
            throw new Exception("No balance remaining for this quote");
        }

        return $this->createFromQuote([
            'quote_id' => $quoteId,
            'payment_type' => 'balance',
            'amount' => $quote['balance_remaining'],
            'merchant_reference' => $merchantReference
        ]);
    }

    /**
     * Update payment with Pesapal details
     */
    public function updatePesapalDetails($paymentId, $pesapalData) {
        $sql = "UPDATE payments SET
                pesapal_tracking_id = :tracking_id,
                pesapal_merchant_reference = :merchant_ref,
                pesapal_response = :response,
                updated_at = NOW()
                WHERE payment_id = :payment_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':tracking_id', $pesapalData['tracking_id']);
        $stmt->bindParam(':merchant_ref', $pesapalData['merchant_reference']);

        $responseJson = json_encode($pesapalData);
        $stmt->bindParam(':response', $responseJson);
        $stmt->bindParam(':payment_id', $paymentId);

        return $stmt->execute();
    }

    /**
     * Update payment status
     */
    public function updatePaymentStatus($paymentId, $status, $pesapalStatus = null, $paymentMethod = null) {
        $sql = "UPDATE payments SET payment_status = :status";
        $params = [':status' => $status, ':payment_id' => $paymentId];

        if ($pesapalStatus !== null) {
            $sql .= ", pesapal_status = :pesapal_status";
            $params[':pesapal_status'] = $pesapalStatus;
        }

        if ($paymentMethod !== null) {
            $sql .= ", payment_method = :payment_method";
            $params[':payment_method'] = $paymentMethod;
        }

        if ($status === 'completed') {
            $sql .= ", payment_date = NOW()";
        }

        $sql .= " WHERE payment_id = :payment_id";

        $stmt = $this->db->prepare($sql);
        return $stmt->execute($params);
    }

    /**
     * Get payments for a booking
     */
    public function findByBookingId($bookingId) {
        $sql = "SELECT * FROM {$this->table} WHERE booking_id = :booking_id ORDER BY created_at DESC";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':booking_id', $bookingId);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Get payment summary for a booking
     */
    public function getPaymentSummary($bookingId) {
        $sql = "SELECT
                    COUNT(*) as total_payments,
                    COALESCE(SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END), 0) as total_paid,
                    COALESCE(SUM(CASE WHEN payment_status IN ('pending', 'processing') THEN amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN payment_status = 'failed' THEN amount ELSE 0 END), 0) as failed_amount,
                    MAX(CASE WHEN payment_status = 'completed' THEN payment_date END) as last_payment_date
                FROM {$this->table}
                WHERE booking_id = :booking_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':booking_id', $bookingId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Get payment with booking details
     */
    public function findWithBookingDetails($paymentId) {
        $sql = "SELECT p.*, b.booking_reference, b.customer_name, b.customer_email, b.total_amount as booking_total
                FROM payments p
                JOIN bookings b ON p.booking_id = b.booking_id
                WHERE p.payment_id = :payment_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':payment_id', $paymentId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Calculate total paid amount for a booking
     */
    public function getTotalPaidForBooking($bookingId) {
        $sql = "SELECT SUM(amount) as total_paid
                FROM payments
                WHERE booking_id = :booking_id AND payment_status = 'completed'";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':booking_id', $bookingId);
        $stmt->execute();
        $result = $stmt->fetch();

        return $result['total_paid'] ?? 0;
    }

    /**
     * Get all payments with details and filtering
     */
    public function findAllWithDetails($limit = 20, $offset = 0, $filters = []) {
        // Check if quote_id column exists in payments table
        $hasQuoteId = $this->checkColumnExists('payments', 'quote_id');

        if ($hasQuoteId) {
            // New schema with quote_id support
            $sql = "SELECT p.*, q.quote_reference, q.customer_name, q.customer_email, q.customer_phone
                    FROM payments p
                    LEFT JOIN quotes q ON p.quote_id = q.quote_id";
        } else {
            // Original schema with booking_id only
            $sql = "SELECT p.*, b.booking_reference, b.customer_name, b.customer_email, b.customer_phone
                    FROM payments p
                    LEFT JOIN bookings b ON p.booking_id = b.booking_id";
        }

        $whereConditions = [];
        $params = [];

        // Status filter
        if (!empty($filters['status'])) {
            $whereConditions[] = "p.payment_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Search filter (customer name, reference, or payment reference)
        if (!empty($filters['search'])) {
            if ($hasQuoteId) {
                // Search in quotes table
                $whereConditions[] = "(q.customer_name LIKE :search1 OR q.quote_reference LIKE :search2 OR p.payment_reference LIKE :search3 OR q.customer_email LIKE :search4)";
                $params[':search1'] = '%' . $filters['search'] . '%';
                $params[':search2'] = '%' . $filters['search'] . '%';
                $params[':search3'] = '%' . $filters['search'] . '%';
                $params[':search4'] = '%' . $filters['search'] . '%';
            } else {
                // Search in bookings table
                $whereConditions[] = "(b.customer_name LIKE :search1 OR b.booking_reference LIKE :search2 OR p.payment_reference LIKE :search3 OR b.customer_email LIKE :search4)";
                $params[':search1'] = '%' . $filters['search'] . '%';
                $params[':search2'] = '%' . $filters['search'] . '%';
                $params[':search3'] = '%' . $filters['search'] . '%';
                $params[':search4'] = '%' . $filters['search'] . '%';
            }
        }

        // Date from filter
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(p.created_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        // Date to filter
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(p.created_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        // Add WHERE clause if there are conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $sql .= " ORDER BY p.created_at DESC LIMIT :limit OFFSET :offset";

        $stmt = $this->db->prepare($sql);

        // Add pagination parameters to the params array
        $params[':limit'] = $limit;
        $params[':offset'] = $offset;

        // Bind all parameters at once
        foreach ($params as $key => $value) {
            if ($key === ':limit' || $key === ':offset') {
                $stmt->bindValue($key, $value, PDO::PARAM_INT);
            } else {
                $stmt->bindValue($key, $value);
            }
        }

        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Count payments with filters
     */
    public function countWithFilters($filters = []) {
        // Check if quote_id column exists in payments table
        $hasQuoteId = $this->checkColumnExists('payments', 'quote_id');

        if ($hasQuoteId) {
            // New schema with quote_id support
            $sql = "SELECT COUNT(DISTINCT p.payment_id) as total
                    FROM payments p
                    LEFT JOIN quotes q ON p.quote_id = q.quote_id";
        } else {
            // Original schema with booking_id only
            $sql = "SELECT COUNT(DISTINCT p.payment_id) as total
                    FROM payments p
                    LEFT JOIN bookings b ON p.booking_id = b.booking_id";
        }

        $whereConditions = [];
        $params = [];

        // Status filter
        if (!empty($filters['status'])) {
            $whereConditions[] = "p.payment_status = :status";
            $params[':status'] = $filters['status'];
        }

        // Search filter (customer name, reference, or payment reference)
        if (!empty($filters['search'])) {
            if ($hasQuoteId) {
                // Search in quotes table
                $whereConditions[] = "(q.customer_name LIKE :search1 OR q.quote_reference LIKE :search2 OR p.payment_reference LIKE :search3 OR q.customer_email LIKE :search4)";
                $params[':search1'] = '%' . $filters['search'] . '%';
                $params[':search2'] = '%' . $filters['search'] . '%';
                $params[':search3'] = '%' . $filters['search'] . '%';
                $params[':search4'] = '%' . $filters['search'] . '%';
            } else {
                // Search in bookings table
                $whereConditions[] = "(b.customer_name LIKE :search1 OR b.booking_reference LIKE :search2 OR p.payment_reference LIKE :search3 OR b.customer_email LIKE :search4)";
                $params[':search1'] = '%' . $filters['search'] . '%';
                $params[':search2'] = '%' . $filters['search'] . '%';
                $params[':search3'] = '%' . $filters['search'] . '%';
                $params[':search4'] = '%' . $filters['search'] . '%';
            }
        }

        // Date from filter
        if (!empty($filters['date_from'])) {
            $whereConditions[] = "DATE(p.created_at) >= :date_from";
            $params[':date_from'] = $filters['date_from'];
        }

        // Date to filter
        if (!empty($filters['date_to'])) {
            $whereConditions[] = "DATE(p.created_at) <= :date_to";
            $params[':date_to'] = $filters['date_to'];
        }

        // Add WHERE clause if there are conditions
        if (!empty($whereConditions)) {
            $sql .= " WHERE " . implode(" AND ", $whereConditions);
        }

        $stmt = $this->db->prepare($sql);

        // Bind filter parameters
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }

        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'] ?? 0;
    }

    /**
     * Count total payments
     */
    public function count($status = null) {
        $sql = "SELECT COUNT(*) as total FROM payments";
        if ($status) {
            $sql .= " WHERE payment_status = :status";
        }

        $stmt = $this->db->prepare($sql);
        if ($status) {
            $stmt->bindParam(':status', $status);
        }
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['total'];
    }

    /**
     * Find all payments
     */
    public function findAll($limit = null, $offset = 0) {
        $sql = "SELECT * FROM payments ORDER BY created_at DESC";
        if ($limit) {
            $sql .= " LIMIT :limit OFFSET :offset";
        }

        $stmt = $this->db->prepare($sql);
        if ($limit) {
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        }
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Find payment by ID
     */
    public function findById($paymentId) {
        $sql = "SELECT * FROM payments WHERE payment_id = :payment_id";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':payment_id', $paymentId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Get all payments for a specific quote
     */
    public function getByQuoteId($quoteId) {
        $sql = "SELECT * FROM payments
                WHERE quote_id = :quote_id
                ORDER BY created_at DESC";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Generate payment link token
     */
    public function generatePaymentLink($quoteId, $amountDue, $linkType = 'full', $expiresInHours = 72) {
        // Generate secure token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiresInHours} hours"));

        $sql = "INSERT INTO payment_links (quote_id, link_token, amount_due, link_type, expires_at)
                VALUES (:quote_id, :token, :amount_due, :link_type, :expires_at)";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->bindParam(':token', $token);
        $stmt->bindParam(':amount_due', $amountDue);
        $stmt->bindParam(':link_type', $linkType);
        $stmt->bindParam(':expires_at', $expiresAt);

        if ($stmt->execute()) {
            return $token;
        }
        return false;
    }

    /**
     * Validate payment link token
     */
    public function validatePaymentLink($token) {
        $sql = "SELECT pl.*, q.quote_reference, q.customer_name, q.customer_email, q.quoted_amount, q.total_paid
                FROM payment_links pl
                JOIN quotes q ON pl.quote_id = q.quote_id
                WHERE pl.link_token = :token
                AND pl.expires_at > NOW()
                AND pl.used_at IS NULL";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':token', $token);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Mark payment link as used
     */
    public function markPaymentLinkUsed($token) {
        $sql = "UPDATE payment_links SET used_at = NOW() WHERE link_token = :token";
        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':token', $token);
        return $stmt->execute();
    }

    /**
     * Get payment summary for quote
     */
    public function getQuotePaymentSummary($quoteId) {
        $sql = "SELECT
                    COUNT(*) as total_payments,
                    COALESCE(SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END), 0) as total_paid,
                    COALESCE(SUM(CASE WHEN payment_status IN ('pending', 'processing') THEN amount ELSE 0 END), 0) as pending_amount,
                    COALESCE(SUM(CASE WHEN payment_status = 'failed' THEN amount ELSE 0 END), 0) as failed_amount,
                    MAX(CASE WHEN payment_status = 'completed' THEN payment_date END) as last_payment_date
                FROM payments
                WHERE quote_id = :quote_id";

        $stmt = $this->db->prepare($sql);
        $stmt->bindParam(':quote_id', $quoteId);
        $stmt->execute();
        return $stmt->fetch();
    }

    /**
     * Update payment status and recalculate quote totals
     */
    public function updatePaymentStatusWithQuoteUpdate($paymentId, $status, $pesapalStatus = null, $paymentMethod = null) {
        try {
            $this->db->beginTransaction();

            // Get payment details
            $payment = $this->findById($paymentId);
            if (!$payment) {
                throw new Exception("Payment not found");
            }

            // Update payment status
            $this->updatePaymentStatus($paymentId, $status, $pesapalStatus, $paymentMethod);

            // Update quote payment status if this is a quote payment
            if ($payment['quote_id']) {
                $quoteModel = new Quote();
                $quoteModel->updatePaymentStatus($payment['quote_id']);
            }

            $this->db->commit();
            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
}
