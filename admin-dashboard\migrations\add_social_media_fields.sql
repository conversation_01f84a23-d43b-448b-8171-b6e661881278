-- Migration: Add Social Media Fields to Contact Info Table
-- Run this script if you already have an existing contact_info table
-- and need to add the social media fields

-- Check if the columns don't exist before adding them
SET @sql = '';

-- Check and add facebook_url column
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'contact_info' 
AND column_name = 'facebook_url';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE contact_info ADD COLUMN facebook_url VARCHAR(255) AFTER map_embed_code;', 
    'SELECT "facebook_url column already exists" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add twitter_url column
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'contact_info' 
AND column_name = 'twitter_url';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE contact_info ADD COLUMN twitter_url VARCHAR(255) AFTER facebook_url;', 
    'SELECT "twitter_url column already exists" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add instagram_url column
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'contact_info' 
AND column_name = 'instagram_url';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE contact_info ADD COLUMN instagram_url VARCHAR(255) AFTER twitter_url;', 
    'SELECT "instagram_url column already exists" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add tiktok_url column
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'contact_info' 
AND column_name = 'tiktok_url';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE contact_info ADD COLUMN tiktok_url VARCHAR(255) AFTER instagram_url;', 
    'SELECT "tiktok_url column already exists" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add youtube_url column
SELECT COUNT(*) INTO @col_exists 
FROM information_schema.columns 
WHERE table_schema = DATABASE() 
AND table_name = 'contact_info' 
AND column_name = 'youtube_url';

SET @sql = IF(@col_exists = 0, 
    'ALTER TABLE contact_info ADD COLUMN youtube_url VARCHAR(255) AFTER tiktok_url;', 
    'SELECT "youtube_url column already exists" as message;');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Display final table structure
DESCRIBE contact_info;
