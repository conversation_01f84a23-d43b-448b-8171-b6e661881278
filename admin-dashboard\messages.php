<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';
require_once 'classes/EmailService.php';

use <PERSON><PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\Exception;
require_once __DIR__ . '/vendor/autoload.php';

// Require authentication
Auth::requireLogin();

$messageModel = new Message();
$currentUser = Auth::getCurrentUser();

// Get dashboard statistics for sidebar
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();

// Handle form submissions
$success = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'mark_read':
                $messageId = intval($_POST['message_id']);
                if ($messageModel->markAsRead($messageId)) {
                    $success = "Message marked as read.";
                } else {
                    $error = "Failed to mark message as read.";
                }
                break;
                
            case 'mark_unread':
                $messageId = intval($_POST['message_id']);
                $sql = "UPDATE messages SET is_read = 0 WHERE message_id = :id";
                $stmt = $messageModel->getDb()->prepare($sql);
                $stmt->bindParam(':id', $messageId);
                if ($stmt->execute()) {
                    $success = "Message marked as unread.";
                } else {
                    $error = "Failed to mark message as unread.";
                }
                break;
                
            case 'reply':
                $messageId = intval($_POST['message_id']);
                $replyContent = Utils::sanitizeInput($_POST['reply_content']);
                $senderEmail = Utils::sanitizeInput($_POST['sender_email']);
                $senderName = Utils::sanitizeInput($_POST['sender_name']);
                $originalSubject = Utils::sanitizeInput($_POST['original_subject']);
                
                if (!empty($replyContent)) {
                    // Save reply to database
                    if ($messageModel->reply($messageId, $replyContent)) {
                        // Mark as read
                        $messageModel->markAsRead($messageId);
                        
                        // Send email reply
                        $emailService = new EmailService();

                        // Get the full original message details from database
                        $fullOriginalMessage = $messageModel->findById($messageId);
                        $originalMessage = [
                            'message_id' => $messageId,
                            'sender_name' => $fullOriginalMessage['sender_name'],
                            'sender_email' => $fullOriginalMessage['sender_email'],
                            'subject' => $fullOriginalMessage['subject'],
                            'message_content' => $fullOriginalMessage['message_content']
                        ];

                        // Use direct PHPMailer (like our working tests) instead of EmailService

                        $mail = new PHPMailer(true);

                        try {
                            // Determine if quote-related
                            $isQuoteRelated = strpos(strtolower($originalMessage['subject']), 'quote') !== false;

                            // SMTP settings
                            $mail->isSMTP();
                            $mail->Host = 'melevatours.co.ke';
                            $mail->SMTPAuth = true;
                            $mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
                            $mail->Port = 465;

                            if ($isQuoteRelated) {
                                $mail->Username = '<EMAIL>';
                                $mail->Password = '1V^bAvDR!%)6,C&A';
                                $mail->setFrom('<EMAIL>', 'Meleva Tours - Booking Department');
                                $fromDept = 'Booking Department';
                            } else {
                                $mail->Username = '<EMAIL>';
                                $mail->Password = 'hi$Ch9=lYcap{7cA';
                                $mail->setFrom('<EMAIL>', 'Meleva Tours - Customer Service');
                                $fromDept = 'Customer Service';
                            }

                            $mail->addAddress($originalMessage['sender_email'], $originalMessage['sender_name']);
                            $mail->isHTML(true);
                            $mail->Subject = 'Re: ' . $originalMessage['subject'];

                            // Professional email content matching quote email format
                            $adminName = $currentUser['username'] ?? 'Meleva Tours Team';
                            $mail->Body = "
                            <html>
                            <head>
                                <title>Response to Your Inquiry - Meleva Tours and Travel</title>
                                <style>
                                    body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
                                    .container { max-width: 600px; margin: 0 auto; padding: 20px; background: #ffffff; }
                                    .header { background: linear-gradient(135deg, #dc2626, #ea580c); color: white; padding: 25px; text-align: center; border-radius: 8px 8px 0 0; }
                                    .content { background: #f8fafc; padding: 25px; border-radius: 0 0 8px 8px; border: 1px solid #e2e8f0; }
                                    .highlight { background: white; padding: 20px; border-radius: 8px; border-left: 4px solid #dc2626; margin: 20px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
                                    .contact-info { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border: 1px solid #e2e8f0; }
                                    .contact-item { display: flex; align-items: center; margin-bottom: 10px; }
                                    .contact-icon { width: 20px; margin-right: 10px; color: #dc2626; }
                                    .footer { text-align: center; margin-top: 25px; color: #64748b; font-size: 12px; }
                                    .logo { margin-bottom: 15px; }
                                    .logo img { display: block; margin: 0 auto; max-width: 100px; height: auto; }
                                    .signature { margin-top: 20px; padding-top: 15px; border-top: 1px solid #e2e8f0; }
                                </style>
                            </head>
                            <body>
                                <div class='container'>
                                    <div class='header'>
                                        <div class='logo'>
                                            <h1 style='margin: 0; font-size: 28px;'>MELEVA TOURS</h1>
                                            <p style='margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;'>Smooth Travels, Seamless Experiences!</p>
                                        </div>
                                        <h2 style='margin: 15px 0 0 0;'>Response from {$fromDept}</h2>
                                    </div>
                                    <div class='content'>
                                        <p>Dear <strong>{$originalMessage['sender_name']}</strong>,</p>

                                        <p>Thank you for contacting Meleva Tours and Travel. We have received your message and are pleased to provide you with the following response:</p>

                                        <div class='highlight'>
                                            <h4 style='color: #dc2626; margin-top: 0;'>Your Message:</h4>
                                            <p style='color: #4b5563; font-style: italic;'>" . nl2br(htmlspecialchars($originalMessage['message_content'])) . "</p>
                                        </div>

                                        <div class='highlight'>
                                            <h4 style='color: #dc2626; margin-top: 0;'>Our Response:</h4>
                                            <p style='color: #374151; line-height: 1.6;'>" . nl2br(htmlspecialchars($replyContent)) . "</p>
                                        </div>

                                        <div class='contact-info'>
                                            <h4 style='color: #dc2626; margin-top: 0;'>Contact Information</h4>
                                            <div class='contact-item'>
                                                <span class='contact-icon'>📧</span>
                                                <span><strong>Email:</strong> <EMAIL></span>
                                            </div>
                                            <div class='contact-item'>
                                                <span class='contact-icon'>📞</span>
                                                <span><strong>Phone:</strong> +254 123 456 789</span>
                                            </div>
                                            <div class='contact-item'>
                                                <span class='contact-icon'>📍</span>
                                                <span><strong>Location:</strong> Nairobi, Kenya</span>
                                            </div>
                                            <div class='contact-item'>
                                                <span class='contact-icon'>🕒</span>
                                                <span><strong>Hours:</strong> Mon-Fri 8AM-6PM</span>
                                            </div>
                                        </div>

                                        <div style='text-align: center; margin: 30px 0;'>
                                            <a href='https://melevatours.co.ke' style='display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Visit Our Website</a>
                                            <a href='https://melevatours.co.ke/tours.php' style='display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>View Tours</a>
                                            <a href='https://melevatours.co.ke/contact.php' style='display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 5px;'>Contact Us</a>
                                        </div>

                                        <div class='signature'>
                                            <p>We look forward to helping you create unforgettable travel memories!</p>
                                        </div>
                                    </div>
                                    <div class='footer'>
                                        <p>Best regards,<br>
                                        <strong>{$adminName}</strong><br>
                                        Meleva Tours and Travel Team<br>
                                        <em>Smooth Travels, Seamless Experiences!</em></p>

                                        <p style='margin-top: 20px;'>
                                            <a href='https://melevatours.co.ke'>Visit our website</a> |
                                            <a href='https://melevatours.co.ke/tours.php'>View Tours</a> |
                                            <a href='https://melevatours.co.ke/contact.php'>Contact Us</a>
                                        </p>
                                    </div>
                                </div>
                            </body>
                            </html>";

                            $mail->AltBody = "MELEVA TOURS AND TRAVEL\nSmooth Travels, Seamless Experiences!\n\n" .
                                           "Reply from {$fromDept}\n\n" .
                                           "Your Message:\n" . $originalMessage['message_content'] . "\n\n" .
                                           "Our Reply:\n" . $replyContent . "\n\n" .
                                           "CONTACT INFORMATION:\n" .
                                           "Email: <EMAIL>\n" .
                                           "Phone: +254 123 456 789\n" .
                                           "Location: Nairobi, Kenya\n" .
                                           "Hours: Mon-Fri 8AM-6PM\n\n" .
                                           "Website: https://melevatours.co.ke\n" .
                                           "Tours: https://melevatours.co.ke/tours.php\n" .
                                           "Contact: https://melevatours.co.ke/contact.php\n\n" .
                                           "Best regards,\n" . $adminName . "\n" .
                                           "Meleva Tours and Travel Team\n" .
                                           "Smooth Travels, Seamless Experiences!";

                            $emailSent = $mail->send();

                        } catch (Exception $e) {
                            error_log("Direct PHPMailer error: " . $e->getMessage());
                            $emailSent = false;
                        }

                        if ($emailSent) {
                            $success = "Reply sent successfully and saved to database. [DEBUG: Email sent to " . $originalMessage['sender_email'] . "]";
                        } else {
                            $success = "Reply saved to database but email sending failed. [DEBUG: Check error logs for details]";
                        }
                    } else {
                        $error = "Failed to save reply.";
                    }
                } else {
                    $error = "Reply content cannot be empty.";
                }
                break;
                
            case 'delete':
                $messageId = intval($_POST['message_id']);
                if ($messageModel->delete($messageId)) {
                    $success = "Message deleted successfully.";
                } else {
                    $error = "Failed to delete message.";
                }
                break;
                
            case 'bulk_action':
                $selectedMessages = $_POST['selected_messages'] ?? [];
                $bulkAction = $_POST['bulk_action_type'] ?? '';
                
                if (!empty($selectedMessages) && !empty($bulkAction)) {
                    $successCount = 0;
                    foreach ($selectedMessages as $messageId) {
                        $messageId = intval($messageId);
                        switch ($bulkAction) {
                            case 'mark_read':
                                if ($messageModel->markAsRead($messageId)) $successCount++;
                                break;
                            case 'mark_unread':
                                $sql = "UPDATE messages SET is_read = 0 WHERE message_id = :id";
                                $stmt = $messageModel->getDb()->prepare($sql);
                                $stmt->bindParam(':id', $messageId);
                                if ($stmt->execute()) $successCount++;
                                break;
                            case 'delete':
                                if ($messageModel->delete($messageId)) $successCount++;
                                break;
                        }
                    }
                    $success = "Bulk action completed. {$successCount} message(s) processed.";
                } else {
                    $error = "Please select messages and an action.";
                }
                break;
        }
    }
}

// Get filter parameters
$filter = $_GET['filter'] ?? 'all';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Build filters array for the new method
$filters = [];

if ($filter === 'unread') {
    $filters['is_read'] = 0;
} elseif ($filter === 'read') {
    $filters['is_read'] = 1;
} elseif ($filter === 'replied') {
    // We'll handle this in the query
} elseif ($filter === 'contact') {
    $filters['category'] = 'contact';
} elseif ($filter === 'quote') {
    $filters['category'] = 'quote';
}

if (!empty($search)) {
    $filters['search'] = $search;
}

// Build query based on filters (compatible with existing database)
$whereConditions = [];
$params = [];

if ($filter === 'unread') {
    $whereConditions[] = "is_read = 0";
} elseif ($filter === 'read') {
    $whereConditions[] = "is_read = 1";
} elseif ($filter === 'replied') {
    $whereConditions[] = "reply_content IS NOT NULL";
} elseif ($filter === 'contact') {
    // Check if message_category column exists, otherwise use subject pattern
    try {
        $checkColumn = $messageModel->getDb()->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
        $checkColumn->execute();
        if ($checkColumn->fetch()) {
            $whereConditions[] = "message_category = 'contact'";
        } else {
            $whereConditions[] = "subject LIKE '%Contact Form%'";
        }
    } catch (Exception $e) {
        $whereConditions[] = "subject LIKE '%Contact Form%'";
    }
} elseif ($filter === 'quote') {
    // Check if message_category column exists, otherwise use subject pattern
    try {
        $checkColumn = $messageModel->getDb()->prepare("SHOW COLUMNS FROM messages LIKE 'message_category'");
        $checkColumn->execute();
        if ($checkColumn->fetch()) {
            $whereConditions[] = "message_category = 'quote'";
        } else {
            $whereConditions[] = "subject LIKE '%Quote Request%'";
        }
    } catch (Exception $e) {
        $whereConditions[] = "subject LIKE '%Quote Request%'";
    }
} elseif ($filter === 'outgoing') {
    // Show only outgoing messages (admin replies)
    $whereConditions[] = "message_type = 'outgoing'";
}

if (!empty($search)) {
    $whereConditions[] = "(sender_name LIKE :search OR sender_email LIKE :search OR subject LIKE :search OR message_content LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

// Filter out outgoing messages by default (admin replies tracked in database)
// Only show incoming customer messages unless specifically viewing outgoing messages
if ($filter !== 'outgoing') {
    $whereConditions[] = "(message_type IS NULL OR message_type = 'incoming')";
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// Get messages with pagination
$sql = "SELECT m.*
        FROM messages m
        {$whereClause}
        ORDER BY m.received_at DESC
        LIMIT :limit OFFSET :offset";
$stmt = $messageModel->getDb()->prepare($sql);

foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$messages = $stmt->fetchAll();

// Get total count for pagination
$countSql = "SELECT COUNT(*) as total FROM messages m {$whereClause}";
$countStmt = $messageModel->getDb()->prepare($countSql);
foreach ($params as $key => $value) {
    $countStmt->bindValue($key, $value);
}
$countStmt->execute();
$totalMessages = $countStmt->fetch()['total'];
$totalPages = ceil($totalMessages / $limit);

// Get message statistics
$messageStats = $messageModel->getStats();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Messages - Meleva Tours Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .sidebar-transition { transition: transform 0.3s ease-in-out; }
        .sidebar-active { background-color: #fff7ed !important; color: #ea580c !important; }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.1); }
        .message-card { transition: all 0.2s ease; cursor: pointer; }
        .message-card:hover { background-color: #f9fafb; }

        /* Unread message styling for table rows */
        .message-unread {
            background-color: #fffbf5;
            border-left: 4px solid #f97316;
        }
        .message-unread .sender-name { font-weight: 700; }
        .message-unread .message-subject { font-weight: 600; }
        .message-unread .message-preview { font-weight: 500; color: #4b5563; }

        /* Read message styling */
        .message-read {
            border-left: 4px solid transparent;
            font-weight: 400;
        }

        /* Replied message styling */
        .message-replied {
            border-left: 4px solid #10b981;
        }

        /* Table row hover effects */
        .message-card:hover {
            background-color: #f8fafc;
            box-shadow: inset 4px 0 0 #f97316;
        }

        /* Selected row styling */
        .message-card.selected {
            background-color: #fef3e2;
            border-color: #fed7aa;
        }

        /* Text styling for readability */
        .sender-name {
            font-size: 14px;
            line-height: 1.4;
        }
        .message-subject {
            font-size: 14px;
            line-height: 1.4;
        }
        .message-preview {
            font-size: 13px;
            line-height: 1.3;
            color: #6b7280;
        }

        /* Badge improvements */
        .badge {
            font-size: 11px;
            font-weight: 500;
            padding: 2px 6px;
        }

        /* Table specific styles */
        .min-w-full td {
            vertical-align: top;
        }

        /* Responsive table improvements */
        @media (max-width: 768px) {
            .message-subject,
            .message-preview {
                max-width: 150px;
            }
        }

        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }

            #sidebar.sidebar-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
            }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }

        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>

    <!-- Sidebar -->
    <?php include 'sidebar.php'; ?>

    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Messages</h1>
            </div>
        </div>

        <!-- Content Area -->
        <div class="p-6">
            <!-- Page Description and Statistics -->
            <div class="mb-6">
                <div class="text-center mb-4">
                    <p class="text-gray-600">Manage contact form and quote request messages</p>
                </div>
                <div class="flex flex-col sm:flex-row items-center justify-center gap-2 sm:gap-4">
                    <div class="flex flex-wrap items-center justify-center gap-2 text-sm text-gray-600">
                        <span class="bg-orange-100 text-orange-800 px-3 py-1 rounded-full whitespace-nowrap">
                            <?php echo $messageStats['unread']; ?> Unread
                        </span>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full whitespace-nowrap">
                            <?php echo $messageStats['replied']; ?> Replied
                        </span>
                        <span class="bg-gray-100 text-gray-800 px-3 py-1 rounded-full whitespace-nowrap">
                            <?php echo $messageStats['total']; ?> Total
                        </span>
                    </div>
                </div>
            </div>

            <!-- Success/Error Messages -->
            <?php if ($success): ?>
                <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                <form method="GET" class="space-y-4 sm:space-y-0 sm:flex sm:flex-wrap sm:items-center sm:gap-4">
                    <div class="flex-1 min-w-0 sm:min-w-64">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>"
                               placeholder="Search messages..."
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                    </div>
                    <div class="w-full sm:w-auto">
                        <select name="filter" class="w-full sm:w-auto px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500">
                            <option value="all" <?php echo $filter === 'all' ? 'selected' : ''; ?>>Incoming Messages</option>
                            <option value="unread" <?php echo $filter === 'unread' ? 'selected' : ''; ?>>Unread</option>
                            <option value="read" <?php echo $filter === 'read' ? 'selected' : ''; ?>>Read</option>
                            <option value="replied" <?php echo $filter === 'replied' ? 'selected' : ''; ?>>Replied</option>
                            <option value="contact" <?php echo $filter === 'contact' ? 'selected' : ''; ?>>Contact Form</option>
                            <option value="quote" <?php echo $filter === 'quote' ? 'selected' : ''; ?>>Quote Requests</option>
                            <option value="outgoing" <?php echo $filter === 'outgoing' ? 'selected' : ''; ?>>Sent Replies</option>
                        </select>
                    </div>
                    <div class="flex flex-col sm:flex-row gap-2 sm:gap-4 w-full sm:w-auto">
                        <button type="submit" class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors whitespace-nowrap">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                        <a href="messages.php" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors text-center whitespace-nowrap">
                            <i class="fas fa-times mr-2"></i>Clear
                        </a>
                    </div>
                </form>
            </div>

            <!-- Bulk Actions -->
            <form method="POST" id="bulk-form">
                <input type="hidden" name="action" value="bulk_action">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6 mb-6">
                    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div class="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
                            <label class="flex items-center">
                                <input type="checkbox" id="select-all" class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                                <span class="ml-2 text-sm text-gray-700">Select All</span>
                            </label>
                            <div class="flex flex-col sm:flex-row gap-2 sm:gap-3">
                                <select name="bulk_action_type" class="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                                    <option value="">Bulk Actions</option>
                                    <option value="mark_read">Mark as Read</option>
                                    <option value="mark_unread">Mark as Unread</option>
                                    <option value="delete">Delete</option>
                                </select>
                                <button type="submit" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg text-sm transition-colors whitespace-nowrap">
                                    Apply
                                </button>
                            </div>
                        </div>
                        <?php if ($totalMessages > 0): ?>
                        <div class="text-sm text-gray-600 text-center sm:text-right">
                            Showing <?php echo $offset + 1; ?> to <?php echo min($offset + $limit, $totalMessages); ?> of <?php echo $totalMessages; ?> messages
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Messages Table -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden card-hover">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-800">All Messages</h3>
                        <p class="text-sm text-gray-500 mt-1">Click on any message to view details and take actions</p>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                                        <input type="checkbox" id="select-all" class="rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                                    </th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sender</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category & Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php if (empty($messages)): ?>
                                    <tr>
                                        <td colspan="5" class="px-6 py-12 text-center text-gray-500">
                                            <i class="fas fa-inbox text-6xl text-gray-300 mb-4"></i>
                                            <div>
                                                <h3 class="text-xl font-semibold text-gray-600 mb-2">No Messages Found</h3>
                                                <p class="text-gray-500">
                                                    <?php if (!empty($search) || $filter !== 'all'): ?>
                                                        No messages match your current filters.
                                                    <?php else: ?>
                                                        You haven't received any messages yet.
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($messages as $message): ?>
                                        <tr class="message-card cursor-pointer hover:bg-gray-50 transition-colors <?php
                                            echo !$message['is_read'] ? 'message-unread' : 'message-read';
                                            echo !empty($message['reply_content']) ? ' message-replied' : '';
                                        ?>" onclick="viewMessage(<?php echo $message['message_id']; ?>)">
                                            <!-- Checkbox -->
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <label class="flex items-center" onclick="event.stopPropagation();">
                                                    <input type="checkbox" name="selected_messages[]" value="<?php echo $message['message_id']; ?>"
                                                           class="message-checkbox rounded border-gray-300 text-orange-600 focus:ring-orange-500">
                                                </label>
                                            </td>

                                            <!-- Sender -->
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex items-center">
                                                    <div class="flex-shrink-0 h-10 w-10">
                                                        <div class="h-10 w-10 rounded-full bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                                            <span class="text-white font-medium text-sm">
                                                                <?php echo strtoupper(substr($message['sender_name'], 0, 1)); ?>
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="ml-4">
                                                        <div class="sender-name text-sm font-medium text-gray-900">
                                                            <?php echo htmlspecialchars($message['sender_name']); ?>
                                                        </div>
                                                        <div class="text-sm text-gray-500">
                                                            <?php echo htmlspecialchars($message['sender_email']); ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- Subject -->
                                            <td class="px-6 py-4">
                                                <div class="message-subject text-sm text-gray-900 max-w-xs truncate">
                                                    <?php echo htmlspecialchars($message['subject']); ?>
                                                </div>
                                            </td>



                                            <!-- Status -->
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="flex flex-wrap gap-1">
                                                    <?php if (!$message['is_read']): ?>
                                                        <span class="badge bg-orange-100 text-orange-800 text-xs px-2 py-1 rounded-full">New</span>
                                                    <?php endif; ?>
                                                    <?php if (!empty($message['reply_content'])): ?>
                                                        <span class="badge bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Replied</span>
                                                    <?php endif; ?>
                                                    <?php
                                                    // Check if message_category exists, otherwise determine from subject
                                                    if (isset($message['message_category'])) {
                                                        $category = $message['message_category'];
                                                    } else {
                                                        // Fallback to subject-based detection
                                                        $subject = strtolower($message['subject']);
                                                        if (strpos($subject, 'quote') !== false) {
                                                            $category = 'quote';
                                                        } elseif (strpos($subject, 'contact') !== false) {
                                                            $category = 'contact';
                                                        } else {
                                                            $category = 'general';
                                                        }
                                                    }

                                                    if ($category === 'quote'): ?>
                                                        <span class="badge bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                                            <i class="fas fa-file-invoice-dollar mr-1"></i>Quote
                                                        </span>
                                                    <?php elseif ($category === 'contact'): ?>
                                                        <span class="badge bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                                                            <i class="fas fa-envelope mr-1"></i>Contact
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="badge bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full">
                                                            <i class="fas fa-message mr-1"></i>General
                                                        </span>
                                                    <?php endif; ?>

                                                    <?php if (isset($message['is_reply']) && $message['is_reply']): ?>
                                                        <span class="badge bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">
                                                            <i class="fas fa-reply mr-1"></i>Reply
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>

                                            <!-- Date -->
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                <div>
                                                    <?php
                                                    // Database already stores in local timezone (UTC+3)
                                                    $date = new DateTime($message['received_at'], new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                    $now = new DateTime('now', new DateTimeZone(DatabaseConfig::TIMEZONE));
                                                    $diff = $now->diff($date);

                                                    if ($diff->days == 0) {
                                                        echo $date->format('g:i A');
                                                    } elseif ($diff->days < 7) {
                                                        echo $date->format('M j');
                                                    } else {
                                                        echo $date->format('M j, Y');
                                                    }
                                                    ?>
                                                </div>
                                                <?php if (!empty($message['replied_at'])): ?>
                                                    <div class="text-xs text-green-600 mt-1">
                                                        <i class="fas fa-reply mr-1"></i>Replied
                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </form>

            <!-- Pagination -->
            <?php if ($totalPages > 1): ?>
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
                    <div class="flex items-center justify-center">
                        <div class="flex items-center space-x-2">
                            <?php if ($page > 1): ?>
                                <a href="?page=<?php echo $page - 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>"
                                   class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors">
                                    <i class="fas fa-chevron-left mr-1"></i>Previous
                                </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?page=<?php echo $i; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>"
                                   class="<?php echo $i === $page ? 'bg-orange-500 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700'; ?> px-3 py-2 rounded text-sm transition-colors">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($page < $totalPages): ?>
                                <a href="?page=<?php echo $page + 1; ?>&filter=<?php echo urlencode($filter); ?>&search=<?php echo urlencode($search); ?>"
                                   class="bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors">
                                    Next<i class="fas fa-chevron-right ml-1"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- View Message Modal -->
    <div id="viewMessageModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-screen overflow-y-auto">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-900">Message Details</h2>
                        <div class="flex items-center space-x-2">
                            <!-- Action buttons will be added here dynamically -->
                            <div id="messageActions" class="flex items-center space-x-2">
                                <!-- Actions will be populated by JavaScript -->
                            </div>
                            <button onclick="closeModal('viewMessageModal')" class="text-gray-400 hover:text-gray-600 ml-4">
                                <i class="fas fa-times text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div id="messageContent" class="p-6">
                    <!-- Message content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Reply Modal -->
    <div id="replyModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-900">Reply to Message</h2>
                        <button onclick="closeModal('replyModal')" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <form method="POST" class="p-6">
                    <input type="hidden" name="action" value="reply">
                    <input type="hidden" name="message_id" id="replyMessageId">
                    <input type="hidden" name="sender_email" id="replySenderEmail">
                    <input type="hidden" name="sender_name" id="replySenderName">
                    <input type="hidden" name="original_subject" id="replyOriginalSubject">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reply Message:</label>
                        <textarea name="reply_content" rows="8" required
                                  placeholder="Type your reply here..."
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 resize-none"></textarea>
                    </div>

                    <div class="flex items-center justify-end space-x-4">
                        <button type="button" onclick="closeModal('replyModal')"
                                class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="bg-orange-500 hover:bg-orange-600 text-white px-6 py-2 rounded-lg transition-colors">
                            <i class="fas fa-paper-plane mr-2"></i>Send Reply
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // Mobile sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');

            function toggleSidebar() {
                sidebar.classList.toggle('sidebar-open');
                sidebarOverlay.classList.toggle('active');
                document.body.classList.toggle('overflow-hidden');
            }

            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }

            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }

            const navLinks = sidebar.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });

            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });
        });

        // Select all functionality
        document.getElementById('select-all').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.message-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Update select all when individual checkboxes change
        document.querySelectorAll('.message-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allCheckboxes = document.querySelectorAll('.message-checkbox');
                const checkedCheckboxes = document.querySelectorAll('.message-checkbox:checked');
                const selectAllCheckbox = document.getElementById('select-all');

                // Update row selection styling
                const messageRow = this.closest('tr');
                if (this.checked) {
                    messageRow.classList.add('selected');
                } else {
                    messageRow.classList.remove('selected');
                }

                if (checkedCheckboxes.length === 0) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = false;
                } else if (checkedCheckboxes.length === allCheckboxes.length) {
                    selectAllCheckbox.indeterminate = false;
                    selectAllCheckbox.checked = true;
                } else {
                    selectAllCheckbox.indeterminate = true;
                }
            });
        });

        // Bulk form validation
        document.getElementById('bulk-form').addEventListener('submit', function(e) {
            const selectedMessages = document.querySelectorAll('.message-checkbox:checked');
            const bulkAction = document.querySelector('select[name="bulk_action_type"]').value;

            if (selectedMessages.length === 0) {
                e.preventDefault();
                alert('Please select at least one message.');
                return false;
            }

            if (!bulkAction) {
                e.preventDefault();
                alert('Please select an action.');
                return false;
            }

            if (bulkAction === 'delete') {
                if (!confirm(`Are you sure you want to delete ${selectedMessages.length} message(s)?`)) {
                    e.preventDefault();
                    return false;
                }
            }
        });

        // Modal functions
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        function viewMessage(messageId) {
            // Fetch message details via AJAX
            fetch(`api/get_message.php?id=${messageId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const message = data.message;

                        // Create action buttons
                        const actionButtons = `
                            ${!message.is_read ? `
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="mark_read">
                                    <input type="hidden" name="message_id" value="${message.message_id}">
                                    <button type="submit" class="bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded text-sm transition-colors">
                                        <i class="fas fa-check mr-1"></i>Mark Read
                                    </button>
                                </form>
                            ` : `
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="mark_unread">
                                    <input type="hidden" name="message_id" value="${message.message_id}">
                                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white px-3 py-2 rounded text-sm transition-colors">
                                        <i class="fas fa-envelope mr-1"></i>Mark Unread
                                    </button>
                                </form>
                            `}
                            <button type="button" onclick="replyToMessage(${message.message_id})"
                                    class="bg-orange-500 hover:bg-orange-600 text-white px-3 py-2 rounded text-sm transition-colors">
                                <i class="fas fa-reply mr-1"></i>Reply
                            </button>
                            <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this message?')">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="message_id" value="${message.message_id}">
                                <button type="submit" class="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded text-sm transition-colors">
                                    <i class="fas fa-trash mr-1"></i>Delete
                                </button>
                            </form>
                        `;

                        const content = `
                            <div class="space-y-6">
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">From:</label>
                                        <p class="text-gray-900 font-medium">${message.sender_name}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Email:</label>
                                        <p class="text-gray-900">${message.sender_email}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Subject:</label>
                                        <p class="text-gray-900 font-medium">${message.subject}</p>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-1">Received:</label>
                                        <p class="text-gray-900">${new Date(message.received_at).toLocaleString()}</p>
                                    </div>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-3">Message:</label>
                                    <div class="bg-gray-50 rounded-lg p-4 border">
                                        <pre class="whitespace-pre-wrap text-gray-900 font-normal leading-relaxed">${message.message_content}</pre>
                                    </div>
                                </div>

                                ${message.reply_content ? `
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-3">Your Reply:</label>
                                        <div class="bg-green-50 rounded-lg p-4 border border-green-200">
                                            <pre class="whitespace-pre-wrap text-green-800 font-normal leading-relaxed">${message.reply_content}</pre>
                                            <p class="text-sm text-green-600 mt-3 pt-3 border-t border-green-200">
                                                <i class="fas fa-clock mr-1"></i>Replied on: ${new Date(message.replied_at).toLocaleString()}
                                            </p>
                                        </div>
                                    </div>
                                ` : ''}
                            </div>
                        `;

                        document.getElementById('messageActions').innerHTML = actionButtons;
                        document.getElementById('messageContent').innerHTML = content;
                        document.getElementById('viewMessageModal').classList.remove('hidden');
                    } else {
                        alert('Error loading message details.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading message details.');
                });
        }

        function replyToMessage(messageId) {
            // Fetch message details for reply
            fetch(`api/get_message.php?id=${messageId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const message = data.message;
                        document.getElementById('replyMessageId').value = messageId;
                        document.getElementById('replySenderEmail').value = message.sender_email;
                        document.getElementById('replySenderName').value = message.sender_name;
                        document.getElementById('replyOriginalSubject').value = message.subject;
                        document.getElementById('replyModal').classList.remove('hidden');
                    } else {
                        alert('Error loading message details.');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error loading message details.');
                });
        }

        // Close modals when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('fixed') && e.target.classList.contains('inset-0')) {
                e.target.classList.add('hidden');
            }
        });

        // Auto-refresh unread count every 30 seconds
        setInterval(function() {
            fetch('api/get_unread_count.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const badge = document.querySelector('.sidebar a[href="messages.php"] .bg-red-500');
                        if (badge) {
                            if (data.count > 0) {
                                badge.textContent = data.count;
                                badge.style.display = 'inline-block';
                            } else {
                                badge.style.display = 'none';
                            }
                        }
                    }
                })
                .catch(error => console.error('Error updating unread count:', error));
        }, 30000);
    </script>
</body>
</html>
