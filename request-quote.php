<?php
// Include database configuration and models
require_once 'admin-dashboard/config/config.php';
require_once 'admin-dashboard/classes/models.php';
require_once 'admin-dashboard/classes/additional_models.php';
require_once 'admin-dashboard/classes/booking_models.php';
require_once 'admin-dashboard/classes/EmailService.php';

/**
 * Convert date from dd/mm/yyyy format to Y-m-d format for database storage
 */
function convertDateFormat($dateString) {
    if (empty($dateString) || trim($dateString) === '') {
        return null;
    }

    $dateString = trim($dateString);

    // Check if it's already in Y-m-d format
    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateString)) {
        // Validate the date
        $dateParts = explode('-', $dateString);
        if (count($dateParts) === 3 && checkdate($dateParts[1], $dateParts[2], $dateParts[0])) {
            return $dateString;
        }
    }

    // Check if it's in dd/mm/yyyy format (from Flatpickr)
    if (preg_match('/^\d{1,2}\/\d{1,2}\/\d{4}$/', $dateString)) {
        $dateParts = explode('/', $dateString);
        if (count($dateParts) === 3) {
            $day = (int)$dateParts[0];
            $month = (int)$dateParts[1];
            $year = (int)$dateParts[2];

            // Validate the date and ensure reasonable year range
            if (checkdate($month, $day, $year) && $year >= 2024 && $year <= 2030) {
                return sprintf('%04d-%02d-%02d', $year, $month, $day);
            }
        }
    }

    // Try to parse with strtotime as fallback for other formats
    $timestamp = strtotime($dateString);
    if ($timestamp !== false && $timestamp > 0) {
        $convertedDate = date('Y-m-d', $timestamp);
        // Ensure the converted date is reasonable (not in the past beyond 1 year, not too far in future)
        $currentYear = (int)date('Y');
        $convertedYear = (int)date('Y', $timestamp);
        if ($convertedYear >= ($currentYear - 1) && $convertedYear <= ($currentYear + 10)) {
            return $convertedDate;
        }
    }

    // Log the problematic date for debugging
    error_log("Failed to convert date format: '$dateString'");
    return null;
}

// Initialize models
$contactModel = new ContactInfo();
$destinationModel = new Destination();
$tourPackageModel = new TourPackage();

// Fetch contact info
$contactInfo = $contactModel->getCurrent();

// Fetch destinations for the form
$destinations = $destinationModel->findAll();

// Fetch tour packages for the form
$tourPackages = $tourPackageModel->findAllWithDetails();

// Fetch featured tour packages for sidebar (limit to 4)
$featuredPackages = $tourPackageModel->findFeaturedWithDetails(4);

// Handle URL parameters for pre-selection
$preselectedPackageId = isset($_GET['package_id']) ? intval($_GET['package_id']) : null;
$preselectedDestinationId = isset($_GET['destination_id']) ? intval($_GET['destination_id']) : null;

// Get preselected package details if package_id is provided
$preselectedPackage = null;
if ($preselectedPackageId) {
    $preselectedPackage = $tourPackageModel->findByIdWithDetails($preselectedPackageId);
}

// Get preselected destination details if destination_id is provided
$preselectedDestination = null;
if ($preselectedDestinationId) {
    $preselectedDestination = $destinationModel->findById($preselectedDestinationId);
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Process the simplified quote request
    $quoteModel = new Quote();

    // Prepare quote data for the simplified system
    $quoteData = [
        'customer_name' => $_POST['first_name'] . ' ' . $_POST['last_name'],
        'customer_email' => $_POST['email'],
        'customer_phone' => $_POST['phone'] ?? '',
        'customer_country' => $_POST['country'] ?? '',
        'travel_date' => !empty($_POST['travel_date']) && $_POST['travel_date'] !== '0000-00-00' ? convertDateFormat($_POST['travel_date']) : null,
        'number_of_adults' => (int)($_POST['adults'] ?? 1),
        'number_of_children' => (int)($_POST['children'] ?? 0),
        'special_requirements' => $_POST['additional_info'] ?? '',
        'selected_activities' => !empty($selectedActivities) ? implode(', ', $selectedActivities) : null,
        'quote_status' => 'pending'
    ];

    // Get selected tour packages and activities
    $selectedPackages = $_POST['tour_packages'] ?? [];
    $selectedActivities = $_POST['activities'] ?? [];

    try {
        // Log the attempt
        error_log("Quote request attempt for: " . $quoteData['customer_email']);

        // Validate selected packages exist in database
        $validPackages = [];
        if (!empty($selectedPackages)) {
            $packageModel = new TourPackage();
            foreach ($selectedPackages as $packageId) {
                // Convert to integer and validate
                $packageId = (int)$packageId;
                if ($packageId > 0) {
                    $package = $packageModel->findById($packageId);
                    if ($package) {
                        $validPackages[] = $packageId;
                        error_log("Valid package added: ID {$packageId} - {$package['name']}");
                    } else {
                        error_log("Invalid package ID submitted: " . $packageId);
                    }
                } else {
                    error_log("Non-numeric package ID submitted: " . print_r($packageId, true));
                }
            }
        }

        error_log("Valid packages: " . json_encode($validPackages));

        // Create quote with validated packages
        $quoteId = $quoteModel->createQuoteWithPackages($quoteData, $validPackages);

        if ($quoteId) {
            error_log("Quote created successfully with ID: " . $quoteId);

            // Get the created quote with reference
            $quote = $quoteModel->findById($quoteId);
            $quoteReference = $quote['quote_reference'];

            error_log("Quote reference generated: " . $quoteReference);

            // Initialize email service
            $emailService = new EmailService();
            error_log("Email service initialized");

            // Prepare comprehensive quote data for emails
            $quoteEmailData = [
                'quote_reference' => $quoteReference,
                'customer_name' => $quoteData['customer_name'],
                'customer_email' => $quoteData['customer_email'],
                'customer_phone' => $quoteData['customer_phone'],
                'customer_country' => $quoteData['customer_country'],
                'travel_date' => $quoteData['travel_date'],
                'number_of_adults' => $quoteData['number_of_adults'],
                'number_of_children' => $quoteData['number_of_children'],
                'special_requirements' => $quoteData['special_requirements'],
                'selected_packages' => $validPackages,
                'selected_activities' => $selectedActivities
            ];

            // Send emails with better error handling
            $userEmailSent = false;
            $adminEmailSent = false;

            try {
                error_log("Attempting to send confirmation email to customer");
                $userEmailSent = $emailService->sendQuoteRequestConfirmation($quoteEmailData);
                error_log("User email sent: " . ($userEmailSent ? 'YES' : 'NO'));
            } catch (Exception $emailError) {
                error_log("User email error: " . $emailError->getMessage());
            }

            try {
                error_log("Attempting to send notification email to admin");
                $adminEmailSent = $emailService->sendQuoteRequestNotification($quoteEmailData);
                error_log("Admin email sent: " . ($adminEmailSent ? 'YES' : 'NO'));
            } catch (Exception $emailError) {
                error_log("Admin email error: " . $emailError->getMessage());
            }

            // Log email status for debugging
            if (!$adminEmailSent) {
                error_log("Quote request: Failed to send admin notification email for {$quoteData['customer_email']}");
            }
            if (!$userEmailSent) {
                error_log("Quote request: Failed to send user confirmation email to {$quoteData['customer_email']}");
            }

            // Success message regardless of email status
            $message = "Your travel quote request has been submitted successfully! Your quote reference is: {$quoteReference}. We will review your request and send you a detailed quote via email.";

            // Add email status info if there were issues
            if (!$userEmailSent || !$adminEmailSent) {
                $message .= " Note: There may be a delay in email notifications, but your request has been saved.";
            }

            $messageType = 'success';
        } else {
            throw new Exception('Failed to create quote');
        }
    } catch (Exception $e) {
        error_log("Quote request error: " . $e->getMessage());
        error_log("Quote request stack trace: " . $e->getTraceAsString());

        $message = 'There was an error submitting your request. Please try again.';
        $messageType = 'error';
    }
}

function generateQuoteMessage($data) {
    $message = "TRAVEL QUOTE REQUEST\n\n";
    
    // Travel Information
    $message .= "TRAVEL INFORMATION:\n";
    $message .= "Tour Packages: " . (isset($data['tour_packages']) ? implode(', ', $data['tour_packages']) : 'Not specified') . "\n";
    $message .= "Activities: " . (isset($data['activities']) ? implode(', ', $data['activities']) : 'Not specified') . "\n";
    $message .= "Travel Date: " . ($data['travel_date'] ?? 'Not specified') . "\n\n";
    
    // Travelers Information
    $message .= "TRAVELERS INFORMATION:\n";
    $message .= "Number of Adults: " . ($data['adults'] ?? '0') . "\n";
    $message .= "Number of Children: " . ($data['children'] ?? '0') . "\n\n";
    
    // Contact Information
    $message .= "CONTACT INFORMATION:\n";
    $message .= "Name: " . ($data['first_name'] ?? '') . " " . ($data['last_name'] ?? '') . "\n";
    $message .= "Email: " . ($data['email'] ?? '') . "\n";
    $message .= "Phone: " . ($data['phone'] ?? '') . "\n";
    $message .= "Country: " . ($data['country'] ?? '') . "\n\n";
    
    // Additional Information
    if (!empty($data['additional_info'])) {
        $message .= "ADDITIONAL INFORMATION:\n";
        $message .= $data['additional_info'] . "\n\n";
    }
    
    return $message;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Request Travel Quote | Meleva Tours & Travel</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.css"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.css" />
    <style>
        body {
            padding-top: 50px;
        }

        .gradient-text {
            background: linear-gradient(135deg, #f97316, #ea580c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .gradient-bg {
            background: linear-gradient(135deg, #7c2d12, #92400e);
        }

        .form-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .form-card {
            background: #FFE5B4; /* Light orange background */
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            border: 1px solid #FFE5B4; /* Light border */
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 0.75rem;
            border: 2px solid var(--border-color, #d1d5db); /* Light gray border */
            border-radius: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
            -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
            user-select: none; /* Prevent text selection */
            background-color: var(--bg-color, #ffffff);
            position: relative;
            box-shadow: var(--box-shadow, none);
        }

        .checkbox-item:hover {
            border-color: #f97316;
            background-color: #fff7ed;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(249, 115, 22, 0.15);
        }

        .checkbox-item.selected {
            border-color: #f97316 !important;
            background-color: #fff7ed !important;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1) !important;
        }

        .checkbox-item:not(.selected) {
            border-color: #d1d5db;
            background-color: #ffffff;
        }

        .checkbox-item.deselected {
            border-color: #d1d5db !important;
            background-color: #ffffff !important;
            box-shadow: none !important;
        }

        .checkbox-item.selected::after {
            content: '✓';
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #f97316;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .checkbox-item.active {
            border-color: #f97316 !important;
            background-color: #fff7ed !important;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
        }

        .checkbox-item:focus {
            outline: none;
        }

        .checkbox-item:focus:not(.selected):not(.deselected) {
            border-color: #f97316;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }

        .checkbox-item.selected:focus {
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.15);
        }

        .checkbox-item.deselected:focus {
            border-color: #d1d5db !important;
            background-color: #ffffff !important;
            box-shadow: none !important;
        }

        /* Package Selector Styles */
        .package-selector-container {
            position: relative;
        }

        .package-search-wrapper {
            position: relative;
        }

        .package-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 2px solid #d1d5db;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 300px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }

        .package-dropdown.hidden {
            display: none;
        }

        .package-option {
            padding: 8px 12px;
            cursor: pointer;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.15s ease;
            font-size: 14px;
        }

        .package-option:hover {
            background-color: #fff7ed;
        }

        .package-option:last-child {
            border-bottom: none;
        }

        .package-search-wrapper input:focus + #dropdown-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .dropdown-open #dropdown-arrow {
            transform: translateY(-50%) rotate(180deg);
        }

        .selected-packages-container {
            min-height: 60px;
            padding: 16px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            background-color: #f9fafb;
        }

        .selected-package-item {
            display: flex;
            align-items: center;
            justify-content: between;
            padding: 8px 12px;
            background: white;
            border: 2px solid #f97316;
            border-radius: 8px;
            margin-bottom: 6px;
            font-size: 14px;
        }

        .selected-package-item:last-child {
            margin-bottom: 0;
        }

        .remove-package-btn {
            background: #ef4444;
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
            transition: background-color 0.15s ease;
            margin-left: auto;
        }

        .remove-package-btn:hover {
            background: #dc2626;
        }

        .empty-selection-message {
            text-align: center;
            color: #6b7280;
            font-style: italic;
            padding: 20px;
        }

        /* Mobile-specific optimizations */
        @media (max-width: 768px) {
            .checkbox-item {
                transition: all 0.15s ease;
                -webkit-touch-callout: none;
                -webkit-user-select: none;
                -khtml-user-select: none;
                -moz-user-select: none;
                -ms-user-select: none;
                padding: 1rem; /* Larger padding for easier touch */
            }

            .checkbox-item:hover {
                /* Keep hover effects minimal on mobile */
                transform: none;
                box-shadow: none;
            }

            .checkbox-item:active {
                transform: scale(0.98);
                transition: transform 0.1s ease;
            }

            .checkbox-item.selected {
                border-color: #f97316 !important;
                background-color: #fff7ed !important;
                box-shadow: 0 0 0 2px rgba(249, 115, 22, 0.15);
            }

            .checkbox-item.selected::after {
                font-size: 1.2rem; /* Larger checkmark on mobile */
            }
        }

        .counter-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid #f97316;
            background: white;
            color: #f97316;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .counter-btn:hover {
            background: #f97316;
            color: white;
        }

        .counter-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .alert {
            padding: 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background-color: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }

        .alert-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }

        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        @media (min-width: 1024px) {
            .sticky {
                position: sticky;
                top: 2rem;
            }
        }

        .datepicker-wrapper {
            position: relative;
        }

        .datepicker-wrapper input {
            padding-right: 3rem; /* Make space for the calendar icon */
            cursor: pointer; /* Show pointer cursor for better UX */
        }

        .datepicker-wrapper .fa-calendar-alt {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #f97316 !important;
            cursor: pointer;
            pointer-events: auto;
            font-size: 1.2rem;
            z-index: 10;
            display: block !important;
        }

        /* Flatpickr calendar styling */
        .flatpickr-calendar {
            z-index: 9999 !important;
        }

        /* Mobile-specific datepicker improvements */
        @media (max-width: 768px) {
            .datepicker-wrapper {
                -webkit-tap-highlight-color: transparent;
            }

            .datepicker-wrapper input {
                cursor: pointer;
                font-size: 16px; /* Prevent zoom on iOS */
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
            }

            .datepicker-wrapper input:focus {
                outline: none;
                border-color: #f97316;
                box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
            }

            .datepicker-wrapper .fa-calendar-alt {
                font-size: 1.3rem !important;
                color: #f97316 !important;
                display: block !important;
                pointer-events: auto !important;
                z-index: 20 !important;
            }

            /* Ensure Flatpickr calendar is visible on mobile */
            .flatpickr-calendar {
                font-size: 16px;
                z-index: 9999 !important;
            }

            /* Hide any native date picker icons */
            input[type="date"]::-webkit-calendar-picker-indicator,
            input[type="text"]::-webkit-calendar-picker-indicator {
                display: none !important;
                -webkit-appearance: none !important;
            }
        }

        /* International Tel Input Styling */
        .iti {
            width: 100%;
        }

        .iti__flag-container {
            border-right: 2px solid #d1d5db;
        }

        .iti__selected-flag {
            padding: 16px 12px;
            border: none;
            background: transparent;
        }

        .iti__selected-flag:hover,
        .iti__selected-flag:focus {
            background: rgba(249, 115, 22, 0.05);
        }

        .iti__country-list {
            border: 2px solid #d1d5db;
            border-radius: 8px;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            max-height: 200px;
        }

        .iti__country:hover {
            background: rgba(249, 115, 22, 0.05);
        }

        .iti__country.iti__highlight {
            background: rgba(249, 115, 22, 0.1);
        }

        #phone.iti__tel-input {
            padding: 16px 16px 16px 60px;
            border: 2px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            width: 100%;
        }

        #phone.iti__tel-input:focus {
            border-color: #f97316;
            outline: none;
            box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.1);
        }

        /* Swiper Styles for Featured Tours */
        .featured-tours-swiper {
            padding: 0 !important;
        }

        .featured-tours-swiper .swiper-slide {
            height: auto;
        }

        .featured-tour-card {
            background: #1f2937;
            border-radius: 1rem;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
        }

        .featured-tour-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }

        .swiper-pagination-bullet {
            background: #d1d5db;
            opacity: 0.5;
        }

        .swiper-pagination-bullet-active {
            background: #f97316;
            opacity: 1;
        }


    </style>
</head>
<body class="bg-gray-50">
    <!-- Include Header Navigation -->
    <?php include 'header.php'; ?>

    <!-- Hero Section -->
    <section class="gradient-bg py-20 px-4">
        <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-3xl md:text-4xl font-semibold mb-6">
                <?php if ($preselectedPackage): ?>
                    Get Quote for <?php echo Utils::displayText($preselectedPackage['name']); ?>
                <?php else: ?>
                    Request Your Travel Quote
                <?php endif; ?>
            </h1>
            <div class="w-24 h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto mb-8"></div>
            <p class="text-lg md:text-xl opacity-90 max-w-3xl mx-auto">
                <?php if ($preselectedPackage): ?>
                    Complete the form below to get a personalized quote for your <?php echo Utils::displayText($preselectedPackage['name']); ?> adventure
                <?php else: ?>
                    Tell us about your dream safari adventure and we'll create a personalized travel proposal just for you
                <?php endif; ?>
            </p>
        </div>
    </section>

    <!-- Quote Request Form -->
    <section class="py-16 px-4">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Form Section (2/3 width) -->
                <div class="lg:col-span-2">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $messageType; ?> mb-8">
                            <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-triangle'; ?> mr-2"></i>
                            <?php echo htmlspecialchars($message); ?>
                        </div>
                    <?php endif; ?>

                    <div class="form-card">
                        <form method="POST" class="space-y-8">
                            <!-- Travel Information Section -->
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <i class="fas fa-map-marked-alt text-orange-500 mr-3"></i>
                                    Travel Information
                                </h2>

                                <!-- Tour Packages -->
                                <div class="mb-8">
                                    <label class="block text-lg font-semibold text-gray-700 mb-4">
                                        1. Which tour packages interest you? <span class="text-red-500">*</span>
                                    </label>
                                    <?php if ($preselectedPackage): ?>
                                        <div class="mb-4 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                            <div class="flex items-center">
                                                <i class="fas fa-info-circle text-orange-500 mr-2"></i>
                                                <span class="text-orange-800 font-medium">
                                                    We've pre-selected "<?php echo htmlspecialchars($preselectedPackage['name']); ?>" based on your interest. You can modify your selection below.
                                                </span>
                                            </div>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Package Selection Interface -->
                                    <div class="package-selector-container">
                                        <!-- Search and Add Interface -->
                                        <div class="relative mb-4">
                                            <div class="package-search-wrapper">
                                                <input type="text"
                                                       id="package-search"
                                                       placeholder="Click to select tour packages..."
                                                       class="w-full p-3 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none text-base pr-12 cursor-pointer"
                                                       autocomplete="off"
                                                       readonly>
                                                <i class="fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 transition-transform duration-200" id="dropdown-arrow"></i>
                                            </div>

                                            <!-- Dropdown Results -->
                                            <div id="package-dropdown" class="package-dropdown hidden">
                                                <?php foreach ($tourPackages as $package): ?>
                                                    <?php
                                                    // Smart formatting based on package type and duration (same as index.php)
                                                    $packageName = $package['name'];
                                                    $tourType = $package['type_name'] ?: 'Tour';
                                                    $duration = $package['duration'];

                                                    // Check if tour type already implies duration
                                                    $lowerTourType = strtolower($tourType);
                                                    $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                                                        strpos($lowerTourType, 'overnight') !== false ||
                                                                        strpos($lowerTourType, 'night') !== false);

                                                    if ($typeImpliesDuration) {
                                                        // Format: Tour Type + Package Name
                                                        $formattedName = $tourType . ' ' . $packageName;
                                                    } elseif (!empty($duration)) {
                                                        // Format: Duration + Package Name + Tour Type
                                                        $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                                    } else {
                                                        // Format: Package Name + Tour Type
                                                        $formattedName = $packageName . ' ' . $tourType;
                                                    }
                                                    ?>
                                                    <div class="package-option"
                                                         data-package-id="<?php echo $package['tour_package_id']; ?>"
                                                         data-package-name="<?php echo htmlspecialchars($formattedName); ?>"
                                                         data-package-price="<?php echo $package['price']; ?>"
                                                         data-package-duration="<?php echo htmlspecialchars($package['duration'] ?? ''); ?>">
                                                        <div class="flex items-center justify-between">
                                                            <div class="flex items-center flex-1">
                                                                <i class="fas fa-suitcase text-orange-500 mr-2 text-sm"></i>
                                                                <div class="flex-1">
                                                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($formattedName); ?></div>
                                                                </div>
                                                            </div>
                                                            <div class="text-orange-600 font-semibold text-sm ml-2">
                                                                $<?php echo number_format($package['price']); ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <!-- Selected Packages Display -->
                                        <div id="selected-packages" class="selected-packages-container">
                                            <div class="text-sm text-gray-600 mb-3">Selected packages:</div>
                                            <div id="selected-packages-list" class="space-y-2">
                                                <!-- Selected packages will be displayed here -->
                                            </div>
                                        </div>

                                        <!-- Hidden inputs for form submission -->
                                        <div id="package-inputs"></div>
                                    </div>
                                </div>

                                <!-- Activities -->
                                <div class="mb-8">
                                    <label class="block text-lg font-semibold text-gray-700 mb-4">
                                        2. What activities interest you? <span class="text-red-500">*</span>
                                    </label>
                                    <p class="text-gray-600 mb-4">Pick one, two, or even all of the following options.</p>
                                    <div class="checkbox-grid">
                                        <div class="checkbox-item">
                                            <input type="checkbox" name="activities[]" value="Safari" class="hidden">
                                            <i class="fas fa-binoculars text-orange-500 mr-3"></i>
                                            <span>Safari</span>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" name="activities[]" value="Beach Holiday" class="hidden">
                                            <i class="fas fa-umbrella-beach text-orange-500 mr-3"></i>
                                            <span>Beach Holiday</span>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" name="activities[]" value="Mountain Climbing" class="hidden">
                                            <i class="fas fa-mountain text-orange-500 mr-3"></i>
                                            <span>Mountain Climbing</span>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" name="activities[]" value="Cultural Tours" class="hidden">
                                            <i class="fas fa-landmark text-orange-500 mr-3"></i>
                                            <span>Cultural Tours</span>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" name="activities[]" value="Wildlife Photography" class="hidden">
                                            <i class="fas fa-camera text-orange-500 mr-3"></i>
                                            <span>Wildlife Photography</span>
                                        </div>
                                        <div class="checkbox-item">
                                            <input type="checkbox" name="activities[]" value="Adventure Sports" class="hidden">
                                            <i class="fas fa-parachute-box text-orange-500 mr-3"></i>
                                            <span>Adventure Sports</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Travelers Counter -->
                                <div class="mb-8">
                                    <label class="block text-lg font-semibold text-gray-700 mb-4">
                                        3. How many are travelling? <span class="text-red-500">*</span>
                                    </label>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Adults -->
                                        <div>
                                            <label class="block text-md font-semibold text-gray-700 mb-4">
                                                Number of adults <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex items-center space-x-4">
                                                <button type="button" class="counter-btn" onclick="changeCounter('adults', -1)">-</button>
                                                <input type="number" name="adults" id="adults" value="2" min="1" max="10" readonly
                                                       class="w-20 text-center text-xl font-bold border-2 border-gray-300 rounded-lg p-2">
                                                <button type="button" class="counter-btn" onclick="changeCounter('adults', 1)">+</button>
                                            </div>
                                        </div>

                                        <!-- Children -->
                                        <div>
                                            <label class="block text-md font-semibold text-gray-700 mb-4">
                                                Number of children <span class="text-red-500">*</span>
                                            </label>
                                            <div class="flex items-center space-x-4">
                                                <button type="button" class="counter-btn" onclick="changeCounter('children', -1)">-</button>
                                                <input type="number" name="children" id="children" value="0" min="0" max="10" readonly
                                                       class="w-20 text-center text-xl font-bold border-2 border-gray-300 rounded-lg p-2">
                                                <button type="button" class="counter-btn" onclick="changeCounter('children', 1)">+</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Travel Date -->
                                <div class="mb-8">
                                    <label class="block text-lg font-semibold text-gray-700 mb-4">
                                        4. When do you want to travel? <span class="text-red-500">*</span>
                                    </label>
                                    <p class="text-gray-600 mb-4">Select a date. You can always change it later on, if you are not sure.</p>
                                    <div class="datepicker-wrapper relative cursor-pointer">
                                        <input type="text"
                                            name="travel_date"
                                            id="travel_date"
                                            required
                                            placeholder="dd/mm/yyyy"
                                            class="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none text-lg pr-12 cursor-pointer"
                                            autocomplete="off">
                                        <i class="fas fa-calendar-alt absolute right-4 top-1/2 transform -translate-y-1/2 text-orange-500 cursor-pointer z-10"></i>
                                    </div>
                                </div>
                            </div>


                            <!-- Additional Information Section -->
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <i class="fas fa-comment-dots text-orange-500 mr-3"></i>
                                    Additional Information
                                </h2>
                                <textarea name="additional_info" rows="6"
                                          placeholder="Tell us about your preferences, special requirements, or anything else to make your trip unforgettable."
                                          class="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none resize-none"></textarea>
                            </div>


                            <!-- Contact Details Section -->
                            <div>
                                <h2 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                    <i class="fas fa-address-card text-orange-500 mr-3"></i>
                                    Your Contact Details
                                </h2>

                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- First Name -->
                                    <div>
                                        <label class="block text-lg font-semibold text-gray-700 mb-2">First name <span class="text-red-500">*</span></label>
                                        <input type="text" name="first_name" required
                                               placeholder="Your first name"
                                               class="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none">
                                    </div>

                                    <!-- Last Name -->
                                    <div>
                                        <label class="block text-lg font-semibold text-gray-700 mb-2">Last name <span class="text-red-500">*</span></label>
                                        <input type="text" name="last_name" required
                                               placeholder="Your last name"
                                               class="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none">
                                    </div>

                                    <!-- Email -->
                                    <div class="md:col-span-2">
                                        <label class="block text-lg font-semibold text-gray-700 mb-2">E-mail <span class="text-red-500">*</span></label>
                                        <input type="email" name="email" required
                                               placeholder="Your e-mail address"
                                               class="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none">
                                    </div>

                                    <!-- Phone with International Input -->
                                    <div class="md:col-span-2">
                                        <label class="block text-lg font-semibold text-gray-700 mb-2">Phone number <span class="text-red-500">*</span></label>
                                        <input type="tel" name="phone" id="phone" required
                                               class="w-full p-4 border-2 border-gray-300 rounded-lg focus:border-orange-500 focus:outline-none">
                                        <input type="hidden" name="country" id="country">
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center pt-8">
                                <button type="submit" 
                                        class="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white px-12 py-4 rounded-full font-bold text-xl transition duration-300 transform hover:scale-105 shadow-lg">
                                    <i class="fas fa-paper-plane mr-3"></i>
                                    Request Quote
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Sidebar Section (1/3 width) - Unchanged from original -->
                <div class="lg:col-span-1">
                    <div class="space-y-8 sticky top-8">
                        <!-- Contact Information Card -->
                        <div class="form-section rounded-2xl shadow-lg p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-phone text-orange-500 mr-3"></i>
                                Need Help?
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-phone text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Call Us</p>
                                        <p class="text-gray-600 text-sm">
                                            <a href="tel:<?php echo htmlspecialchars($contactInfo['phone_number'] ?? '+254123456789'); ?>"
                                               class="hover:text-orange-600 transition-colors">
                                                <?php echo htmlspecialchars($contactInfo['phone_number'] ?? '+254 123 456 789'); ?>
                                            </a>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-envelope text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Email Us</p>
                                        <p class="text-gray-600 text-sm">
                                            <a href="mailto:<?php echo htmlspecialchars($contactInfo['email'] ?? '<EMAIL>'); ?>"
                                               class="hover:text-orange-600 transition-colors">
                                                <?php echo htmlspecialchars($contactInfo['email'] ?? '<EMAIL>'); ?>
                                            </a>
                                        </p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                        <i class="fas fa-clock text-white text-sm"></i>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-gray-900">Working Hours</p>
                                        <p class="text-gray-600 text-sm"><?php echo htmlspecialchars($contactInfo['working_hours'] ?? 'Available 24/7'); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Featured Tours Card with Swiper -->
                        <div class="form-section rounded-2xl shadow-lg p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-star text-orange-500 mr-3"></i>
                                Featured Tours
                            </h3>

                            <?php if (!empty($featuredPackages)): ?>
                                <!-- Swiper Container -->
                                <div class="featured-tours-swiper swiper">
                                    <div class="swiper-wrapper">
                                        <?php foreach ($featuredPackages as $package): ?>
                                            <div class="swiper-slide">
                                                <div class="featured-tour-card">
                                                    <?php if ($package['display_image_url']): ?>
                                                        <img src="admin-dashboard/<?php echo htmlspecialchars($package['display_image_url']); ?>"
                                                             alt="<?php echo htmlspecialchars($package['name']); ?>"
                                                             class="w-full h-32 object-cover">
                                                    <?php else: ?>
                                                        <div class="w-full h-32 bg-gradient-to-br from-orange-400 to-orange-600 flex items-center justify-center">
                                                            <div class="text-center text-white">
                                                                <svg class="w-8 h-8 mx-auto mb-1" fill="none" stroke="currentColor" stroke-width="1" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                                                </svg>
                                                                <p class="text-xs">Coming Soon</p>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>

                                                    <div class="p-4">
                                                        <h4 class="text-sm font-semibold text-white mb-2 line-clamp-2">
                                                            <?php
                                                            if ($package['tour_package_id']) {
                                                                // Smart formatting based on package type and duration
                                                                $packageName = $package['name'];
                                                                $tourType = $package['type_name'] ?: 'Tour';
                                                                $duration = $package['duration'];

                                                                // Check if tour type already implies duration
                                                                $lowerTourType = strtolower($tourType);
                                                                $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                                                                    strpos($lowerTourType, 'overnight') !== false ||
                                                                                    strpos($lowerTourType, 'night') !== false);

                                                                if ($typeImpliesDuration) {
                                                                    // Format: Tour Type + Package Name
                                                                    $formattedName = $tourType . ' ' . $packageName;
                                                                } elseif (!empty($duration)) {
                                                                    // Format: Duration + Package Name + Tour Type
                                                                    $formattedName = $duration . ' ' . $packageName . ' ' . $tourType;
                                                                } else {
                                                                    // Format: Package Name + Tour Type
                                                                    $formattedName = $packageName . ' ' . $tourType;
                                                                }

                                                                echo htmlspecialchars($formattedName);
                                                            } else {
                                                                echo htmlspecialchars($package['name']);
                                                            }
                                                            ?>
                                                        </h4>
                                                        <p class="text-gray-200 mb-3 text-xs line-clamp-2">
                                                            <?php
                                                            $description = $package['description'] ?: 'Discover this amazing tour package with unique experiences and unforgettable memories.';
                                                            echo htmlspecialchars(substr($description, 0, 100)) . (strlen($description) > 100 ? '...' : '');
                                                            ?>
                                                        </p>
                                                        <div class="flex justify-between items-center">
                                                            <span class="text-orange-400 font-bold text-sm">
                                                                $<?php echo number_format($package['price']); ?>
                                                            </span>
                                                            <?php if ($package['tour_package_id']): ?>
                                                                <a href="package-details.php?id=<?php echo $package['tour_package_id']; ?>"
                                                                   class="text-xs bg-orange-500 hover:bg-orange-600 text-white px-3 py-1 rounded-full font-medium transition duration-300">
                                                                    View Details
                                                                </a>
                                                            <?php else: ?>
                                                                <span class="text-xs bg-gray-600 text-white px-3 py-1 rounded-full font-medium">
                                                                    Coming Soon
                                                                </span>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>

                                    <!-- Pagination Bullets -->
                                    <div class="swiper-pagination mt-4"></div>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-8">
                                    <i class="fas fa-suitcase text-gray-300 text-3xl mb-3"></i>
                                    <p class="text-gray-500">No featured tours available</p>
                                </div>
                            <?php endif; ?>

                            <div class="mt-6">
                                <a href="tours.php"
                                   class="block w-full text-center bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white py-3 rounded-lg font-semibold transition duration-300">
                                    View All Tours
                                </a>
                            </div>
                        </div>

                        <!-- Why Choose Us Card -->
                        <div class="form-section rounded-2xl shadow-lg p-6">
                            <h3 class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                                <i class="fas fa-award text-orange-500 mr-3"></i>
                                Why Choose Us?
                            </h3>
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Expert Local Guides</h4>
                                        <p class="text-gray-600 text-xs">Professional guides with deep local knowledge</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">24/7 Support</h4>
                                        <p class="text-gray-600 text-xs">Round-the-clock assistance during your trip</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Best Price Guarantee</h4>
                                        <p class="text-gray-600 text-xs">Competitive pricing with no hidden costs</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                                        <i class="fas fa-check text-white text-xs"></i>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-gray-900 text-sm">Customized Itineraries</h4>
                                        <p class="text-gray-600 text-xs">Tailored experiences to match your preferences</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Swiper JS -->
    <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script>

    <script>
        // Date picker functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Featured Tours Swiper
            const featuredToursSwiper = new Swiper('.featured-tours-swiper', {
                loop: true,
                slidesPerView: 1,
                spaceBetween: 0,
                autoplay: {
                    delay: 4000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                    dynamicBullets: true,
                },
                effect: 'slide',
                speed: 600,
            });

            // Initialize date picker
            const dateInput = document.getElementById('travel_date');

            if (dateInput) {
                // Initialize Flatpickr with consistent mobile/desktop behavior
                const fp = flatpickr(dateInput, {
                    dateFormat: "d/m/Y",
                    allowInput: false,
                    clickOpens: true,
                    disableMobile: true, // Force Flatpickr calendar on mobile instead of native
                    onOpen: function() {
                        if (!('ontouchstart' in window)) {
                            dateInput.focus();
                        }
                    },
                    onReady: function() {
                        // Ensure placeholder is visible when no date is selected
                        if (!dateInput.value) {
                            dateInput.placeholder = "dd/mm/yyyy";
                        }
                    },
                    onChange: function(selectedDates, dateStr) {
                        // Update the input value with proper format
                        if (dateStr) {
                            dateInput.value = dateStr;
                        }
                    }
                });

                // Make the entire wrapper clickable
                const dateWrapper = document.querySelector('.datepicker-wrapper');
                if (dateWrapper) {
                    dateWrapper.addEventListener('click', function(e) {
                        if (fp) {
                            fp.open();
                        }
                    });
                }

                // Ensure the orange calendar icon is always visible and clickable
                const dateIcon = document.querySelector('.fa-calendar-alt');
                if (dateIcon) {
                    // Make sure the icon is visible and styled correctly
                    dateIcon.style.display = 'block';
                    dateIcon.style.color = '#f97316';
                    dateIcon.style.pointerEvents = 'auto';

                    dateIcon.addEventListener('click', function(e) {
                        e.stopPropagation();
                        if (fp) {
                            fp.open();
                        }
                    });
                }

                // Mobile-specific enhancements
                if ('ontouchstart' in window) {
                    // Prevent mobile keyboard and native date picker
                    dateInput.addEventListener('focus', function(e) {
                        e.preventDefault();
                        this.blur();
                        if (fp) {
                            fp.open();
                        }
                    });

                    // Prevent any native date picker behavior
                    dateInput.addEventListener('click', function(e) {
                        e.preventDefault();
                        if (fp) {
                            fp.open();
                        }
                    });

                    // Add readonly attribute on mobile to prevent keyboard
                    dateInput.setAttribute('readonly', 'readonly');
                }
            }

            // Counter function for travelers
            window.changeCounter = function(fieldName, change) {
                const input = document.getElementById(fieldName);
                const currentValue = parseInt(input.value);
                const newValue = Math.max(fieldName === 'adults' ? 1 : 0, Math.min(10, currentValue + change));
                input.value = newValue;
            };

            window.changeCounter = function(fieldName, change) {
                const input = document.getElementById(fieldName);
                const currentValue = parseInt(input.value);
                const newValue = Math.max(fieldName === 'adults' ? 1 : 0, Math.min(10, currentValue + change));
                input.value = newValue;
            };

            // Initialize package selector functionality
            initializePackageSelector();

            function initializePackageSelector() {
                const searchInput = document.getElementById('package-search');
                const dropdown = document.getElementById('package-dropdown');
                const selectedPackagesList = document.getElementById('selected-packages-list');
                const packageInputsContainer = document.getElementById('package-inputs');
                const packageOptions = document.querySelectorAll('.package-option');
                const dropdownArrow = document.getElementById('dropdown-arrow');
                const selectorContainer = document.querySelector('.package-selector-container');

                let selectedPackages = [];

                // Initialize with preselected package if any
                <?php if ($preselectedPackage): ?>
                    <?php
                    // Format the preselected package name the same way as in the dropdown
                    $packageName = $preselectedPackage['name'];
                    $tourType = $preselectedPackage['type_name'] ?: 'Tour';
                    $duration = $preselectedPackage['duration'];

                    $lowerTourType = strtolower($tourType);
                    $typeImpliesDuration = (strpos($lowerTourType, 'day') !== false ||
                                        strpos($lowerTourType, 'overnight') !== false ||
                                        strpos($lowerTourType, 'night') !== false);

                    if ($typeImpliesDuration) {
                        $formattedPreselectedName = $tourType . ' ' . $packageName;
                    } elseif (!empty($duration)) {
                        $formattedPreselectedName = $duration . ' ' . $packageName . ' ' . $tourType;
                    } else {
                        $formattedPreselectedName = $packageName . ' ' . $tourType;
                    }
                    ?>
                    const preselectedPackage = {
                        id: <?php echo $preselectedPackageId; ?>,
                        name: "<?php echo addslashes($formattedPreselectedName); ?>",
                        price: <?php echo $preselectedPackage['price']; ?>,
                        duration: "<?php echo addslashes($preselectedPackage['duration'] ?? ''); ?>"
                    };
                    selectedPackages.push(preselectedPackage);
                    updateSelectedPackagesDisplay();
                    updateHiddenInputs();
                <?php endif; ?>

                // Show/hide dropdown on click
                searchInput.addEventListener('click', function() {
                    const isHidden = dropdown.classList.contains('hidden');
                    if (isHidden) {
                        dropdown.classList.remove('hidden');
                        selectorContainer.classList.add('dropdown-open');
                    } else {
                        dropdown.classList.add('hidden');
                        selectorContainer.classList.remove('dropdown-open');
                    }
                });

                // Hide dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!e.target.closest('.package-selector-container')) {
                        dropdown.classList.add('hidden');
                        selectorContainer.classList.remove('dropdown-open');
                    }
                });

                // Package selection
                packageOptions.forEach(option => {
                    option.addEventListener('click', function() {
                        const packageData = {
                            id: parseInt(this.dataset.packageId),
                            name: this.dataset.packageName,
                            price: parseFloat(this.dataset.packagePrice),
                            duration: this.dataset.packageDuration
                        };

                        // Check if package is already selected
                        if (!selectedPackages.find(pkg => pkg.id === packageData.id)) {
                            selectedPackages.push(packageData);
                            updateSelectedPackagesDisplay();
                            updateHiddenInputs();
                        }

                        // Hide dropdown after selection
                        dropdown.classList.add('hidden');
                        selectorContainer.classList.remove('dropdown-open');
                    });
                });

                function updateSelectedPackagesDisplay() {
                    if (selectedPackages.length === 0) {
                        selectedPackagesList.innerHTML = '<div class="empty-selection-message">No packages selected yet. Click above to select packages.</div>';
                        return;
                    }

                    selectedPackagesList.innerHTML = selectedPackages.map(pkg => `
                        <div class="selected-package-item" data-package-id="${pkg.id}">
                            <div class="flex items-center flex-1">
                                <i class="fas fa-suitcase text-orange-500 mr-2 text-sm"></i>
                                <div class="flex-1">
                                    <div class="text-sm font-medium text-gray-900">${pkg.name}</div>
                                </div>
                                <div class="text-orange-600 font-semibold mr-3 text-sm">
                                    $${pkg.price.toLocaleString()}
                                </div>
                            </div>
                            <button type="button" class="remove-package-btn" onclick="removePackage(${pkg.id})">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `).join('');
                }

                function updateHiddenInputs() {
                    packageInputsContainer.innerHTML = selectedPackages.map(pkg =>
                        `<input type="hidden" name="tour_packages[]" value="${pkg.id}">`
                    ).join('');
                }

                // Make removePackage function global
                window.removePackage = function(packageId) {
                    selectedPackages = selectedPackages.filter(pkg => pkg.id !== packageId);
                    updateSelectedPackagesDisplay();
                    updateHiddenInputs();
                };
            }

            // Activities checkbox handling
            const checkboxItems = document.querySelectorAll('.checkbox-item');

            checkboxItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    // Prevent double-triggering if clicking on the hidden checkbox
                    e.preventDefault();

                    const checkbox = this.querySelector('input[type="checkbox"]');
                    const isCurrentlyChecked = checkbox.checked;

                    // Toggle checkbox state
                    checkbox.checked = !isCurrentlyChecked;

                    // Force immediate visual update
                    updateItemVisualState(this, checkbox.checked);
                });

                // Handle keyboard accessibility
                item.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });

                // Handle focus and blur for better visual feedback
                item.addEventListener('focus', function() {
                    if (!this.classList.contains('selected') && !this.classList.contains('deselected')) {
                        this.style.borderColor = '#f97316';
                        this.style.boxShadow = '0 0 0 3px rgba(249, 115, 22, 0.1)';
                    }
                });

                item.addEventListener('blur', function() {
                    if (!this.classList.contains('selected')) {
                        this.style.borderColor = '';
                        this.style.boxShadow = '';
                        // Force remove any lingering focus styles
                        this.classList.remove('deselected');
                    }
                });

                // Make items focusable for accessibility
                item.setAttribute('tabindex', '0');
                item.setAttribute('role', 'checkbox');

                // Set initial ARIA state
                const checkbox = item.querySelector('input[type="checkbox"]');
                item.setAttribute('aria-checked', checkbox.checked);

                // Update ARIA state when checkbox changes
                const observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'checked') {
                            item.setAttribute('aria-checked', checkbox.checked);
                        }
                    });
                });
                observer.observe(checkbox, { attributes: true });
            });

            // Helper function to update visual state immediately
            function updateItemVisualState(item, isSelected) {
                if (isSelected) {
                    item.classList.add('selected', 'active');
                    item.classList.remove('deselected');
                    // Reset CSS custom properties
                    item.style.setProperty('--border-color', '');
                    item.style.setProperty('--bg-color', '');
                    item.style.setProperty('--box-shadow', '');
                } else {
                    item.classList.remove('selected', 'active');
                    item.classList.add('deselected');

                    // Force immediate visual change using CSS custom properties
                    item.style.setProperty('--border-color', '#d1d5db');
                    item.style.setProperty('--bg-color', '#ffffff');
                    item.style.setProperty('--box-shadow', 'none');

                    // Force the element to lose focus immediately
                    if (document.activeElement === item) {
                        item.blur();
                    }

                    // Clean up after a short delay
                    setTimeout(() => {
                        if (!item.classList.contains('selected')) {
                            item.classList.remove('deselected');
                            item.style.setProperty('--border-color', '');
                            item.style.setProperty('--bg-color', '');
                            item.style.setProperty('--box-shadow', '');
                        }
                    }, 150);
                }

                // Update ARIA state
                item.setAttribute('aria-checked', isSelected);
            }

            // Phone input initialization
            const phoneInput = document.querySelector("#phone");
            const countryInput = document.querySelector("#country");

            if (phoneInput) {
                const iti = window.intlTelInput(phoneInput, {
                    initialCountry: "ke",
                    preferredCountries: ["ke", "ug", "tz", "za", "ng", "gh", "et", "rw"],
                    utilsScript: "https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js",
                    separateDialCode: true,
                    autoHideDialCode: false,
                    nationalMode: false,
                    formatOnDisplay: true,
                    placeholderNumberType: "MOBILE"
                });

                phoneInput.addEventListener('countrychange', function() {
                    const countryData = iti.getSelectedCountryData();
                    if (countryInput && countryData) {
                        countryInput.value = countryData.name;
                    }
                });

                const initialCountryData = iti.getSelectedCountryData();
                if (countryInput && initialCountryData) {
                    countryInput.value = initialCountryData.name;
                }

                const form = phoneInput.closest('form');
                if (form) {
                    form.addEventListener('submit', function(e) {
                        // Only validate if phone number is not empty
                        if (phoneInput.value.trim() !== '' && !iti.isValidNumber()) {
                            e.preventDefault();
                            phoneInput.classList.add('border-red-500');
                            phoneInput.focus();
                            phoneInput.addEventListener('input', function() {
                                phoneInput.classList.remove('border-red-500');
                            }, { once: true });
                            alert('Please enter a valid phone number or leave it empty.');
                            return false;
                        }
                        // Set the formatted number if valid
                        if (phoneInput.value.trim() !== '' && iti.isValidNumber()) {
                            phoneInput.value = iti.getNumber();
                        }
                    });
                }
            }
        });
    </script>

    <?php include 'footer.php'; ?>
</body>
</html>