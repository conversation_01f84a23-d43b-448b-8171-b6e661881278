# Meleva Tours and Travel - Admin Dashboard Documentation

## Overview
The admin dashboard is a comprehensive content management system built for Meleva Tours and Travel. It provides complete control over destinations, tour packages, images, quotes, payments, and user management.

## Access & Security

### Login System
- **URL**: `/admin-dashboard/`
- **Authentication**: Username/password with session management
- **Security Features**:
  - 30-minute session timeout
  - CSRF protection on all forms
  - Password reset functionality
  - Login attempt limiting
  - Secure headers (XSS, CSRF, Content Security Policy)

### User Roles
- **Admin**: Full system access
- **Manager**: Content management access
- **Editor**: Limited content editing

## Dashboard Structure

### File Organization
```
/admin-dashboard/
├── index.php              # Dashboard home
├── login.php              # Authentication
├── logout.php             # Session termination
├── destinations.php       # Destination management
├── packages.php           # Tour package management
├── package-types.php      # Package type management
├── images.php             # Gallery management
├── messages.php           # Contact form messages
├── quotes.php             # Quote management
├── payments.php           # Payment tracking
├── reports.php            # Analytics and reports
├── users.php              # User management
├── sidebar.php            # Navigation component
├── database.sql           # Database schema
├── setup.php              # Initial setup
├── classes/               # Core classes
├── config/                # Configuration files
├── includes/              # Shared components
├── api/                   # API endpoints
├── uploads/               # File uploads
└── vendor/                # Composer dependencies
```

## Core Functionality

### 1. Destinations Management (destinations.php)
**Features:**
- Add/edit/delete destinations
- Multiple image upload with compression
- Display image selection
- Status management (active/inactive)
- Bulk operations

**Image Handling:**
- Automatic compression to under 5MB
- WebP conversion support
- Thumbnail generation
- Drag-and-drop upload interface
- Preview with remove functionality

**Form Fields:**
- Destination name
- Description
- Location details
- Status toggle
- Image gallery

### 2. Tour Packages Management (packages.php)
**Features:**
- Complete package lifecycle management
- Package type association
- Destination linking
- Pricing management
- Featured package selection (via edit only)

**Package Creation:**
- New packages default to "Regular" status
- Admin can mark as "Featured" via edit function
- Multiple image support
- Display image selection
- Comprehensive form validation

**Form Fields:**
- Package name
- Package type selection
- Destination association
- Description
- Price (USD)
- Duration
- Image gallery
- Status management

### 3. Package Types Management (package-types.php)
**Features:**
- Create/edit/delete package categories
- Type descriptions
- Package count tracking
- Bulk operations

**Default Types:**
- Day Tours
- Group Tours
- Private Tours
- Luxury Tours

### 4. Image Gallery Management (images.php)
**Features:**
- Main gallery image management
- Bulk upload functionality
- Image categorization
- Title and description editing
- Delete functionality

**Image Processing:**
- Automatic compression
- Format optimization
- Thumbnail generation
- Alt text management

### 5. Messages Management (messages.php)
**Features:**
- Contact form submissions
- Email conversation tracking
- Response functionality
- Status management (read/unread)
- Archive functionality

**Email Integration:**
- Automatic email notifications
- Conversation threading
- Reply tracking
- Delivery status monitoring

### 6. Quotes Management (quotes.php)
**Features:**
- Quote request processing
- Status management (pending/accepted/rejected)
- Payment link generation
- Customer communication
- Quote history tracking

**Quote Workflow:**
1. Customer submits quote request
2. Admin reviews and responds
3. Payment link generation
4. Payment processing
5. Booking confirmation

**Status Colors:**
- Blue: Accepted
- Yellow/Orange: Partially paid
- Green: Fully paid
- Red: Rejected/failed

### 7. Payments Management (payments.php)
**Features:**
- Payment history tracking
- Status monitoring
- Revenue analytics
- Payment method tracking
- Refund management

**Payment Statistics:**
- Total revenue
- Failed payments
- Pending amounts
- Payment trends

### 8. Reports & Analytics (reports.php)
**Features:**
- Revenue reports
- Booking analytics
- Customer insights
- Export functionality (PDF/Excel)
- Date range filtering

### 9. User Management (users.php)
**Features:**
- Admin user creation
- Role assignment
- Password management
- Activity tracking
- Account status control

## Technical Features

### Image Management System
**Compression Algorithm:**
- Progressive quality reduction
- Dimension optimization
- Format conversion (WebP support)
- 5MB maximum file size guarantee
- Batch processing capability

**Upload Process:**
1. File validation (type, size, security)
2. Virus scanning (if available)
3. Compression optimization
4. Thumbnail generation
5. Database storage
6. Preview generation

### Form Handling
**Validation:**
- Client-side validation (JavaScript)
- Server-side validation (PHP)
- CSRF token verification
- Input sanitization
- File upload security

**User Experience:**
- Real-time validation feedback
- Progress indicators for uploads
- Auto-save functionality
- Error recovery
- Success confirmations

### Database Operations
**Models:**
- Destination model
- TourPackage model
- Image model
- Message model
- Quote model
- Payment model
- User model

**Features:**
- Prepared statements (SQL injection prevention)
- Transaction support
- Error logging
- Connection pooling
- Query optimization

### Security Implementation
**Headers:**
- Content Security Policy
- X-Frame-Options: DENY
- X-XSS-Protection
- X-Content-Type-Options
- Strict-Transport-Security

**File Security:**
- Upload directory protection
- File type validation
- Size limitations
- Executable file prevention
- Directory traversal protection

## User Interface

### Design System
- **Framework**: Tailwind CSS
- **Icons**: Font Awesome
- **Components**: Custom admin components
- **Responsive**: Mobile-friendly admin interface
- **Theme**: Professional admin theme

### Navigation
- **Sidebar**: Collapsible navigation menu
- **Breadcrumbs**: Page hierarchy indication
- **Active States**: Current page highlighting
- **Quick Actions**: Frequently used functions

### Tables & Lists
- **Pagination**: 30 items per page
- **Sorting**: Column-based sorting
- **Filtering**: Search and filter options
- **Bulk Actions**: Multi-item operations
- **Status Indicators**: Visual status representation

### Forms
- **Validation**: Real-time feedback
- **File Uploads**: Drag-and-drop interface
- **Rich Text**: WYSIWYG editors where needed
- **Auto-complete**: Smart form completion
- **Save States**: Draft and publish options

## Configuration

### Environment Setup
**Requirements:**
- PHP 7.4+
- MySQL 5.7+
- Apache with mod_rewrite
- Composer for dependencies
- Write permissions for uploads

**Configuration Files:**
- `config/config.php`: Database and app settings
- `config/pesapal_config.php`: Payment configuration
- `.htaccess`: Security and routing rules

### Database Configuration
**Connection Settings:**
- Host: localhost
- Database: meleva_tours
- Charset: utf8mb4
- Timezone: UTC

**Security Settings:**
- Session timeout: 30 minutes
- Max login attempts: 5
- Lockout duration: 15 minutes
- CSRF token lifetime: 1 hour

## Maintenance & Monitoring

### Logging
- **Error Logs**: PHP error logging
- **Activity Logs**: User action tracking
- **Security Logs**: Authentication attempts
- **Performance Logs**: Query performance

### Backup Procedures
- **Database**: Regular MySQL dumps
- **Files**: Upload directory backups
- **Configuration**: Settings backup
- **Automated**: Scheduled backup scripts

### Performance Optimization
- **Query Optimization**: Indexed database queries
- **Image Optimization**: Compressed uploads
- **Caching**: Browser and server caching
- **CDN Ready**: Asset delivery optimization

### Updates & Patches
- **Security Updates**: Regular security patches
- **Feature Updates**: New functionality releases
- **Bug Fixes**: Issue resolution
- **Database Migrations**: Schema updates

## Troubleshooting

### Common Issues
1. **Upload Failures**: Check file permissions and size limits
2. **Login Issues**: Verify session configuration
3. **Image Problems**: Check compression settings
4. **Email Issues**: Verify SMTP configuration
5. **Payment Errors**: Check Pesapal configuration

### Debug Mode
- Enable error reporting in development
- Check PHP error logs
- Monitor database query logs
- Use browser developer tools

### Support
- Check error logs first
- Verify configuration settings
- Test with minimal data
- Contact system administrator
