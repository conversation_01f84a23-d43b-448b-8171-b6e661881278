# IMAP Setup Guide for Meleva Tours

## 🎯 **Quick Answer for Your Hosting**

Since your hosting recommends IMAP and POP3, here's what you need to know:

### **Use IMAP (Recommended)**
- ✅ **Better for your business** - You can access emails in both your admin system AND your email client
- ✅ **Emails stay on server** - Won't disappear from your email client
- ✅ **Real-time sync** - Changes sync everywhere

### **Your Current Settings (Likely Correct)**
```
Protocol: IMAP
Host: melevatours.co.ke (or mail.melevatours.co.ke)
Port: 993
Encryption: SSL
```

## 🔧 **Configuration Steps**

### **Step 1: Find Your Exact Settings**
Your hosting provider should have given you these details. Common variations:

**Option A (Most Common):**
- Host: `mail.melevatours.co.ke`
- Port: `993` (IMAP SSL) or `143` (IMAP non-SSL)

**Option B (Alternative):**
- Host: `melevatours.co.ke`
- Port: `993` (IMAP SSL)

**Option C (Some Hosts):**
- Host: `imap.melevatours.co.ke`
- Port: `993` (IMAP SSL)

### **Step 2: Test Your Settings**
1. Go to `admin-dashboard/email-config-wizard.php`
2. Enter your email and password
3. Click "Test Connection"
4. If it works, save the configuration

### **Step 3: Common Issues & Solutions**

#### **Issue: "IMAP extension not found"**
**For XAMPP:**
1. Open `php.ini` file (usually in `C:\xampp\php\php.ini`)
2. Find the line `;extension=imap`
3. Remove the semicolon: `extension=imap`
4. Restart Apache

**For Linux/Ubuntu:**
```bash
sudo apt-get install php-imap
sudo systemctl restart apache2
```

**For cPanel/Shared Hosting:**
- Contact your hosting provider to enable IMAP extension

#### **Issue: "Connection failed"**
Try these settings in order:

1. **First try:**
   - Host: `mail.melevatours.co.ke`
   - Port: `993`
   - Encryption: `SSL`

2. **If that fails:**
   - Host: `melevatours.co.ke`
   - Port: `993`
   - Encryption: `SSL`

3. **If still failing:**
   - Host: `mail.melevatours.co.ke`
   - Port: `143`
   - Encryption: `TLS` or `None`

#### **Issue: "Authentication failed"**
- Double-check your email password
- Some hosts require "app-specific passwords"
- Try logging into webmail first to verify credentials

## 📧 **Email Account Setup**

You'll need to configure both email accounts:

### **Contact Email (<EMAIL>)**
```
Email: <EMAIL>
Password: [your email password]
Category: contact
```

### **Booking Email (<EMAIL>)**
```
Email: <EMAIL>
Password: [your email password]
Category: quote
```

**Note:** Both accounts might use the same password, or they might be different. Check with your hosting provider.

## 🚀 **Quick Setup Process**

### **Method 1: Use the Configuration Wizard (Recommended)**
1. Go to `admin-dashboard/email-config-wizard.php`
2. Select your hosting type (probably "Custom/Other Provider")
3. Enter your server details
4. Test the connection
5. If successful, save the configuration

### **Method 2: Manual Configuration**
1. Edit `admin-dashboard/email-fetcher.php`
2. Update the config section:
```php
'host' => 'mail.melevatours.co.ke', // or melevatours.co.ke
'port' => 993,
'encryption' => 'ssl',
'accounts' => [
    [
        'email' => '<EMAIL>',
        'password' => 'your_actual_password',
        'category' => 'contact'
    ],
    [
        'email' => '<EMAIL>',
        'password' => 'your_actual_password',
        'category' => 'quote'
    ]
]
```

## 🔍 **How to Find Your Settings**

### **Check Your Email Client**
If you already have your email set up in Outlook, Thunderbird, or your phone:
1. Go to account settings
2. Look for "Incoming mail server" settings
3. Copy those exact settings

### **Contact Your Hosting Provider**
Ask them for:
- IMAP server hostname
- IMAP port number
- Encryption type (SSL/TLS)
- Whether IMAP is enabled for your accounts

### **Check cPanel (if you have it)**
1. Login to cPanel
2. Go to "Email Accounts"
3. Click "Configure Mail Client" next to your email
4. Look for IMAP settings

## ⚡ **Testing Your Setup**

### **Quick Test**
1. Go to `admin-dashboard/email-config-wizard.php`
2. Enter one email account details
3. Click "Test Connection"
4. Should show "Connection successful" with message count

### **Full Test**
1. Send a test <NAME_EMAIL>
2. Run `admin-dashboard/email-fetcher.php`
3. Check if the email appears in your admin messages

## 🛠️ **Troubleshooting Checklist**

- [ ] IMAP extension is installed (`php -m | grep imap`)
- [ ] Email credentials are correct
- [ ] Server hostname is correct
- [ ] Port number is correct (usually 993 for SSL)
- [ ] Encryption is set correctly (usually SSL)
- [ ] Firewall allows outgoing connections on the port
- [ ] Email accounts exist and are active

## 📞 **Need Help?**

### **Contact Your Hosting Provider**
Ask them:
1. "What are the IMAP settings for my email accounts?"
2. "Is IMAP enabled for my domain?"
3. "Do I need special passwords for IMAP access?"

### **Common Hosting Provider Settings**

**cPanel/WHM Hosting:**
- Host: `mail.yourdomain.com`
- Port: `993` (SSL) or `143` (non-SSL)

**Plesk Hosting:**
- Host: `mail.yourdomain.com` or `yourdomain.com`
- Port: `993` (SSL)

**Google Workspace:**
- Host: `imap.gmail.com`
- Port: `993`
- Encryption: `SSL`

## 🎉 **Once It's Working**

1. **Test manually:** Run the email fetcher to see if it works
2. **Set up automation:** Use the cron job setup script
3. **Monitor:** Check logs to ensure it's working properly
4. **Enjoy:** No more missing customer emails!

The key is getting the right server settings from your hosting provider. Once you have those, the rest is straightforward!
