<?php
// Define admin access constant
define('ADMIN_ACCESS', true);

// Include security middleware
require_once 'includes/security_middleware.php';
require_once 'classes/models.php';
require_once 'classes/additional_models.php';
require_once 'classes/booking_models.php';

// Get dashboard statistics
$reportModel = new Report();
$stats = $reportModel->generateDashboardStats();
$currentUser = Auth::getCurrentUser();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar-transition {
            transition: all 0.3s ease;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .sidebar-active {
            background-color: rgba(249, 115, 22, 0.1);
            border-right: 3px solid #f97316;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #ffffff, #f8fafc);
            border: 1px solid #e2e8f0;
        }
        
        .stat-icon {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        /* Mobile and Tablet Responsive Styles */
        @media (max-width: 1023px) {
            #sidebar {
                transform: translateX(-100%);
            }
            
            #sidebar.sidebar-open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 40;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }
            
            .sidebar-overlay.active {
                opacity: 1;
                visibility: visible;
            }
        }
        
        /* Hide toggle button on desktop */
        @media (min-width: 1024px) {
            #sidebar-toggle {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Sidebar Overlay for Mobile -->
    <div id="sidebar-overlay" class="sidebar-overlay"></div>
    
    <?php include 'sidebar.php'; ?>
    
    <!-- Main Content -->
    <div class="lg:ml-64 main-content min-h-screen">
        <!-- Header -->
        <div class="bg-white shadow-sm border-b border-gray-200 md:h-24">
            <div class="flex items-center justify-center px-6 py-4 h-full relative">
                <button id="sidebar-toggle" class="lg:hidden text-gray-600 hover:text-gray-800 absolute left-6">
                    <i class="fas fa-bars text-xl"></i>
                </button>
                <h1 class="text-2xl font-bold text-gray-900 text-center">Dashboard</h1>
            </div>
        </div>
        <!-- Content Area -->
        <div class="p-6">
            <div class="flex items-center justify-between mb-6">
                <div>
                    <p class="text-sm text-gray-500">Welcome back, <?php echo htmlspecialchars($currentUser["username"]); ?>!</p>
                </div>
                <div class="text-sm text-gray-500">
                    <?php echo date("l, F j, Y"); ?>
                </div>
            </div>
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Quotes Card -->
                <div class="stat-card rounded-lg p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Quotes</p>
                            <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_quotes'] ?? 0; ?></p>
                            <?php if (($stats['pending_quotes'] ?? 0) > 0): ?>
                                <p class="text-sm text-orange-600"><?php echo $stats['pending_quotes']; ?> pending</p>
                            <?php endif; ?>
                        </div>
                        <div class="stat-icon w-12 h-12 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-invoice text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="quotes.php" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
                            Manage quotes →
                        </a>
                    </div>
                </div>

                <!-- Bookings Card -->
                <div class="stat-card rounded-lg p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Bookings</p>
                            <p class="text-3xl font-bold text-gray-900"><?php echo $stats['total_bookings'] ?? 0; ?></p>
                            <?php if (($stats['active_bookings'] ?? 0) > 0): ?>
                                <p class="text-sm text-green-600"><?php echo $stats['active_bookings']; ?> active</p>
                            <?php endif; ?>
                        </div>
                        <div class="stat-icon w-12 h-12 rounded-lg flex items-center justify-center">
                            <i class="fas fa-calendar-check text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="bookings.php" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
                            Manage bookings →
                        </a>
                    </div>
                </div>

                <!-- Revenue Card -->
                <div class="stat-card rounded-lg p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Revenue</p>
                            <p class="text-3xl font-bold text-gray-900">$<?php echo number_format($stats['total_revenue'] ?? 0, 0); ?></p>
                            <?php if (($stats['pending_payments'] ?? 0) > 0): ?>
                                <p class="text-sm text-yellow-600"><?php echo $stats['pending_payments']; ?> pending</p>
                            <?php endif; ?>
                        </div>
                        <div class="stat-icon w-12 h-12 rounded-lg flex items-center justify-center">
                            <i class="fas fa-dollar-sign text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="payments.php" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
                            View payments →
                        </a>
                    </div>
                </div>

                <!-- Messages Card -->
                <div class="stat-card rounded-lg p-6 card-hover">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Messages</p>
                            <p class="text-3xl font-bold text-gray-900"><?php echo $stats['messages']; ?></p>
                            <?php if ($stats['unread_messages'] > 0): ?>
                                <p class="text-sm text-red-600"><?php echo $stats['unread_messages']; ?> unread</p>
                            <?php endif; ?>
                        </div>
                        <div class="stat-icon w-12 h-12 rounded-lg flex items-center justify-center">
                            <i class="fas fa-envelope text-white text-xl"></i>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="messages.php" class="text-orange-600 hover:text-orange-700 text-sm font-medium">
                            View messages →
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity and Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Recent Activity -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h3>
                    <div class="space-y-4">
                        <?php if (!empty($stats['recent_activity'])): ?>
                            <?php foreach (array_slice($stats['recent_activity'], 0, 5) as $activity): ?>
                                <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                    <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-<?php echo $activity['type'] === 'destination' ? 'map-marker-alt' : ($activity['type'] === 'package' ? 'suitcase' : 'envelope'); ?> text-orange-600 text-sm"></i>
                                    </div>
                                    <div class="flex-1">
                                        <p class="text-sm font-medium text-gray-800"><?php echo htmlspecialchars($activity['title']); ?></p>
                                        <p class="text-xs text-gray-500"><?php echo Utils::formatDate($activity['date']); ?></p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php else: ?>
                            <p class="text-gray-500 text-center py-4">No recent activity</p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-2 gap-4">
                        <a href="quotes.php" class="flex flex-col items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                            <i class="fas fa-file-invoice text-orange-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-gray-800">Manage Quotes</span>
                        </a>

                        <a href="bookings.php" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                            <i class="fas fa-calendar-check text-blue-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-gray-800">View Bookings</span>
                        </a>

                        <a href="payments.php" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                            <i class="fas fa-credit-card text-green-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-gray-800">Track Payments</span>
                        </a>

                        <a href="reports.php" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                            <i class="fas fa-chart-line text-purple-600 text-2xl mb-2"></i>
                            <span class="text-sm font-medium text-gray-800">Financial Reports</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Mobile sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            
            // Toggle sidebar on mobile
            function toggleSidebar() {
                sidebar.classList.toggle('sidebar-open');
                sidebarOverlay.classList.toggle('active');
                document.body.classList.toggle('overflow-hidden');
            }
            
            // Close sidebar
            function closeSidebar() {
                sidebar.classList.remove('sidebar-open');
                sidebarOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');
            }
            
            // Event listeners
            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', toggleSidebar);
            }
            
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', closeSidebar);
            }
            
            // Close sidebar when clicking on navigation links on mobile
            const navLinks = sidebar.querySelectorAll('nav a');
            navLinks.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth < 1024) {
                        closeSidebar();
                    }
                });
            });
            
            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth >= 1024) {
                    closeSidebar();
                }
            });
        });
    </script>

    <?php injectAutoLogoutScript(); ?>
</body>
</html>

