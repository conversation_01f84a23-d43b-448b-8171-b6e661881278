<?php
// Start output buffering to prevent header issues
ob_start();

require_once 'config/config.php';
require_once 'classes/models.php';

// Check if user is already logged in
Auth::startSession();
if (Auth::isLoggedIn()) {
    header('Location: index.php');
    exit;
}

$message = '';
$messageType = '';
$token = '';
$validToken = false;

// Check if token is provided
if (isset($_GET['token'])) {
    $token = Utils::sanitizeInput($_GET['token']);
    $userModel = new User();
    $resetData = $userModel->validatePasswordResetToken($token);
    
    if ($resetData) {
        $validToken = true;
    } else {
        $message = 'Invalid or expired reset token. Please request a new password reset.';
        $messageType = 'error';
    }
} else {
    $message = 'No reset token provided. Please check your email for the reset link.';
    $messageType = 'error';
}

// Handle password reset form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $validToken) {
    $newPassword = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];
    
    if (!empty($newPassword) && !empty($confirmPassword)) {
        if ($newPassword === $confirmPassword) {
            if (strlen($newPassword) >= 6) {
                $userModel = new User();
                $resetData = $userModel->validatePasswordResetToken($token);
                
                if ($resetData) {
                    // Hash the new password
                    $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
                    
                    // Update user password and mark token as used
                    if ($userModel->updatePassword($resetData['user_id'], $hashedPassword) && 
                        $userModel->markTokenAsUsed($token)) {
                        $message = 'Password has been successfully reset. You can now login with your new password.';
                        $messageType = 'success';
                        $validToken = false; // Hide the form
                    } else {
                        $message = 'Failed to update password. Please try again.';
                        $messageType = 'error';
                    }
                } else {
                    $message = 'Reset token has expired. Please request a new password reset.';
                    $messageType = 'error';
                    $validToken = false;
                }
            } else {
                $message = 'Password must be at least 6 characters long.';
                $messageType = 'error';
            }
        } else {
            $message = 'Passwords do not match.';
            $messageType = 'error';
        }
    } else {
        $message = 'Please fill in all fields.';
        $messageType = 'error';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Meleva Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.0/css/all.min.css">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #f97316, #ea580c);
        }
        
        .safari-pattern {
            background-image: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 48, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
        }
    </style>
</head>
<body class="min-h-screen safari-pattern">
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Logo and Title -->
            <div class="text-center">
                <div class="mx-auto w-20 h-20 lg:w-40 lg:h-40 flex items-center justify-center mb-4">
                    <img src="images/meleva-lg.png" alt="logo">
                </div>
                <h2 class="text-3xl font-bold text-gray-900">Reset Password</h2>
                <p class="mt-2 text-lg text-gray-600">Enter your new password</p>
            </div>
            
            <!-- Reset Password Form -->
            <div class="bg-white rounded-lg shadow-xl p-8">
                <?php if ($message): ?>
                    <div class="mb-4 <?php echo $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'; ?> border px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas <?php echo $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                            <span><?php echo htmlspecialchars($message); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if ($validToken): ?>
                <form method="POST" action="" class="space-y-6">
                    <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">
                    
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            New Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input 
                                type="password" 
                                id="password" 
                                name="password" 
                                required 
                                minlength="6"
                                class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Enter new password (min 6 characters)"
                            >
                            <button 
                                type="button" 
                                id="toggle-password"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                            Confirm New Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input 
                                type="password" 
                                id="confirm_password" 
                                name="confirm_password" 
                                required 
                                minlength="6"
                                class="block w-full pl-10 pr-10 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500"
                                placeholder="Confirm new password"
                            >
                            <button 
                                type="button" 
                                id="toggle-confirm-password"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center"
                            >
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div>
                        <button 
                            type="submit" 
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white gradient-bg hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200"
                        >
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-key text-orange-300 group-hover:text-orange-200"></i>
                            </span>
                            Reset Password
                        </button>
                    </div>
                </form>
                <?php endif; ?>
                
                <div class="mt-6 text-center">
                    <a href="login.php" class="text-sm text-orange-600 hover:text-orange-500 font-medium">
                        <i class="fas fa-arrow-left mr-1"></i>
                        Back to Login
                    </a>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    © 2025 Meleva Tours & Travel. All rights reserved.
                </p>
            </div>
        </div>
    </div>
    
    <script>
        // Toggle password visibility
        function togglePasswordVisibility(inputId, buttonId) {
            const passwordInput = document.getElementById(inputId);
            const button = document.getElementById(buttonId);
            const icon = button.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }
        
        document.getElementById('toggle-password')?.addEventListener('click', function() {
            togglePasswordVisibility('password', 'toggle-password');
        });
        
        document.getElementById('toggle-confirm-password')?.addEventListener('click', function() {
            togglePasswordVisibility('confirm_password', 'toggle-confirm-password');
        });
        
        // Form validation
        document.querySelector('form')?.addEventListener('submit', function(e) {
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            
            if (!password || !confirmPassword) {
                e.preventDefault();
                alert('Please fill in all fields');
                return;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                alert('Password must be at least 6 characters long');
                return;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                alert('Passwords do not match');
                return;
            }
        });
    </script>
</body>
</html>

